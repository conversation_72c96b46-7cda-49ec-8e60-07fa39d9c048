<!--
/**
 * 库存物品 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2025-04-17 07:09:25
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
	<title th:text="${lang.translate('库存物品')}">库存物品</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="opacity:0">

        <input name="id" id="id"  type="hidden"/>

         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-4337-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                <!-- select_box : 分类 ,  categoryId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('分类')}">分类</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="categoryId" input-type="select" th:data="${'/service-eam/eam-category/query-paged-list'}" extraParam="{}"></div>
                    </div>
                </div>
            
                <!-- select_box : 状态 ,  goodsStatus  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('状态')}">状态</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="goodsStatus" input-type="select" th:data="${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}" extraParam="{}"></div>
                    </div>
                </div>
            
                <!-- text_input : 计量单位 ,  unit -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('计量单位')}">计量单位</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="unit" id="unit" name="unit" th:placeholder="${ lang.translate('请输入'+'计量单位') }" type="text" class="layui-input"  />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                <!-- text_input : 物品编码 ,  code -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('物品编码')}">物品编码</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="code" id="code" name="code" th:placeholder="${ lang.translate('请输入'+'物品编码') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- text_input : 物品条码 ,  barCode -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('物品条码')}">物品条码</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="barCode" id="barCode" name="barCode" th:placeholder="${ lang.translate('请输入'+'物品条码') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- select_box : 父级物品 ,  parentGoodsStockIds  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('父级物品')}">父级物品</div></div>
                    <div class="layui-input-block ">
                        <div id="parentGoodsStockIds" input-type="select" th:data="${'/service-eam/eam-goods-stock/query-paged-list?ownerCode=goods'}" extraParam="{}"></div>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                <!-- select_box : 厂商 ,  manufacturerId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('厂商')}">厂商</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="manufacturerId" input-type="select" th:data="${'/service-eam/eam-manufacturer/query-list'}" extraParam="{}"></div>
                    </div>
                </div>
            
                <!-- select_box : 品牌 ,  brandId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('品牌')}">品牌</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="brandId" input-type="select" th:data="${'/service-eam/eam-brand/query-paged-list'}" extraParam="{}"></div>
                    </div>
                </div>
            
                <!-- select_box : 价值类型 ,  costType  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('价值类型')}">价值类型</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="costType" input-type="select" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_goods_cost_type'}" extraParam="{}"></div>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-0010-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column"  style="padding-top: 0px" >

                <!-- text_input : 物品名称 ,  name -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('物品名称')}">物品名称</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="name" id="name" name="name" th:placeholder="${ lang.translate('请输入'+'物品名称') }" type="text" class="layui-input"    lay-verify="|required"  />
                    </div>
                </div>
            
                <!-- text_input : 规格型号 ,  model -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('规格型号')}">规格型号</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="model" id="model" name="model" th:placeholder="${ lang.translate('请输入'+'规格型号') }" type="text" class="layui-input"    lay-verify="|required"  />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column"  style="padding-top: 0px" >

                <!-- number_input : 安全库存下限 ,  stockMin  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('安全库存下限')}">安全库存下限</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="stockMin" id="stockMin" name="stockMin" th:placeholder="${ lang.translate('请输入'+'安全库存下限') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="0" />
                    </div>
                </div>
            
                <!-- number_input : 安全库存上限 ,  stockMax  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('安全库存上限')}">安全库存上限</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="stockMax" id="stockMax" name="stockMax" th:placeholder="${ lang.translate('请输入'+'安全库存上限') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="0" />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column"  style="padding-top: 0px" >

                <!-- number_input : 安全库存 ,  stockSecurity  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('安全库存')}">安全库存</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="stockSecurity" id="stockSecurity" name="stockSecurity" th:placeholder="${ lang.translate('请输入'+'安全库存') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="0" />
                    </div>
                </div>
            
                <!-- number_input : 默认单价 ,  unitPrice  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('默认单价')}">默认单价</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="unitPrice" id="unitPrice" name="unitPrice" th:placeholder="${ lang.translate('请输入'+'默认单价') }" type="text" class="layui-input"    lay-verify="|required"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="2" />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->
        <fieldset class="layui-elem-field layui-field-title form-group-title" id="relGoods-fieldset">
        <legend>关联设备配件档案</legend>
        </fieldset>
        <div class="layui-row form-row" style="text-align: center;" id="relGoods-content">
            <div style="display: inline-block;padding-right: 8px;padding-left: 8px" class="layui-col-xs12">
            <iframe id="relGoods-iframe" js-fn="relGoods" class="form-iframe" frameborder="0" style="width: 100%"></iframe>
            </div>
        </div>
         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-2227-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column"  style="padding-top: 0px" >

                <!-- select_box : 仓库 ,  warehouseId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('仓库')}">仓库</div></div>
                    <div class="layui-input-block ">
                        <div id="warehouseId" input-type="select" th:data="${'/service-eam/eam-warehouse/query-paged-list'}" extraParam="{}"></div>
                    </div>
                </div>
            
                <!-- select_box : 库位 ,  positionId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('库位')}">库位</div></div>
                    <div class="layui-input-block ">
                        <div id="positionId" input-type="select" th:data="${'/service-eam/eam-position/query-paged-list'}" extraParam="{}"></div>
                    </div>
                </div>
            
                <!-- text_area : 备注 ,  notes  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('备注')}">备注</div></div>
                    <div class="layui-input-block ">
                        <textarea lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入'+'备注') }" class="layui-textarea" style="height: 120px" ></textarea>
                    </div>
                </div>
                            <!-- upload : 图片 ,  pictureId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('图片')}">图片</div></div>
                    <div class="layui-upload layui-input-block ">
                        <input input-type="upload" id="pictureId"  name="pictureId" lay-filter="pictureId" style="display: none">
                        <button type="button" class="layui-btn" id="pictureId-button" th:text="${lang.translate('选择图片')}">选择图片</button>
                        <div class="layui-upload-list" id="pictureId-file-list"></div>
                    </div>
                </div>
                            <!-- upload : 附件 ,  fileId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('附件')}">附件</div></div>
                    <div class="layui-upload layui-input-block ">
                        <input input-type="upload" id="fileId"  name="fileId" lay-filter="fileId" style="display: none">
                        <button type="button" class="layui-btn" id="fileId-button" th:text="${lang.translate('选择附件')}">选择附件</button>
                        <div class="layui-upload-list" id="fileId-file-list"></div>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 250px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消','','form.button')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('eam_goods_stock:create','eam_goods_stock:update','eam_goods_stock:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存','','form.button')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var SELECT_GOODSSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetHandleStatusEnum')}]];
    var VALIDATE_CONFIG={"unitPrice":{"labelInForm":"默认单价","inputType":"number_input","required":true},"goodsStatus":{"labelInForm":"状态","inputType":"select_box","required":true},"brandId":{"labelInForm":"品牌","inputType":"select_box","required":true},"costType":{"labelInForm":"价值类型","inputType":"select_box","required":true},"manufacturerId":{"labelInForm":"厂商","inputType":"select_box","required":true},"name":{"labelInForm":"物品名称","inputType":"text_input","required":true},"model":{"labelInForm":"规格型号","inputType":"text_input","required":true},"storageDate":{"date":true,"labelInForm":"入库时间","inputType":"date_input"},"categoryId":{"labelInForm":"分类","inputType":"select_box","required":true}};
    var AUTH_PREFIX="eam_goods_stock";

    // codeSame
    var CODE_SAME = [[${codeSame}]] ;
    // codeFill
    var CODE_FILL = [[${codeFill}]] ;
    // OWNER_CODE
    var OWNER_CODE = [[${ownerCode}]] ;
    // OWNER_TYPE
    var OWNER_TYPE = [[${ownerType}]] ;
    // ASSET_CATEGORY_DATA
    var ASSET_CATEGORY_DATA = [[${assetCategoryData}]] ;
    // CATEGORY_CODE
    var CATEGORY_CODE = [[${categoryCode}]] ;

</script>



<script th:src="'/business/eam/goods_stock/goods_stock_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/goods_stock/goods_stock_form.js?'+${cacheKey}"></script>

</body>
</html>