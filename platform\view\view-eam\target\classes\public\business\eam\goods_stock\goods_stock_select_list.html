<!--
/**
 * 库存物品 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2022-04-21 12:25:22
 */
 -->
<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <title th:text="${lang.translate('库存物品')}">库存物品</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 所属 , ownerId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 所属 , ownerTmpId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 库存所属 , ownerCode ,typeName=text_input, isHideInSearch=true -->
                    <!-- 所属类型 , ownerType ,typeName=text_input, isHideInSearch=true -->
                    <!-- 业务编号 , businessCode ,typeName=text_input, isHideInSearch=true -->
                    <!-- 状态 , goodsStatus ,typeName=select_box, isHideInSearch=true -->
                    <!-- 分类 , categoryId ,typeName=select_box, isHideInSearch=true -->
                    <!-- 厂商 , manufacturerId ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('物品厂商')}" class="search-label manufacturerId-label">物品厂商</span><span class="search-colon">:</span></div>
                        <div id="manufacturerId" th:data="${'/service-eam/eam-manufacturer/query-list'}" style="width:150px" extraParam="{}"></div>
                    </div>

                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('物品厂商')}" class="search-label brandId-label">物品厂商</span><span class="search-colon">:</span></div>
                        <div id="brandId" th:data="${'/service-eam/eam-brand/query-paged-list'}" style="width:150px" extraParam="{}"></div>
                    </div>
                    <!-- 规格型号 , model ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('规格型号')}" class="search-label model-label">规格型号</span><span class="search-colon">:</span></div>
                        <input id="model" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>
                    <!-- 物品名称 , name ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('物品名称')}" class="search-label name-label">物品名称</span><span class="search-colon">:</span></div>
                        <input id="name" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>


                </div>
                <!-- 搜索输入区域 -->

                <div class="layui-form toolbar search-inputs">
                    <!-- 办理状态 , status ,typeName=select_box, isHideInSearch=false -->
                    <!-- 物品编码 , code ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('物品编码')}" class="search-label code-label">物品编码</span><span class="search-colon">:</span></div>
                        <input id="code" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>


                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('物品价值')}" class="search-label costType-label">物品价值</span><span class="search-colon">:</span></div>
                        <div id="costType" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_goods_cost_type'}" style="width:150px" extraParam="{}"></div>
                    </div>

                    <!-- 仓库 , warehouseId ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('所在仓库')}" class="search-label warehouseId-label">所在仓库</span><span class="search-colon">:</span></div>
                        <div id="warehouseId" th:data="${'/service-eam/eam-warehouse/query-paged-list'}" style="width:150px" extraParam="{}"></div>
                    </div>

                    <!-- 档案位置 , goodsStockPositionStr ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('档案位置')}" class="search-label goodsStockPositionStr-label">档案位置</span><span class="search-colon">:</span></div>
                        <div id="goodsStockPositionStr" th:data="${'/service-eam/eam-position/query-paged-list'}" style="width:150px" extraParam="{}"></div>
                    </div>

                </div>
                <!-- 搜索输入区域 -->
<!--                <div class="layui-form toolbar search-inputs">-->
<!--                    &lt;!&ndash; 入库时间 , storageDate ,typeName=date_input, isHideInSearch=false &ndash;&gt;-->
<!--                    <div class="search-unit">-->
<!--                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('入库时间')}" class="search-label storageDate-label">入库时间</span><span class="search-colon">:</span></div>-->
<!--                        <input type="text" id="storageDate-begin" style="width: 150px" lay-verify="date" th:placeholder="${lang.translate('开始日期')}" autocomplete="off" class="layui-input search-input search-date-input"  readonly >-->
<!--                        <span class="search-dash">-</span>-->
<!--                        <input type="text" id="storageDate-end"  style="width: 150px"  lay-verify="date" th:placeholder="${lang.translate('结束日期')}" autocomplete="off" class="layui-input search-input search-date-input" readonly>-->
<!--                    </div>-->
<!--                </div>-->
            </div>



            <!-- 按钮区域 -->
            <div class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索')}">搜索</span></button>
            </div>
        </div>

        <div style="margin-top: 84px ">
            <table class="layui-table" th:id="(${tableId})"  lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>

<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
     </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">
</script>


<script th:inline="javascript">
  //  var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];

    var TABLE_ID = [[${tableId}]] ;
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]] ;
    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var SELECT_GOODSSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];

    var AUTH_PREFIX="eam_goods_stock_select";

    // var CATEGORY_ID= [[${categoryId}]] ;
     var CATEGORY_ID= [[${categoryId}]] ;
    var OWNER_CODE = [[${ownerCode}]] ;
    var OWNER_TMP_ID = [[${ownerTmpId}]] ;
    var OPER_TYPE = [[${operType}]] ;
    var OWNER_TYPE = [[${ownerType}]] ;
    var PAGE_TYPE = [[${pageType}]] ;
    var SELECTED_CODE = [[${selectedCode}]] ;

</script>

<script th:src="'/business/eam/goods_stock/goods_stock_select_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/goods_stock/goods_stock_select_list.js?'+${cacheKey}"></script>

</body>
</html>
