<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.camunda.connect</groupId>
    <artifactId>camunda-connect-root</artifactId>
    <version>1.5.2</version>
  </parent>

  <artifactId>camunda-connect-core</artifactId>
  <name>camunda BPM - connect - core</name>

  <dependencies>

    <dependency>
      <groupId>org.camunda.commons</groupId>
      <artifactId>camunda-commons-logging</artifactId>
    </dependency>

    <dependency>
      <groupId>org.camunda.commons</groupId>
      <artifactId>camunda-commons-utils</artifactId>
    </dependency>

    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
      <scope>test</scope>
    </dependency>

  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <executions>
          <execution>
            <id>bundle-manifest</id>
            <phase>process-classes</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <!-- check for api differences between latest minor release -->
  <profiles>
    <profile>
      <id>check-api-compatibility</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>clirr-maven-plugin</artifactId>
            <version>2.8</version>
            <configuration>
              <comparisonVersion>${connect.version.old}</comparisonVersion>
              <logResults>true</logResults>
              <excludes>
                <exclude>org/camunda/connect/impl/**</exclude>
              </excludes>
            </configuration>
            <executions>
              <execution>
                <id>all</id>
                <phase>verify</phase>
                <goals>
                  <goal>check-no-fork</goal>
                </goals>
                <configuration>
                  <textOutputFile>${project.build.directory}/clirr-all.txt</textOutputFile>
                  <failOnWarning>false</failOnWarning>
                  <failOnError>false</failOnError>
                </configuration>
              </execution>
              <execution>
                <id>restrictive</id>
                <phase>verify</phase>
                <goals>
                  <goal>check-no-fork</goal>
                </goals>
                <configuration>
                  <textOutputFile>${project.build.directory}/clirr-restrictive.txt</textOutputFile>
                  <failOnWarning>true</failOnWarning>
                  <ignoredDifferencesFile>../.clirr-jenkins-ignore.xml</ignoredDifferencesFile>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
