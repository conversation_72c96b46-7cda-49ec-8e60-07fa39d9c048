<project>
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.codehaus.plexus</groupId>
  <artifactId>plexus</artifactId>
  <packaging>pom</packaging>
  <name>Plexus</name>
  <version>1.0.4</version>
  <ciManagement>
    <notifiers>
      <notifier>
        <type>mail</type>
        <configuration>
          <address><EMAIL></address>
        </configuration>
      </notifier>
      <notifier>
        <type>irc</type>
        <configuration>
          <host>irc.codehaus.org</host>
          <port>6667</port>
          <channel>#plexus</channel>
        </configuration>
      </notifier>      
    </notifiers>
  </ciManagement>
  <inceptionYear>2001</inceptionYear>
  <mailingLists>
    <mailingList>
      <name>Plexus Developer List</name>
      <subscribe>http://lists.codehaus.org/mailman/listinfo/plexus-dev</subscribe>
      <unsubscribe>http://lists.codehaus.org/mailman/listinfo/plexus-dev</unsubscribe>
      <archive>http://lists.codehaus.org/pipermail/plexus-dev/</archive>
    </mailingList>
  </mailingLists>

  <distributionManagement>
    <repository>
      <id>repo1</id>
      <name>Maven Central Repository</name>
      <url>scp://repo1.maven.org/home/<USER>/maven/repository-staging/to-ibiblio/maven2</url>
    </repository>
    <snapshotRepository>
      <id>snapshots</id>
      <name>Maven Central Development Repository</name>
      <url>scp://repo1.maven.org/home/<USER>/maven/repository-staging/snapshots/maven2</url>
    </snapshotRepository>
  </distributionManagement>
  <repositories>
    <repository>
      <id>snapshots</id>
      <name>Maven Snapshot Development Repository</name>
      <url>http://snapshots.maven.codehaus.org/maven2</url>
      <releases>
        <enabled>false</enabled>
      </releases>
    </repository>
  </repositories>
  <pluginRepositories>
    <pluginRepository>
      <id>snapshots-plugins</id>
      <name>Maven Snapshot Plugins Development Repository</name>
      <url>http://snapshots.maven.codehaus.org/maven2</url>
      <releases>
        <enabled>false</enabled>
      </releases>
    </pluginRepository>
  </pluginRepositories>

  <developers>
    <developer>
      <id>jvanzyl</id>
      <name>Jason van Zyl</name>
      <email><EMAIL></email>
      <organization>Zenplex</organization>
      <roles>
        <role>Developer</role>
        <role>Release Manager</role>
      </roles>
    </developer>
    <developer>
      <id>kaz</id>
      <name>Pete Kazmier</name>
      <email></email>
      <organization></organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>jtaylor</id>
      <name>James Taylor</name>
      <email><EMAIL></email>
      <organization></organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>dandiep</id>
      <name>Dan Diephouse</name>
      <email><EMAIL></email>
      <organization>Envoi solutions</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>kasper</id>
      <name>Kasper Nielsen</name>
      <email><EMAIL></email>
      <organization></organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>bwalding</id>
      <name>Ben Walding</name>
      <email><EMAIL></email>
      <organization>Walding Consulting Services</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>mhw</id>
      <name>Mark Wilkinson</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>michal</id>
      <name>Michal Maczka</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>evenisse</id>
      <name>Emmanuel Venisse</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Trygve Laugst&oslash;l</name>
      <id>trygvis</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Kenney Westerhof</name>
      <id>kenney</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
  </developers>
  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>3.8.1</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <scm>
    <connection>scm:svn:svn://svn.codehaus.org/plexus/scm/trunk/</connection>
    <developerConnection>scm:svn:https://svn.codehaus.org/plexus/trunk</developerConnection>
  </scm>
  <organization>
    <name>Codehaus</name>
    <url>http://www.codehaus.org/</url>
  </organization>
  <modules>
    <module>plexus-appserver</module>
    <module>plexus-archetypes</module>
    <module>plexus-components</module>
    <module>plexus-component-factories</module>
    <module>plexus-containers</module>
    <module>plexus-logging</module>
    <module>plexus-maven-plugin</module>
    <module>plexus-services</module>
    <module>plexus-tools</module>
    <module>plexus-utils</module>
  </modules>
  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-release-plugin</artifactId>
        <configuration>
          <tagBase>https://svn.codehaus.org/plexus/tags</tagBase>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
