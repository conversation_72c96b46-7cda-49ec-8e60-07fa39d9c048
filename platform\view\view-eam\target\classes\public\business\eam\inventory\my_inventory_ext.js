/**
 * 资产盘点 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2021-11-20 17:27:20
 */

layui.config({
    dir: layuiPath,
    base: '/module/'
}).extend({
    xmSelect: 'xm-select/xm-select',
    foxnicUpload: 'upload/foxnic-upload'
})
//
layui.define(['form', 'table', 'util', 'settings', 'admin', 'upload','foxnic','xmSelect','laydate','foxnicUpload','dropdown'],function () {

    var admin = layui.admin,settings = layui.settings,form = layui.form,upload = layui.upload,laydate= layui.laydate,dropdown=layui.dropdown;
    table = layui.table,layer = layui.layer,util = layui.util,fox = layui.foxnic,xmSelect = layui.xmSelect,foxup=layui.foxnicUpload;

    const moduleURL="/service-eam/eam-inventory";

    //列表页的扩展
    var list={
        /**
         * 列表页初始化前调用
         * */
        beforeInit:function () {
            console.log("list:beforeInit");
        },
        /**
         * 表格渲染前调用
         * @param cfg 表格配置参数
         * */
        beforeTableRender:function (cfg){
            cfg.cellMinWidth=160;;
        },
        /**
         * 表格渲染后调用
         * */
        afterTableRender :function (){

        },
        afterSearchInputReady: function() {
            console.log("list:afterSearchInputReady");
        },
        /**
         * 对话框打开之前调用，如果返回 null 则不打开对话框
         * */
        beforeDialog:function (param){
            param.title="覆盖对话框标题";
            return param;
        },
        /**
         * 对话框回调，表单域以及按钮 会自动改变为选中的值，此处处理额外的逻辑即可
         * */
        afterDialog:function (param,result) {
            console.log('dialog',param,result);
        },
        /**
         * 查询前调用
         * @param conditions 复合查询条件
         * @param param 请求参数
         * @param location 调用的代码位置
         * */
        beforeQuery:function (conditions,param,location) {
            console.log('beforeQuery',conditions,param,location);
            param.ownerCode=OWNER_CODE;
            return true;
        },
        /**
         * 查询结果渲染后调用
         * */
        afterQuery : function (data) {
            console.log('afterDataFill',data);
            for (var i = 0; i < data.length; i++) {
                //如果审批中或审批通过的不允许编辑
                if(data[i].inventoryStatus=="not_start") {
                    console.log('none');
                }else if(data[i].inventoryStatus=="acting"){
                    fox.disableButton($('.ops-delete-button').filter("[data-id='" + data[i].id + "']"), true);
                    fox.disableButton($('.ops-edit-button').filter("[data-id='" + data[i].id + "']"), true);
                }else if(data[i].inventoryStatus=="cancel"){
                    fox.disableButton($('.ops-edit-button').filter("[data-id='" + data[i].id + "']"), true);
                }else if(data[i].inventoryStatus=="finish"){
                    fox.disableButton($('.ops-delete-button').filter("[data-id='" + data[i].id + "']"), true);
                    fox.disableButton($('.ops-edit-button').filter("[data-id='" + data[i].id + "']"), true);
                }
            }

        },
        /**
         * 进一步转换 list 数据
         * */
        templet:function (field,value,r) {
            if(value==null) return "";
            if(r.allEmployee&&r.allEmployee=="enable"){
                return "<div style=\"color:green;font-weight: bold \" >"+value+"</div>"
            }else{
                return value;
            }
        },
        /**
         * 在新建或编辑窗口打开前调用，若返回 false 则不继续执行后续操作
         * */
        beforeEdit:function (data) {
            console.log('beforeEdit',data);
            return true;
        },
        /**
         * 单行删除前调用，若返回false则不执行后续操作
         * */
        beforeSingleDelete:function (data) {
            console.log('beforeSingleDelete',data);
            return true;
        },
        afterSingleDelete:function (data){
            console.log('beforeSingleDelete',data);
            return true;
        },
        /**
         * 批量删除前调用，若返回false则不执行后续操作
         * */
        beforeBatchDelete:function (selected) {
            console.log('beforeBatchDelete',selected);
            return true;
        },
        /**
         * 批量删除后调用，若返回false则不执行后续操作
         * */
        afterBatchDelete:function (data) {
            console.log('afterBatchDelete',data);
            return true;
        },
        /**
         * 工具栏按钮事件前调用，如果返回 false 则不执行后续代码
         * */
        beforeToolBarButtonEvent:function (selected,obj) {
            console.log('beforeToolBarButtonEvent',selected,obj);
            return true;
        },
        /**
         * 列表操作栏按钮事件前调用，如果返回 false 则不执行后续代码
         * */
        beforeRowOperationEvent:function (data,obj) {
            console.log('beforeRowOperationEvent',data,obj);
            return true;
        },
        /**
         * 表格右侧操作列更多按钮事件
         * */
        moreAction:function (menu,data, it){

            console.log('moreAction',menu,data,it);
        },
        inventoryAct:function(url,btn,selected,it){

            if(selected.length==0){
                top.layer.msg("请选择要操作的盘点项!");
                return ;
            }

            if(selected.length>1){
                top.layer.msg("只选择一个盘点项,不可多选!");
                return ;
            }

            top.layer.confirm(fox.translate('确定进行该操作吗？'), function (i) {
                top.layer.close(i);
                admin.post(url, {id:selected[0]}, function (r) {
                    if (r.success) {
                        top.layer.msg(r.message, {time: 1000});
                        window.module.refreshTableData();
                    } else {
                        var errs = [];
                        if(r.errors&&r.errors.length>0){
                            for (var i = 0; i < r.errors.length; i++) {
                                if (errs.indexOf(r.errors[i].message) == -1) {
                                    errs.push(r.errors[i].message);
                                }
                            }
                            top.layer.msg(errs.join("<br>"), {time: 2000});
                        } else {
                            top.layer.msg(r.message, {time: 2000});
                        }
                    }
                }, {delayLoading: 1000, elms: [btn]});
            });


        },
        inventoryStart:function (selected,it){
            var url=moduleURL+"/start";
            var btn=$('#inventory-start');
            console.log('inventoryStart',selected,it);
            list.inventoryAct(url,btn,selected,it);
        },
        inventoryFinish:function (selected,it){
            var url=moduleURL+"/finish";
            var btn=$('#inventory-finish');
            console.log('inventoryFinish',selected,it);
            list.inventoryAct(url,btn,selected,it);
        },
        inventoryCancel:function (selected,it){
            var url=moduleURL+"/cancel";
            var btn=$('#inventory-cancel');
            console.log('inventoryCancel',selected,it);
            list.inventoryAct(url,btn,selected,it);
        },
        inventoryDataSync:function (selected,it){
            var url=moduleURL+"/data-sync";
            var btn=$('#inventory-data-sync');
            console.log('inventoryDataSync',selected,it);
            list.inventoryAct(url,btn,selected,it);

        },

        downloadBill:function (data){
            console.log('downloadBill',data);
            var downloadUrl="/service-eam/eam-asset-bill/query-inventory-bill";
            fox.submit(downloadUrl,{id:data.id});
        },

        inventoryDetail:function (data){
            console.log('inventoryDetail',data);
        },
        /**
         * 末尾执行
         */
        ending:function() {

        }
    }

    //表单页的扩展
    var form={
        /**
         * 表单初始化前调用
         * */
        beforeInit:function () {
            //获取参数，并调整下拉框查询用的URL
            //var companyId=admin.getTempData("companyId");
            //fox.setSelectBoxUrl("employeeId","/service-hrm/hrm-employee/query-paged-list?companyId="+companyId);
            console.log("form:beforeInit")
        },
        /**
         * 窗口调节前
         * */
        beforeAdjustPopup:function () {
            console.log('beforeAdjustPopup');
            return true;
        },
        /**
         * 表单数据填充前
         * */
        beforeDataFill:function (data) {
         //   positionIds inventoryManagerIds inventoryDirectorIds
            var inventoryUserIds="";
            if(data&&data.inventoryUser){
                for(var i=0;i<data.inventoryUser.length;i++){
                    if(i==0){
                        inventoryUserIds=data.inventoryUser[i].id;
                    }else{
                        inventoryUserIds=inventoryUserIds+","+data.inventoryUser[i].id;
                    }
                }
                data.inventoryUserIds=inventoryUserIds;

            }else{
                data.inventoryUserIds="";
            }

            //保管人inventoryManagerIds
            var inventoryManagerIds="";
            if(data&&data.manager){
                for(var i=0;i<data.manager.length;i++){
                    if(i==0){
                        inventoryManagerIds=data.manager[i].id;
                    }else{
                        inventoryManagerIds=inventoryManagerIds+","+data.manager[i].id;
                    }
                }
                data.inventoryManagerIds=inventoryManagerIds;

            }else{
                data.inventoryManagerIds="";
            }

            var inventoryDirectorIds="";
            if(data&&data.director){
                for(var i=0;i<data.director.length;i++){
                    if(i==0){
                        inventoryDirectorIds=data.director[i].id;
                    }else{
                        inventoryDirectorIds=inventoryDirectorIds+","+data.director[i].id;
                    }
                }
                data.inventoryDirectorIds=inventoryDirectorIds;

            }else{
                data.inventoryDirectorIds="";
            }


            var categoryIds="";
            if(data&&data.category){
                for(var i=0;i<data.category.length;i++){
                    if(i==0){
                        categoryIds=data.category[i].id;
                    }else{
                        categoryIds=categoryIds+","+data.category[i].id;
                    }
                }
                data.categoryIds=categoryIds;
            }else{
                data.categoryIds="";
            }

            console.log('beforeDataFill',data);
        },
        /**
         * 表单数据填充后
         * */
        afterDataFill:function (data) {
            console.log('afterDataFill',data);
        },
        /**
         * 对话框打开之前调用，如果返回 null 则不打开对话框
         * */
        beforeDialog:function (param){
            param.title="覆盖对话框标题";
            return param;
        },
        /**
         * 对话框回调，表单域以及按钮 会自动改变为选中的值，此处处理额外的逻辑即可
         * */
        afterDialog:function (param,result) {
            console.log('dialog',param,result);
        },
        /**
         * 数据提交前，如果返回 false，停止后续步骤的执行
         * */
        beforeSubmit:function (data) {
            console.log("beforeSubmit",data);
            data.ownerCode=OWNER_CODE;
            data.planId=PLAN_ID;
            return true;
        },
        /**
         * 数据提交后窗口关闭前，如果返回 false，停止后续步骤的执行
         * */
        betweenFormSubmitAndClose:function (param,result) {
            console.log("betweenFormSubmitAndClose",result);
            return true;
        },
        /**
         * 数据提交后执行
         * */
        afterSubmit:function (param,result) {
            console.log("afterSubmitt",param,result);
        },

        /**
         * 末尾执行
         */
        ending:function() {

        }
    }
    //
    window.pageExt={form:form,list:list};
});