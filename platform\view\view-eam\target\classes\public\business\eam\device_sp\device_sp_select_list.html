<!--
/**
 * 备件清单 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2023-07-17 15:25:53
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
    <title th:text="${lang.translate('备件清单')}">备件清单</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 备件状态 , status ,typeName=radio_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('备件状态')}" class="search-label status-label">备件状态</span><span class="search-colon">:</span></div>


                        <div id="status" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.DeviceSpStatusEnum')}" style="width:140px"></div>
                    </div>
                    <!-- 备件序列 , sn ,typeName=text_input, isHideInSearch=true -->
                    <!-- 存放位置 , locId ,typeName=select_box, isHideInSearch=true -->
                    <!-- 图片 , pictureId ,typeName=upload, isHideInSearch=true -->
                    <!-- 备注 , notes ,typeName=text_area, isHideInSearch=true -->
                    <!-- 是否锁定 , locked ,typeName=text_input, isHideInSearch=true -->
                    <!-- 保管人员 , managerUserId ,typeName=button, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('保管人员')}" class="search-label managerUserId-label">保管人员</span><span class="search-colon">:</span></div>
                            <input lay-filter="managerUserId" id="managerUserId" name="managerUserId"  type="hidden" class="layui-input"   />
                            <button id="managerUserId-button" type="button" action-type="emp-dialog" class="layui-btn layui-btn-primary   " style="width: 140px"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择人员')}" th:default-label="${lang.translate('请选择人员')}">按钮文本</span></button>
                    </div>
                    <!-- 备件分类 , type ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('备件分类')}" class="search-label type-label">备件分类</span><span class="search-colon">:</span></div>
                        <div id="type" th:data="${'/service-eam/eam-device-sp-type/query-paged-list'}" style="width:140px" extraParam="{}"></div>
                    </div>
                    <!-- 使用场景 , usageRange ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('使用场景')}" class="search-label usageRange-label">使用场景</span><span class="search-colon">:</span></div>
                        <div id="usageRange" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_sp_usage_range'}" style="width:140px" extraParam="{}"></div>
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 备件编号 , code ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('备件编号')}" class="search-label code-label">备件编号</span><span class="search-colon">:</span></div>
                        <input id="code" class="layui-input search-input" style="width: 140px" type="text" />
                    </div>
                    <!-- 备件名称 , name ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('备件名称')}" class="search-label name-label">备件名称</span><span class="search-colon">:</span></div>
                        <input id="name" class="layui-input search-input" style="width: 140px" type="text" />
                    </div>
                    <!-- 供应厂商 , supplier ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('供应厂商')}" class="search-label supplier-label">供应厂商</span><span class="search-colon">:</span></div>
                        <input id="supplier" class="layui-input search-input" style="width: 140px" type="text" />
                    </div>
                    <!-- 来源描述 , sourceDesc ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('来源描述')}" class="search-label sourceDesc-label">来源描述</span><span class="search-colon">:</span></div>
                        <input id="sourceDesc" class="layui-input search-input" style="width: 140px" type="text" />
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 适配设备 , adaptingDevice ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('适配设备')}" class="search-label adaptingDevice-label">适配设备</span><span class="search-colon">:</span></div>
                        <input id="adaptingDevice" class="layui-input search-input" style="width: 140px" type="text" />
                    </div>
                    <!-- 入库时间 , insertTime ,typeName=date_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('入库时间')}" class="search-label insertTime-label">入库时间</span><span class="search-colon">:</span></div>
                            <input type="text" id="insertTime-begin" style="width: 140px" lay-verify="date" th:placeholder="${lang.translate('开始日期')}" autocomplete="off" class="layui-input search-input search-date-input"  readonly >
                            <span class="search-dash">-</span>
                            <input type="text" id="insertTime-end"  style="width: 140px"  lay-verify="date" th:placeholder="${lang.translate('结束日期')}" autocomplete="off" class="layui-input search-input search-date-input" readonly>
                    </div>


                </div>
            </div>


            <!-- 按钮区域 -->
            <div id="search-area" class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索','','cmp:table.search')}">搜索</span></button>
                <button id="search-button-advance" class="layui-btn layui-btn-primary icon-btn search-button-advance"><i class="layui-icon">&#xe671;</i><span th:text="${lang.translate('更多','','cmp:table.search')}">更多</span></button>
            </div>
        </div>

        <div id="table-area" style="margin-top: 84px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
        <button   id="add-button" class="layui-btn icon-btn layui-btn-sm create-new-button " lay-event="create"><i class="layui-icon">&#xe654;</i><span th:text="${lang.translate('确定','','cmp:table.button')}">确定</span></button>
    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">


</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
    var RADIO_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.DeviceSpStatusEnum')}]];
    var AUTH_PREFIX="eam_device_sp_select";
    var SELECTED_CODE = [[${selectedCode}]] ;
    var OWNER_ID = [[${ownerId}]] ;
    var OWNER_TYPE = [[${ownerType}]] ;
    var PAGE_TYPE = [[${pageType}]] ;



</script>

<script th:src="'/business/eam/device_sp/device_sp_s_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/device_sp/device_sp_s_list.js?'+${cacheKey}"></script>

</body>
</html>