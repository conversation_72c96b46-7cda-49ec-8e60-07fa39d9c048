<!--
/**
 * 巡检点 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2023-07-06 20:04:14
 */
 -->
<!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
    <title th:text="${lang.translate('巡检点')}">巡检点</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >
    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="opacity:0">
        <input name="id" id="id"  type="hidden"/>
        <!--开始：group 循环-->
        <div class="layui-row form-row" id="random-0188-content">
            <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column" >

            <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                <div class="layui-form-label "><div>修改方式</div><div class="layui-required">*</div></div>
                <div class="layui-input-block ">
                    <input input-type="radio" type="radio" name="action" lay-filter="action" value="append" title="追加" checked="checked">
                    <input input-type="radio" type="radio" name="action" lay-filter="action" value="replace" title="替换">
                </div>
            </div>


                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--结束：group循环-->
        <fieldset class="layui-elem-field layui-field-title form-group-title" id="random-8664-fieldset">
            <legend>巡检项</legend>
        </fieldset>
        <div class="layui-row form-row" style="text-align: center;" id="random-8664-content">
            <div style="display: inline-block;padding-right: 8px;padding-left: 8px" class="layui-col-xs12">
                <iframe id="random-8664-iframe" js-fn="checkSelectList" class="form-iframe" frameborder="0" style="width: 100%"></iframe>
            </div>
        </div>
        <div style="height: 8px"></div>
        <div style="height: 20px"></div>
    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消','','form.button')}" >取消</button>
    <button class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存','','form.button')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var VALIDATE_CONFIG={};
    var AUTH_PREFIX="eam_inspection_point_item_c";
    var POINT_IDS = [[${pointIds}]];
    var SELECT_ACTION_DATA = [{"code":"replace","name":"替换","description":"替换","text":"替换"},{"code":"append","name":"追加","description":"追加","text":"追加"}];

</script>

<script th:src="'/business/eam/inspection_point/modify_check_item.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/inspection_point/modify_check_item_ext.js?'+${cacheKey}"></script>

</body>
</html>