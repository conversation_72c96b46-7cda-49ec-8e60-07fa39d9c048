<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.12.RELEASE</version>
    </parent>
    <groupId>com.dt.platform</groupId>
    <artifactId>parent-eam</artifactId>
    <version>*******</version>
    <packaging>pom</packaging>
    <name>parent-eam</name>
    <description>platform-parent</description>
    <properties>
        <skipTests>true</skipTests>
        <java.version>1.8</java.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <knife4j.version>3.0.2</knife4j.version>
        <platform.version>*******</platform.version>
        <foxnic.version>1.8.0.RELEASE</foxnic.version>
        <foxnic-web.version>1.8.0.RELEASE</foxnic-web.version>
        <mysql.version>5.1.49</mysql.version>


        <foxnic-web-plus.version>*******</foxnic-web-plus.version>
        <foxnic-oa.version>*******</foxnic-oa.version>
        <foxnic-ops.version>*******</foxnic-ops.version>
        <foxnic-hr.version>*******</foxnic-hr.version>
        <foxnic-contract.version>*******</foxnic-contract.version>
        <foxnic-customer.version>*******</foxnic-customer.version>

    </properties>
    <modules>
        <!-- 通用模块 -->
        <module>./common/domain</module>
        <module>./common/framework</module>
        <module>./common/proxy</module>
        <!-- 接口服务 -->
        <module>./service/service-job</module>
        <module>./service/service-eam</module>
        <!-- 界面视图 -->
        <module>./view/view-eam</module>
        <!-- 聚合 -->
        <module>./wrapper/wrapper-all</module>
        <module>./wrapper/wrapper-camunda</module>
    </modules>
    <dependencies>
        <!-- https://mvnrepository.com/artifact/io.minio/minio -->
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <version>8.5.11</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.9.0</version>
        </dependency>
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>
    </dependencies>
    <build>
        <defaultGoal>compile</defaultGoal>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.tql</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.0.0-M5</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.3</version>
                <configuration>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <excludes>
                        <exclude>**/node_modules/**</exclude>
                    </excludes>
                    <archive>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <classpathPrefix>lib/</classpathPrefix>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <repositories>
        <repository>
            <id>foxnic-releases</id>
            <name>FoxnicReleases</name>
            <url>http://foxnicweb.com:9091/repository/maven-releases/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
        <repository>
            <id>foxnic-snapshots</id>
            <name>FoxnicSnapshots</name>
            <url>http://foxnicweb.com:9091/repository/maven-snapshots/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>releases</id>
            <name>Releases</name>
            <url>http://foxnicweb.com:9091/repository/maven-releases/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
    <distributionManagement>
        <repository>
            <id>releases</id>
            <name>Releases</name>
            <url>http://foxnicweb.com:9091/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>snapshot</id>
            <name>Snapshot</name>
            <url>http://foxnicweb.com:9091/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
