<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
 <!-- General information -->

  <modelVersion>4.0.0</modelVersion>
  <parent>
      <groupId>com.fasterxml</groupId>
      <artifactId>oss-parent</artifactId>
      <version>35</version>
  </parent>
  <groupId>com.fasterxml.uuid</groupId>
  <artifactId>java-uuid-generator</artifactId>
  <packaging>bundle</packaging>
  <name>Java UUID Generator</name>
  <version>3.2.0</version>
  <description>
Java UUID Generator (JUG) is a Java library for generating
Universally Unique IDentifiers, UUIDs (see http://en.wikipedia.org/wiki/UUID).
It can be used either as a component in a bigger application, or as a standalone command line tool.
JUG generates UUIDs according to the IETF UUID draft specification.
JUG supports all 3 official UUID generation methods.
</description>
  <scm>
    <connection>scm:git:git://github.com/cowtowncoder/java-uuid-generator.git</connection>
    <url>scm:git:**************:cowtowncoder/java-uuid-generator.git</url>
    <developerConnection>scm:git:**************:cowtowncoder/java-uuid-generator.git</developerConnection>
    <tag>java-uuid-generator-3.2.0</tag>
  </scm>
  <developers>
    <developer>
      <id>cowtowncoder</id>
      <name>Tatu Saloranta</name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <url>http://wiki.fasterxml.com/JugHome</url>
  <issueManagement>
    <url>http://github.com/cowtowncoder/java-uuid-generator/issues</url>
  </issueManagement>

  <prerequisites>
    <maven>2.2.1</maven>
  </prerequisites>
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <log4j.version>1.2.17</log4j.version>
  </properties>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <organization>
    <name>FasterXML.com</name>
    <url>http://fasterxml.com</url>
  </organization>

 <!-- Dependency information -->
 
  <dependencies>
    <dependency> <!-- log4j is optional, but needed for compilation -->
      <groupId>log4j</groupId>
      <artifactId>log4j</artifactId>
      <version>${log4j.version}</version>
      <scope>provided</scope>
    </dependency>
    <!-- and for testing, JUnit is needed -->
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>${version.junit}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

 <!-- Also: must specify non-standard source level -->
  <build>
    <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>${version.plugin.compiler}</version>
          <configuration>
            <source>1.6</source>
            <target>1.6</target>
          </configuration>
        </plugin>
        <plugin><!-- plug-in to attach source bundle in repo -->
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>2.1.2</version>
          <executions>
            <execution>
              <id>attach-sources</id>
              <goals>
                <goal>jar</goal>
              </goals>
            </execution>
          </executions>
        </plugin>

        <!-- javadocs? yes please -->
       <plugin>
         <groupId>org.apache.maven.plugins</groupId>
         <artifactId>maven-javadoc-plugin</artifactId>
         <version>${version.plugin.javadoc}</version>
         <executions>
             <execution>
                  <id>attach-javadocs</id>
                  <phase>verify</phase>
                  <goals>
                      <goal>jar</goal>
                  </goals>
             </execution>
         </executions>
       </plugin>

       <!-- Plus, let's make jars OSGi bundles as well  -->
        <plugin>
          <groupId>org.apache.felix</groupId>
          <artifactId>maven-bundle-plugin</artifactId>
          <extensions>true</extensions>
          <configuration>
            <instructions>
              <Bundle-Name>${project.name}</Bundle-Name>
              <Bundle-SymbolicName>${project.artifactId}</Bundle-SymbolicName>
              <Bundle-Description>${project.description}</Bundle-Description>
              <Bundle-Vendor>FasterXML.com</Bundle-Vendor>
              <Export-Package>
                com.fasterxml.uuid;version="${project.version}",
                com.fasterxml.uuid.ext;version="${project.version}",
                com.fasterxml.uuid.impl;version="${project.version}"
              </Export-Package>
              <Import-Package>
                com.fasterxml.uuid;version="[${project.version},${project.version}]",
                com.fasterxml.uuid.ext;version="[${project.version},${project.version}]",
                com.fasterxml.uuid.impl;version="[${project.version},${project.version}]"
              </Import-Package>
              <Private-Package />
              <DynamicImport-Package>
                org.apache.log4j;version="[${log4j.version},1.3)"
              </DynamicImport-Package>
            </instructions>
          </configuration>
       </plugin>

        <!-- for maven release, need yet another plug-in -->
        <!-- And Sonatype also mandates GPG... -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <configuration>
            <mavenExecutorId>forked-path</mavenExecutorId>
          </configuration>
        </plugin>
     <!--  22-Mar-2019, tatu: Add rudimentary JDK9+ module info. To build with JDK 8
             will have to use `moduleInfoFile` which is not optimal but anything else
             requires JDK 9+.
        -->
      <plugin>
        <groupId>org.moditect</groupId>
        <artifactId>moditect-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>add-module-infos</id>
            <phase>package</phase>
            <goals>
              <goal>add-module-info</goal>
            </goals>
            <configuration>
              <overwriteExistingFiles>true</overwriteExistingFiles>
              <module>
                <moduleInfoFile>src/moditect/module-info.java</moduleInfoFile>
              </module>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <profiles>
        <profile>
            <id>release-sign-artifacts</id>
            <activation>
                <property>
                    <name>performRelease</name>
                    <value>true</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>${version.plugin.gpg}</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
  </profiles>

  <!-- NOTE: repositories from parent POM -->

</project>
