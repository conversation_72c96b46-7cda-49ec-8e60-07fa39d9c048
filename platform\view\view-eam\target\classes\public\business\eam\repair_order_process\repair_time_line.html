<!--
/**
 * 维修验收 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2023-08-05 09:01:10
 */
 -->
<!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta charset="utf-8"/>
	<meta name="referrer" content="no-referrer">
	<title th:text="${lang.translate('时间轴')}">时间轴</title>
	<link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
	<link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
	<link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
	<link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
	<link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
	<link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
	<link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
	<script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
	<style>
	</style>
	<link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="background-color: white">
<div class="form-container" style="width:100%"  >
	<ul class="layui-timeline" id="lineData">
	</ul>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">

	var ORDER_ID = [[${orderId}]];
</script>



<script th:src="'/business/eam/repair_order_process/repair_time_l_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/repair_order_process/repair_time_l.js?'+${cacheKey}"></script>

</body>
</html>