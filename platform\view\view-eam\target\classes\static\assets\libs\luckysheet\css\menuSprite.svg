<?xml version='1.0' encoding='UTF-8'?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"><svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" version="1.1" x="0" y="0" width="590px" height="424px" viewBox="0 0 590 424" preserveAspectRatio="none"><g transform="translate(36,90)"><path fill="#000000" fill-rule="evenodd" d="M4,0 L2,0 C0.895,0 0,0.895 0,2 L0,10 L2,10 L2,8 L4,8 L4,10 L6,10 L6,2 C6,0.895 5.105,0 4,0 Z M2,6 L4,6 L4,2 L2,2 L2,6 Z M14,3.5 L14,2 C14,0.895 13.105,0 12,0 L8,0 L8,10 L12,10 C13.105,10 14,9.105 14,8 L14,6.5 C14,5.672 13.328,5 12.5,5 C13.328,5 14,4.328 14,3.5 Z M10,8 L12,8 L12,6 L10,6 L10,8 Z M10,4 L12,4 L12,2 L10,2 L10,4 Z" transform="translate(2 4)"/>
</g><g transform="translate(108,252)"><path fill="#000000" fill-rule="evenodd" d="M16,0 L12,0 L12,1 L4,1 L4,0 L0,0 L0,4 L1,4 L1,12 L0,12 L0,16 L4,16 L4,15 L12,15 L12,16 L16,16 L16,12 L15,12 L15,4 L16,4 L16,0 Z M3,13 L3,3 L13,3 L13,13 L3,13 Z M7,4 C5.895,4 5,4.895 5,6 L5,12 L7,12 L7,10 L9,10 L9,12 L11,12 L11,6 C11,4.895 10.105,4 9,4 L7,4 Z M7,8 L9,8 L9,6 L7,6 L7,8 Z" transform="translate(1 1)"/>
</g><g transform="translate(536,184)"><path fill="#000000" fill-rule="evenodd" d="M7,14 C10.8659932,14 14,10.8659932 14,7 C14,3.13400675 10.8659932,0 7,0 C3.13400675,0 0,3.13400675 0,7 C0,10.8659932 3.13400675,14 7,14 Z M10.53,4.53 L9.47,3.47 L7,5.94 L4.53,3.47 L3.47,4.53 L5.94,7 L3.47,9.47 L4.53,10.53 L7,8.06 L9.47,10.53 L10.53,9.47 L8.06,7 L10.53,4.53 Z" transform="translate(2 2)"/>
</g><g transform="translate(36,324)"><path fill="#000000" d="M0,7 L1.99632026,7 L1.99632026,9 L0,9 L0,7 Z M10,12 L15,12 L15,14 L10,14 L10,12 Z M16.9694527,11.9570946 L18,13.0188246 L16.9734025,14.0389803 L15,16 L15,10 L16.9694527,11.9570946 Z M2.99264052,2.99686968 C2.99264052,1.34174426 4.33709312,-1.59872116e-14 5.98804084,-1.59872116e-14 C7.64235476,-1.59872116e-14 8.98344117,1.34739093 8.98344117,2.99686968 L8.98344117,6.00313032 C8.98344117,7.65825574 7.63898856,9 5.98804084,9 C4.33372692,9 2.99264052,7.65260907 2.99264052,6.00313032 L2.99264052,2.99686968 Z M4.5,2.99857602 L4.5,6.00142398 C4.5,6.83497024 5.17157288,7.5 6,7.5 C6.83420277,7.5 7.5,6.82906466 7.5,6.00142398 L7.5,2.99857602 C7.5,2.16502976 6.82842712,1.5 6,1.5 C5.16579723,1.5 4.5,2.17093534 4.5,2.99857602 Z M9.99264052,2.99686968 C9.99264052,1.34174426 11.3370931,-1.59872116e-14 12.9880408,-1.59872116e-14 C14.6423548,-1.59872116e-14 15.9834412,1.34739093 15.9834412,2.99686968 L15.9834412,6.00313032 C15.9834412,7.65825574 14.6389886,9 12.9880408,9 C11.3337269,9 9.99264052,7.65260907 9.99264052,6.00313032 L9.99264052,2.99686968 Z M11.5,2.99857602 L11.5,6.00142398 C11.5,6.83497024 12.1715729,7.5 13,7.5 C13.8342028,7.5 14.5,6.82906466 14.5,6.00142398 L14.5,2.99857602 C14.5,2.16502976 13.8284271,1.5 13,1.5 C12.1657972,1.5 11.5,2.17093534 11.5,2.99857602 Z" transform="translate(0 1)"/>
</g><g transform="translate(216,234)"><path fill="#000000" fill-rule="evenodd" d="M9,3.5 C9,1.57 7.43,0 5.5,0 L1.77635684e-15,0 L1.77635684e-15,12 L6.25,12 C8.04,12 9.5,10.54 9.5,8.75 C9.5,7.45 8.73,6.34 7.63,5.82 C8.46,5.24 9,4.38 9,3.5 Z M5,2 C5.82999992,2 6.5,2.67 6.5,3.5 C6.5,4.33 5.82999992,5 5,5 L3,5 L3,2 L5,2 Z M3,10 L3,7 L5.5,7 C6.32999992,7 7,7.67 7,8.5 C7,9.33 6.32999992,10 5.5,10 L3,10 Z" transform="translate(4 3)"/>
</g><g transform="translate(234,324)"><g fill="#000000" fill-rule="evenodd" transform="translate(2 2)">
    <path d="M5,0 L3,0 L3,2 L5,2 L5,0 L5,0 Z M8,6 L6,6 L6,8 L8,8 L8,6 L8,6 Z M8,9 L6,9 L6,11 L8,11 L8,9 L8,9 Z M11,6 L9,6 L9,8 L11,8 L11,6 L11,6 Z M5,6 L3,6 L3,8 L5,8 L5,6 L5,6 Z M11,0 L9,0 L9,2 L11,2 L11,0 L11,0 Z M8,3 L6,3 L6,5 L8,5 L8,3 L8,3 Z M8,0 L6,0 L6,2 L8,2 L8,0 L8,0 Z M2,9 L0,9 L0,11 L2,11 L2,9 L2,9 Z M12,11 L14,11 L14,9 L12,9 L12,11 L12,11 Z M12,5 L14,5 L14,3 L12,3 L12,5 L12,5 Z M12,8 L14,8 L14,6 L12,6 L12,8 L12,8 Z M12,0 L12,2 L14,2 L14,0 L12,0 L12,0 Z M2,0 L0,0 L0,2 L2,2 L2,0 L2,0 Z M2,3 L0,3 L0,5 L2,5 L2,3 L2,3 Z M2,6 L0,6 L0,8 L2,8 L2,6 L2,6 Z" opacity=".54"/>
    <polygon points="0 14 14 14 14 12 0 12"/>
  </g>
</g><g transform="translate(454,378)"><path fill="#000000" fill-rule="evenodd" d="M16,3.88888889 L16,13.2222222 C16,14.2044444 15.2035556,15 14.2222222,15 L1.77777778,15 C0.796444444,15 0,14.2044444 0,13.2222222 L0,3.88888889 L0,7 L2,7 L2,13 L14,13 L14,7 L16,7 L16,3.88888889 Z M5.5,7 L7.5,9.5 L10,6 L13,11 L3,11 L5.5,7 Z M1.90939331,1.92431641 L0,-1.77635684e-15 L0,5 L5,5 L3.2149353,3.21627808 C4.2902485,2.38413522 5.85793364,1.78571429 7.37579451,1.78571429 C10.1281305,1.78571429 11.1530865,2.71785714 11.9683059,5 L14,5 C12.9324508,2.00714286 10.9860518,-1.77635684e-15 7.37579451,-1.77635684e-15 C5.32221804,-1.77635684e-15 3.34185024,0.770744978 1.90939331,1.92431641 Z" transform="translate(1 2)"/>
</g><g transform="translate(500,220)"><path fill="#010101" fill-rule="evenodd" d="M2,10.5857864 L2,1.77635684e-15 L0,1.77635684e-15 L0,13 L0,14 L14,14 L14,12 L3.41421356,12 L10.7071068,4.70710678 L12,6 L12,2 L8,2 L9.29289322,3.29289322 L2,10.5857864 Z" transform="translate(2 2)"/>
</g><g transform="translate(428,18)"><path fill="#4285F4" fill-rule="evenodd" d="M6,4 L6,0 L4,0 L4,4 L0,4 L0,6 L4,6 L4,10 L6,10 L6,6 L10,6 L10,4 L6,4 Z" transform="translate(4 4)"/>
</g><g transform="translate(288,234)"><g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <polygon points="0 0 18 0 18 18 0 18"/>
        <path d="M6.0390625,13 L11.0495605,13 L11.0495605,7.46153846 L15,7.46153846 L8.5,1 L2,7.46153846 L6.0390625,7.46153846 L6.0390625,13 Z M2,15 L15,15 L15,17 L2,17 L2,15 Z" fill="#000000"/>
    </g>
</g><g transform="translate(90,90)"><path fill="#000000" fill-rule="evenodd" d="M15.5,3.29 L12.05375,0.39875 L11.09,1.54625 L14.53625,4.4375 L15.5,3.29 L15.5,3.29 Z M4.91,1.5425 L3.94625,0.395 L0.5,3.28625 L1.46375,4.43375 L4.91,1.5425 L4.91,1.5425 Z M7,9 L9.5,11.5 L11,10 L9,8 L9,5 L7,5 L7,9 Z M7.99625,2 C4.265,2 1.25,5.0225 1.25,8.75 C1.25,12.4775 4.265,15.5 7.99625,15.5 C11.7275,15.5 14.75,12.4775 14.75,8.75 C14.75,5.0225 11.7275,2 7.99625,2 Z M8,14 C5.10125,14 2.75,11.64875 2.75,8.75 C2.75,5.85125 5.10125,3.5 8,3.5 C10.89875,3.5 13.25,5.85125 13.25,8.75 C13.25,11.64875 10.9025,14 8,14 Z" transform="translate(1 1)"/>
</g><g transform="translate(518,18)"><polygon fill="#000000" fill-rule="evenodd" points="12 5 3.125 5 7.06 1.06 6 0 0 6 6 12 7.06 10.94 3.125 7 12 7" transform="matrix(-1 0 0 1 15 3)"/>
</g><g transform="translate(428,0)"><polygon fill="#000000" fill-rule="evenodd" points="-2 2 2 6 6 2" transform="rotate(90 3 10)"/>
</g><g transform="translate(162,360)"><path fill="#000000" fill-rule="evenodd" d="M6,10 L14,10 L14,8 L6,8 L6,10 Z M6,6 L14,6 L14,4 L6,4 L6,6 Z M14,12 L0,12 L0,14 L14,14 L14,12 Z M0.5,7 L4,10.5 L4,3.5 L0.5,7 Z M0,0 L0,2 L14,2 L14,0 L0,0 Z" transform="matrix(-1 0 0 1 16 2)"/>
</g><g transform="translate(198,234)"><path fill="#000000" fill-rule="evenodd" d="M13,0 L11,0 L11,1 L13,1 L13,2 L12,2 C11.448,2 11,2.4475 11,3 L11,5 L14,5 L14,4 L12,4 L12,3 L13,3 C13.5525,3 14,2.5525 14,2 L14,1 C14,0.4475 13.5525,0 13,0 Z M5,6.5 L8,11 L9.88503367,11 L6.5,5.5 L10,0 L8,0 L5,4.5 L2,0 L0,0 L3.5,5.5 L0,11 L2,11 L5,6.5 Z" transform="translate(2 4)"/>
</g><g transform="translate(18,270)"><g fill="none" fill-rule="evenodd" transform="translate(0 1)">
    <polygon fill="#4285F4" points="4.71 16 15.24 16 17.85 11 7.29 11"/>
    <polygon fill="#0F9D58" points="5.63 1.45 .14 11.45 2.8 16 8.28 6.02 5.63 1.45"/>
    <polygon fill="#FFC107" points="17.86 10 11.97 0 6.71 0 12.6 10"/>
  </g>
</g><g transform="translate(108,306)"><path fill="#000000" fill-rule="evenodd" d="M14.0074653,8.784 C14.0394653,8.528 14.0634653,8.272 14.0634653,8 C14.0634653,7.728 14.0394653,7.472 14.0074653,7.216 L15.6954653,5.896 C15.8474653,5.776 15.8874653,5.56 15.7914653,5.384 L14.1914653,2.616 C14.0954653,2.44 13.8794653,2.376 13.7034653,2.44 L11.7114653,3.24 C11.2954653,2.92 10.8474653,2.656 10.3594653,2.456 L10.0554653,0.336 C10.0314653,0.144 9.86346525,0 9.66346525,0 L6.46346525,0 C6.26346525,0 6.09546525,0.144 6.07146525,0.336 L5.76746525,2.456 C5.27946525,2.656 4.83146525,2.928 4.41546525,3.24 L2.42346525,2.44 C2.23946525,2.368 2.03146525,2.44 1.93546525,2.616 L0.335465255,5.384 C0.231465255,5.56 0.279465255,5.776 0.431465255,5.896 L2.11946525,7.216 C2.08746525,7.472 2.06346525,7.736 2.06346525,8 C2.06346525,8.264 2.08746525,8.528 2.11946525,8.784 L0.431465255,10.104 C0.279465255,10.224 0.239465255,10.44 0.335465255,10.616 L1.93546525,13.384 C2.03146525,13.56 2.24746525,13.624 2.42346525,13.56 L4.41546525,12.76 C4.83146525,13.08 5.27946525,13.344 5.76746525,13.544 L6.07146525,15.664 C6.09546525,15.856 6.26346525,16 6.46346525,16 L9.66346525,16 C9.86346525,16 10.0314653,15.856 10.0554653,15.664 L10.3594653,13.544 C10.8474653,13.344 11.2954653,13.072 11.7114653,12.76 L13.7034653,13.56 C13.8874653,13.632 14.0954653,13.56 14.1914653,13.384 L15.7914653,10.616 C15.8874653,10.44 15.8474653,10.224 15.6954653,10.104 L14.0074653,8.784 L14.0074653,8.784 Z M8,11 C6.34571429,11 5,9.65428571 5,8 C5,6.34571429 6.34571429,5 8,5 C9.65428571,5 11,6.34571429 11,8 C11,9.65428571 9.65428571,11 8,11 L8,11 Z" transform="translate(1 1)"/>
</g><g transform="translate(468,90)"><path fill="#000000" fill-rule="evenodd" d="M4,4 L1.00087166,4 C0.444630861,4 0,4.44882258 0,5.00247329 L0,14.9975267 C0,15.544239 0.448105505,16 1.00087166,16 L14.9991283,16 C15.5553691,16 16,15.5511774 16,14.9975267 L16,5.50123665 L16,6 L4,6 L4,4 Z M2,6 L7,6 L7,9 L2,9 L2,6 Z M2,11 L7,11 L7,14 L2,14 L2,11 Z M9,6 L14,6 L14,9 L9,9 L9,6 Z M9,11 L14,11 L14,14 L9,14 L9,11 Z M12,1.75 L12,1 C12,0.447 11.553,0 11,0 L9,0 L9,5 L11,5 C11.553,5 12,4.553 12,4 L12,3.25 C12,2.836 11.664,2.5 11.25,2.5 C11.664,2.5 12,2.164 12,1.75 Z M10,4 L11,4 L11,3 L10,3 L10,4 Z M10,2 L11,2 L11,1 L10,1 L10,2 Z M13,4 C13,4.553 13.447,5 14,5 L15,5 C15.553,5 16,4.553 16,4 L16,3.25 L15,3.25 L15,4 L14,4 L14,1 L15,1 L15,1.75 L16,1.75 L16,1 C16,0.447 15.553,0 15,0 L14,0 C13.447,0 13,0.447 13,1 L13,4 Z M7,0 L6,0 C5.447,0 5,0.447 5,1 L5,5 L6,5 L6,4 L7,4 L7,5 L8,5 L8,1 C8,0.447 7.553,0 7,0 Z M6,3 L7,3 L7,1 L6,1 L6,3 Z" transform="translate(1 1)"/>
</g><g transform="translate(274,18)"><path fill="#000000" fill-rule="evenodd" d="M3.73243561,3 C3.38662619,3.59780137 2.74028236,4 2,4 C0.8954305,4 8.8817842e-16,3.1045695 8.8817842e-16,2 C8.8817842e-16,0.8954305 0.8954305,0 2,0 C2.74028236,0 3.38662619,0.40219863 3.73243561,1 L10,1 L10,3 L3.73243561,3 Z M4.00183105,8 C3.95133866,8 3.83224929,8.00708433 3.670281,8.0295662 C3.39764759,8.06740886 3.12698029,8.13503441 2.88533098,8.2356602 C2.29317848,8.48223991 2,8.84848947 2,9.5 C2,10.1515105 2.29317848,10.5177601 2.88533098,10.7643398 C3.12698029,10.8649656 3.39764759,10.9325911 3.670281,10.9704338 C3.83224929,10.9929157 3.95133866,11 4.00183105,11 L10.2675644,11 C10.6133738,10.4021986 11.2597176,10 12,10 C13.1045695,10 14,10.8954305 14,12 C14,13.1045695 13.1045695,14 12,14 C11.2597176,14 10.6133738,13.5978014 10.2675644,13 L4,13 L4,11 L4.00183105,11 L4.00183105,13 C3.50631274,13 2.8240725,12.9053022 2.11650007,12.6106602 C0.832508167,12.0759899 0,11.0359895 0,9.5 C-2.66453526e-15,7.96401053 0.832508167,6.92401009 2.11650007,6.3893398 C2.8240725,6.09469779 3.50631274,6 4.00183105,6 L4.00183105,8 L4,8 L4,6 L10,6 L10,8 L4.00183091,8 Z M10.3315501,3.0295662 C10.6041835,3.06740886 10.8748508,3.13503441 11.1165001,3.2356602 C11.7086526,3.48223991 12.0018311,3.84848947 12.0018311,4.5 C12.0018311,5.15151053 11.7086526,5.51776009 11.1165001,5.7643398 C10.8748508,5.86496559 10.6041835,5.93259114 10.3315501,5.9704338 C10.1695818,5.99291567 10.0504924,6 10,6 L10,8 C10.4955183,8 11.1777586,7.90530221 11.885331,7.6106602 C13.1693229,7.07598991 14.0018311,6.03598947 14.0018311,4.5 C14.0018311,2.96401053 13.1693229,1.92401009 11.885331,1.3893398 C11.1777586,1.09469779 10.4955183,1 10,1 L10,3 C10.0504924,3 10.1695818,3.00708433 10.3315501,3.0295662 Z" transform="translate(2 2)"/>
</g><g transform="translate(72,72)"><g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <polygon points="0 0 18 0 18 18 0 18"/>
        <path d="M16.5694737,11 L11.5147368,2 L6.47473684,2 L6.47473684,2.0075 L11.5221053,11 L16.5694737,11 Z M7.32210526,12 L4.79473684,16 L14.4621053,16 L16.9894737,12 L7.32210526,12 Z M1,11.5789474 L3.52,15.9926316 L8.2906993,7.63467097 L5.83368421,3.12 L1,11.5789474 Z" fill="#000000"/>
    </g>
</g><g transform="translate(464,148)"><defs>
    <polygon id="mask-a" points="16 0 0 0 0 16 16 16"/>
  </defs>
  <g fill="none" fill-rule="evenodd" transform="translate(1 1)">
    <mask id="mask-b" fill="#fff">
      <use xlink:href="#mask-a"/>
    </mask>
    <path fill="#000" fill-rule="nonzero" d="M10.5,12.5 L5.5,12.5 L3,8 L5.5,3.5 L10.5,3.5 L13,8 L10.5,12.5 Z M1.77781818,-0.000363636364 C0.795272727,-0.000363636364 0.000363636364,0.795272727 0.000363636364,1.77781818 L0.000363636364,14.2221818 C0.000363636364,15.2047273 0.795272727,15.9996364 1.77781818,15.9996364 L14.2221818,15.9996364 C15.2047273,15.9996364 16.0003636,15.2047273 16.0003636,14.2221818 L16.0003636,1.77781818 C16.0003636,0.795272727 15.2047273,-0.000363636364 14.2221818,-0.000363636364 L1.77781818,-0.000363636364 Z" mask="url(#mask-b)"/>
  </g>
</g><g transform="translate(180,36)"><path fill="#000000" fill-rule="evenodd" d="M2,12 L2,14 L12,14 L12,12 L2,12 Z M2,4 L2,6 L12,6 L12,4 L2,4 Z M0,10 L14,10 L14,8 L0,8 L0,10 Z M0,0 L0,2 L14,2 L14,0 L0,0 Z" transform="translate(2 2)"/>
</g><g transform="translate(364,220)"><polygon fill="#FFFFFF" fill-rule="evenodd" points="16 7 3.83 7 9.42 1.41 8 0 0 8 8 16 9.41 14.59 3.83 9 16 9" transform="translate(4 4)"/>
</g><g transform="translate(500,112)"><path fill="#000000" fill-rule="evenodd" d="M9,2 L7,0 L1,0 C0.45,0 0,0.45 0,1 L0,13 C0,13.55 0.45,14 1,14 L15,14 C15.55,14 16,13.55 16,13 L16,3 C16,2.45 15.55,2 15,2 L9,2 Z" transform="translate(1 2)"/>
</g><g transform="translate(252,324)"><path fill="#4285F4" fill-rule="evenodd" d="M0.01,1 L0,15 C0,15.55 0.44,16 1,16 L11,16 C11.55,16 12,15.55 12,15 L12,5 L7,0 L1,0 C0.45,0 0.01,0.45 0.01,1 Z M7,5 L7,1 L11,5 L7,5 L7,5 Z" transform="translate(3 1)"/>
</g><g transform="translate(36,18)"><g fill="none" fill-rule="evenodd" transform="rotate(90 9 9)">
    <polygon points="0 0 18 0 18 18 0 18"/>
    <path fill="#000" d="M2,16 L4,16 L4,14 L2,14 L2,16 L2,16 Z M8,16 L10,16 L10,14 L8,14 L8,16 L8,16 Z M5,16 L7,16 L7,14 L5,14 L5,16 L5,16 Z M2,13 L4,13 L4,11 L2,11 L2,13 L2,13 Z M2,7 L4,7 L4,5 L2,5 L2,7 L2,7 Z M2,10 L4,10 L4,8 L2,8 L2,10 L2,10 Z M14,10 L16,10 L16,8 L14,8 L14,10 L14,10 Z M14,13 L16,13 L16,11 L14,11 L14,13 L14,13 Z M14,7 L16,7 L16,5 L14,5 L14,7 L14,7 Z M11,16 L13,16 L13,14 L11,14 L11,16 L11,16 Z M14,16 L16,16 L16,14 L14,14 L14,16 L14,16 Z" opacity=".54"/>
    <polygon fill="#000" points="2 2 2 4 16 4 16 2"/>
  </g>
</g><g transform="translate(436,342)"><path fill="#FFFFFF" fill-rule="evenodd" d="M1.00684547,0 C0.450780073,0 1.57717987e-15,0.447449351 1.57717987e-15,1.0068708 C1.57717987e-15,1.0068708 1.57717987e-15,0.795555556 1.57717987e-15,1.77777778 L1.57717987e-15,12.4444444 C1.57717987e-15,13.4266667 -1.20058952e-09,12.9909959 -1.20058952e-09,12.9909959 C-6.73340566e-09,13.5482535 0.443716635,14 0.999807483,14 L5,14 L7,16 L9,14 L12.1307373,14 L12.9927192,14 C13.549025,14 14,13.5505095 14,12.9998043 L14,1.77777778 C14,0.795555556 14,1.0068708 14,1.0068708 C14,0.45079141 13.5500512,0 12.9931545,0 L1.00684547,0 Z M7,12 L5.796875,8.375 L2.625,7 L5.796875,5.625 L7,2 L8.203125,5.625 L11.375,7 L8.203125,8.375 L7,12 Z" transform="translate(2 1)"/>
</g><g transform="translate(382,0)"><rect width="14" height="2" fill="#000000" fill-rule="evenodd" transform="translate(2 8)"/>
</g><g transform="translate(0,54)"><path fill="#000000" fill-rule="evenodd" d="M17.86,10 L11.97,0 L6.71,0 L12.6,10 L17.86,10 L17.86,10 Z M4.71,16 L15.24,16 L17.85,11 L7.29,11 L4.71,16 L4.71,16 Z M5.63,1.45 L0.14,11.45 L2.8,16 L8.28,6.02 L5.63,1.45 L5.63,1.45 Z" transform="translate(0 1)" opacity=".54"/>
</g><g transform="translate(162,234)"><path fill="#FFFFFF" fill-rule="evenodd" d="M14.0074653,8.784 C14.0394653,8.528 14.0634653,8.272 14.0634653,8 C14.0634653,7.728 14.0394653,7.472 14.0074653,7.216 L15.6954653,5.896 C15.8474653,5.776 15.8874653,5.56 15.7914653,5.384 L14.1914653,2.616 C14.0954653,2.44 13.8794653,2.376 13.7034653,2.44 L11.7114653,3.24 C11.2954653,2.92 10.8474653,2.656 10.3594653,2.456 L10.0554653,0.336 C10.0314653,0.144 9.86346525,0 9.66346525,0 L6.46346525,0 C6.26346525,0 6.09546525,0.144 6.07146525,0.336 L5.76746525,2.456 C5.27946525,2.656 4.83146525,2.928 4.41546525,3.24 L2.42346525,2.44 C2.23946525,2.368 2.03146525,2.44 1.93546525,2.616 L0.335465255,5.384 C0.231465255,5.56 0.279465255,5.776 0.431465255,5.896 L2.11946525,7.216 C2.08746525,7.472 2.06346525,7.736 2.06346525,8 C2.06346525,8.264 2.08746525,8.528 2.11946525,8.784 L0.431465255,10.104 C0.279465255,10.224 0.239465255,10.44 0.335465255,10.616 L1.93546525,13.384 C2.03146525,13.56 2.24746525,13.624 2.42346525,13.56 L4.41546525,12.76 C4.83146525,13.08 5.27946525,13.344 5.76746525,13.544 L6.07146525,15.664 C6.09546525,15.856 6.26346525,16 6.46346525,16 L9.66346525,16 C9.86346525,16 10.0314653,15.856 10.0554653,15.664 L10.3594653,13.544 C10.8474653,13.344 11.2954653,13.072 11.7114653,12.76 L13.7034653,13.56 C13.8874653,13.632 14.0954653,13.56 14.1914653,13.384 L15.7914653,10.616 C15.8874653,10.44 15.8474653,10.224 15.6954653,10.104 L14.0074653,8.784 L14.0074653,8.784 Z M8,11 C6.34571429,11 5,9.65428571 5,8 C5,6.34571429 6.34571429,5 8,5 C9.65428571,5 11,6.34571429 11,8 C11,9.65428571 9.65428571,11 8,11 L8,11 Z" transform="translate(1 1)"/>
</g><g transform="translate(234,90)"><path fill="#000000" fill-rule="evenodd" d="M4,6 L4,11 L6,11 L6,1 L7,1 L7,11 L9,11 L9,1 L10,1 L10,0 L4,0 C2.34,0 1,1.34 1,3 C1,4.66 2.34,6 4,6 Z M0,14 L3,11 L3,13 L14,13 L14,15 L3,15 L3,17 L0,14 Z" transform="translate(2 1)"/>
</g><g transform="translate(400,342)"><g fill="#000000" fill-rule="evenodd" transform="translate(2 2)">
    <path d="M0,14 L2,14 L2,12 L0,12 L0,14 L0,14 Z M2,3 L0,3 L0,5 L2,5 L2,3 L2,3 Z M3,14 L5,14 L5,12 L3,12 L3,14 L3,14 Z M11,0 L9,0 L9,2 L11,2 L11,0 L11,0 Z M2,0 L0,0 L0,2 L2,2 L2,0 L2,0 Z M5,0 L3,0 L3,2 L5,2 L5,0 L5,0 Z M0,11 L2,11 L2,9 L0,9 L0,11 L0,11 Z M9,14 L11,14 L11,12 L9,12 L9,14 L9,14 Z M12,0 L12,2 L14,2 L14,0 L12,0 L12,0 Z M12,5 L14,5 L14,3 L12,3 L12,5 L12,5 Z M12,14 L14,14 L14,12 L12,12 L12,14 L12,14 Z M12,11 L14,11 L14,9 L12,9 L12,11 L12,11 Z" opacity=".54"/>
    <polygon points="8 0 6 0 6 6 0 6 0 8 6 8 6 14 8 14 8 8 14 8 14 6 8 6"/>
  </g>
</g><g transform="translate(126,270)"><path fill="#3F51B5" fill-rule="evenodd" d="M15,0 L1,0 C0.5,0 0,0.5 0,1 L0,15 C0,15.5 0.5,16 1,16 L15,16 C15.5,16 16,15.5 16,15 L16,1 C16,0.5 15.5,0 15,0 Z M2,3 L14,3 L14,6 L2,6 L2,3 Z M2,7 L9,7 L9,13 L2,13 L2,7 Z M10,7 L14,7 L14,13 L10,13 L10,7 Z" transform="translate(1 1)"/>
</g><g transform="translate(572,180)"><path fill="#000000" fill-rule="evenodd" d="M15,0 C15.55,0 16,0.45 16,1 L16,15 C16,15.55 15.55,16 15,16 L1,16 C0.45,16 0,15.55 0,15 L0,1 C0,0.45 0.45,0 1,0 L15,0 Z M2,2 L2,14 L14,14 L14,2 L2,2 Z M6,12 L4,12 L4,7 L6,7 L6,12 L6,12 Z M9,12 L7,12 L7,4 L9,4 L9,12 L9,12 Z M12,12 L10,12 L10,8 L12,8 L12,12 L12,12 Z" transform="translate(1 1)"/>
</g><g transform="translate(180,342)"><path fill-rule="evenodd" d="M1,4.00247329 C1,3.44882258 1.44463086,3 2.00087166,3 L15.9991283,3 C16.5518945,3 17,3.45576096 17,4.00247329 L17,13.9975267 C17,14.5511774 16.5553691,15 15.9991283,15 L2.00087166,15 C1.4481055,15 1,14.544239 1,13.9975267 L1,4.00247329 Z M5,11 L5,13 L13,13 L13,11 L5,11 Z M2,5 L2,7 L4,7 L4,5 L2,5 Z M5,5 L5,7 L7,7 L7,5 L5,5 Z M8,5 L8,7 L10,7 L10,5 L8,5 Z M11,5 L11,7 L13,7 L13,5 L11,5 Z M14,5 L14,7 L16,7 L16,5 L14,5 Z M2,8 L2,10 L4,10 L4,8 L2,8 Z M5,8 L5,10 L7,10 L7,8 L5,8 Z M8,8 L8,10 L10,10 L10,8 L8,8 Z M11,8 L11,10 L13,10 L13,8 L11,8 Z M14,8 L14,10 L16,10 L16,8 L14,8 Z"/>
</g><g transform="translate(428,112)"><path fill="#000000" fill-rule="evenodd" d="M11,8 L7,8 L7,12 L11,12 L11,8 L11,8 Z M13,1 L12,1 L12,0 L10,0 L10,1 L4,1 L4,0 L2,0 L2,1 L1,1 C0.45,1 0,1.45 0,2 L0,14 C0,14.55 0.45,15 1,15 L13,15 C13.55,15 14,14.55 14,14 L14,2 C14,1.45 13.55,1 13,1 L13,1 Z M12,13 L2,13 L2,5 L12,5 L12,13 L12,13 Z" transform="translate(2 1)"/>
</g><g transform="translate(144,234)"><path fill="#000000" fill-rule="evenodd" d="M0,0 L0,2 L12,2 L12,0 L0,0 L0,0 Z M2.5,7 L5,7 L5,14 L7,14 L7,7 L9.5,7 L6,3.5 L2.5,7 L2.5,7 Z" transform="translate(3 2)"/>
</g><g transform="translate(328,280)"><path d="M21.99 4c0-1.1-.89-2-1.99-2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4-.01-18zM18 14H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
    <path d="M0 0h24v24H0z" fill="none"/>
</g><g transform="translate(428,130)"><path fill="#000000" fill-rule="evenodd" d="M7,0 C3.134,0 0,3.133 0,7 C0,10.865 3.134,14 7,14 C10.866,14 14,10.865 14,7 C14,3.133 10.866,0 7,0 L7,0 Z M12.2,7 C12.2,8.074 11.874,9.072 11.315,9.9 L4.099,2.684 C4.928,2.125 5.926,1.799 7,1.799 C9.873,1.799 12.2,4.127 12.2,7 L12.2,7 Z M1.8,7 C1.8,5.926 2.126,4.928 2.685,4.098 L9.902,11.315 C9.073,11.874 8.075,12.2 7.001,12.2 C4.127,12.199 1.8,9.873 1.8,7 L1.8,7 Z" transform="translate(2 2)"/>
</g><g transform="translate(162,378)"><defs><linearGradient id="a" x1="50.005%" x2="50.005%" y1="8.586%" y2="100.014%"><stop stop-color="#263238" stop-opacity=".2" offset="0%"/><stop stop-color="#263238" stop-opacity=".02" offset="100%"/></linearGradient><radialGradient id="b" cx="3.168%" cy="2.718%" r="161.248%" fx="3.168%" fy="2.718%" gradientTransform="matrix(1 0 0 .72222 0 .008)"><stop stop-color="#FFF" offset="0%"/><stop stop-color="#FFF" stop-opacity="0" offset="100%"/></radialGradient></defs><g fill="none" fill-rule="evenodd"><path fill="#0F9D58" d="M9.5 2H24l9 9v24.5c0 1.3807119-1.1192881 2.5-2.5 2.5h-21C8.11928813 38 7 36.8807119 7 35.5v-31C7 3.11928813 8.11928813 2 9.5 2z"/><path fill="#263238" fill-opacity=".1" d="M7 35c0 1.3807119 1.11928813 2.5 2.5 2.5h21c1.3807119 0 2.5-1.1192881 2.5-2.5v.5c0 1.3807119-1.1192881 2.5-2.5 2.5h-21C8.11928813 38 7 36.8807119 7 35.5V35z"/><path fill="#FFF" fill-opacity=".2" d="M9.5 2H24v.5H9.5C8.11928813 2.5 7 3.61928813 7 5v-.5C7 3.11928813 8.11928813 2 9.5 2z"/><path fill="url(#a)" fill-rule="nonzero" d="M17.5 8l8.5 8.5V9" transform="translate(7 2)"/><path fill="#87CEAC" d="M24 2l9 9h-6.5C25.1192881 11 24 9.88071187 24 8.5V2z"/><path fill="#F1F1F1" d="M13 18h14v14H13V18zm2 2v2h4v-2h-4zm0 4v2h4v-2h-4zm0 4v2h4v-2h-4zm6-8v2h4v-2h-4zm0 4v2h4v-2h-4zm0 4v2h4v-2h-4z"/><path fill="url(#b)" fill-opacity=".1" d="M2.5 0H17l9 9v24.5c0 1.3807119-1.1192881 2.5-2.5 2.5h-21C1.11928813 36 0 34.8807119 0 33.5v-31C0 1.11928813 1.11928813 0 2.5 0z" transform="translate(7 2)"/></g></g><g transform="translate(90,378)"><defs><linearGradient id="a" x1="50.005%" x2="50.005%" y1="8.586%" y2="100.014%"><stop stop-color="#BF360C" stop-opacity=".2" offset="0%"/><stop stop-color="#BF360C" stop-opacity=".02" offset="100%"/></linearGradient><radialGradient id="b" cx="3.168%" cy="2.718%" r="161.248%" fx="3.168%" fy="2.718%" gradientTransform="matrix(1 0 0 .72222 0 .008)"><stop stop-color="#FFF" offset="0%"/><stop stop-color="#FFF" stop-opacity="0" offset="100%"/></radialGradient></defs><g fill="none" fill-rule="evenodd"><path fill="#F4B400" d="M9.5 2H24l9 9v24.5c0 1.3807119-1.1192881 2.5-2.5 2.5h-21C8.11928813 38 7 36.8807119 7 35.5v-31C7 3.11928813 8.11928813 2 9.5 2z"/><path fill="#BF360C" fill-opacity=".2" d="M7 35c0 1.3807119 1.11928813 2.5 2.5 2.5h21c1.3807119 0 2.5-1.1192881 2.5-2.5v.5c0 1.3807119-1.1192881 2.5-2.5 2.5h-21C8.11928813 38 7 36.8807119 7 35.5V35z"/><path fill="#FFF" fill-opacity=".2" d="M9.5 2H24v.5H9.5C8.11928813 2.5 7 3.61928813 7 5v-.5C7 3.11928813 8.11928813 2 9.5 2z"/><path fill="url(#a)" fill-rule="nonzero" d="M17.5 8l8.5 8.5V9" transform="translate(7 2)"/><path fill="#FADA80" d="M24 2l9 9h-6.5C25.1192881 11 24 9.88071187 24 8.5V2z"/><path fill="#F1F1F1" d="M14.5 18h11c.8284271 0 1.5.6715729 1.5 1.5v11c0 .8284271-.6715729 1.5-1.5 1.5h-11c-.82842712 0-1.5-.6715729-1.5-1.5v-11c0-.8284271.67157288-1.5 1.5-1.5zm.5 4v6h10v-6H15z"/><path fill="url(#b)" fill-opacity=".1" d="M2.5 0H17l9 9v24.5c0 1.3807119-1.1192881 2.5-2.5 2.5h-21C1.11928813 36 0 34.8807119 0 33.5v-31C0 1.11928813 1.11928813 0 2.5 0z" transform="translate(7 2)"/></g></g><g transform="translate(72,108)"><path fill="#000000" fill-rule="evenodd" d="M0,0 L6,0 L6,2 L0,2 L0,0 Z M0,4 L6,4 L6,6 L0,6 L0,4 Z M8,0 L14,0 L14,2 L8,2 L8,0 Z M8,4 L14,4 L14,6 L8,6 L8,4 Z M0,8 L6,8 L6,10 L0,10 L0,8 Z M8,8 L14,8 L14,10 L8,10 L8,8 Z M0,12 L6,12 L6,14 L0,14 L0,12 Z M8,12 L14,12 L14,14 L8,14 L8,12 Z" transform="translate(2 2)"/>
</g><g transform="translate(274,72)"><polygon fill="#000000" fill-rule="evenodd" points="8.44 .44 5 3.88 1.56 .44 .5 1.5 5 6 9.5 1.5" transform="translate(4 6)"/>
</g><g transform="translate(126,198)"><g fill="#000000" fill-rule="evenodd" transform="translate(2 3)">
    <polygon points="5 0 .5 12 5 12 6.12 9 8.37 9 9.49 12 13.99 12 9.5 0" opacity=".3"/>
    <path d="M6.5,0 L4.5,0 L0,12 L2,12 L3.12,9 L7.87,9 L8.99,12 L10.99,12 L6.5,0 L6.5,0 Z M3.88,7 L5.5,2.67 L7.12,7 L3.88,7 L3.88,7 Z"/>
  </g>
</g><g transform="translate(36,378)"><polygon fill="#000000" fill-rule="evenodd" points="8 0 6.59 1.41 12.17 7 0 7 0 9 12.17 9 6.59 14.59 8 16 16 8" transform="translate(4 4)"/>
</g><g transform="translate(18,234)"><polygon fill="#000000" fill-rule="evenodd" points="4 0 4 2 6.58 2 2.92 10 0 10 0 12 8 12 8 10 5.42 10 9.08 2 12 2 12 0" transform="translate(3 3)"/>
</g><g transform="translate(72,54)"><polygon fill="#F4B400" fill-rule="evenodd" points="8 11.953 13 15 11 9.5 15.5 6 10 6 8 .5 6 6 .5 6 5 9.5 3 15" transform="translate(1 1)"/>
</g><g transform="translate(328,184)"><path fill="#F44336" fill-rule="evenodd" d="M12,3 L12,0 L0,0 L0,12 L3,12 L3,3 L12,3 Z M4,4 L16,4 L16,16 L4,16 L4,4 Z" transform="translate(1 1)"/>
</g><g transform="translate(500,202)"><path fill="#000000" fill-rule="evenodd" d="M10,5 L10,4 C10,1.5 8.07,0 6,0 C3.93,0 2,1.5 2,4 L2,5 L1.5,5 C0.67125,5 0,5.67125 0,6.5 L0,13.5 C0,14.32875 0.67125,15 1.5,15 L10.5,15 C11.32875,15 12,14.32875 12,13.5 L12,6.5 C12,5.67125 11.32875,5 10.5,5 L10,5 Z M4,10 C4,8.895 4.895,8 6,8 C7.105,8 8,8.895 8,10 C8,11.105 7.105,12 6,12 C4.895,12 4,11.105 4,10 Z M4,5 L4,4 C4,3 4.7175,2 6,2 C7.2825,2 8,3 8,4 L8,5 L4,5 Z" transform="translate(3 1)"/>
</g><g transform="translate(54,54)"><path fill="#000000" fill-rule="evenodd" d="M4.24826564,3.00916134 C3.49176891,3.00916134 2.39690508,3.00916134 2.10880344,3.00916134 C2.54373837,1.10900675 4.22011554,0 6,0 C8.07,0 10,1.5 10,4 L10,5 L10.5,5 C11.32875,5 12,5.67125 12,6.5 L12,13.5 C12,14.32875 11.32875,15 10.5,15 L1.5,15 C0.67125,15 0,14.32875 0,13.5 L0,6.5 C0,5.67125 0.67125,5 1.5,5 L2,5 L4.5,5 L8,5 L8,4 C8,3 7.2825,2 6,2 C5.15846,2 4.56018701,2.43056109 4.24826564,3.00916134 Z M4,10 C4,8.895 4.895,8 6,8 C7.105,8 8,8.895 8,10 C8,11.105 7.105,12 6,12 C4.895,12 4,11.105 4,10 Z" transform="translate(3 1)"/>
</g><g transform="translate(270,234)"><path fill="#000000" fill-rule="evenodd" d="M0,14 L14,14 L14,12 L0,12 L0,14 Z M0,10 L14,10 L14,8 L0,8 L0,10 Z M0,0 L0,2 L14,2 L14,0 L0,0 Z M0,6 L14,6 L14,4 L0,4 L0,6 Z" transform="translate(2 2)"/>
</g><g transform="translate(364,0)"><path fill="#000000" fill-rule="evenodd" d="M17.86,10 L11.97,0 L6.71,0 L12.6,10 L17.86,10 L17.86,10 Z M4.71,16 L15.24,16 L17.85,11 L7.29,11 L4.71,16 L4.71,16 Z M5.63,1.45 L0.14,11.45 L2.8,16 L8.28,6.02 L5.63,1.45 L5.63,1.45 Z" transform="translate(0 1)" opacity=".54"/>
</g><g transform="translate(202,378)"><path fill="#000000" fill-rule="evenodd" d="M3,6 L1,6 L1,2 L8,2 L8,4 L3,4 L3,6 Z M10,4 L10,2 L17,2 L17,6 L15,6 L15,4 L10,4 Z M10,14 L15,14 L15,12 L17,12 L17,16 L10,16 L10,14 Z M1,12 L3,12 L3,14 L8,14 L8,16 L1,16 L1,12 Z M1,8 L5,8 L5,6 L8,9 L5,12 L5,10 L1,10 L1,8 Z M10,9 L13,6 L13,8 L17,8 L17,10 L13,10 L13,12 L10,9 Z"/>
</g><g transform="translate(90,306)"><polygon fill="#000000" fill-rule="evenodd" points="0 6 1.5 7.5 6 3 10.5 7.5 12 6 6 0" transform="translate(3 5)"/>
</g><g transform="translate(108,90)"><path fill="#000000" fill-rule="evenodd" d="M17.86,10 L11.97,0 L6.71,0 L12.6,10 L17.86,10 L17.86,10 Z M4.71,16 L15.24,16 L17.85,11 L7.29,11 L4.71,16 L4.71,16 Z M5.63,1.45 L0.14,11.45 L2.8,16 L8.28,6.02 L5.63,1.45 L5.63,1.45 Z" transform="translate(0 1)" opacity=".38"/>
</g><g transform="translate(500,72)"><path fill="#000000" fill-rule="evenodd" d="M8,5 L8,0 L4,0 L4,5 L0.75,5 L6,10 L11.25,5 L8,5 Z M1,13 L11,13 L11,11 L1,11 L1,13 Z" transform="translate(3 2)"/>
</g><g transform="translate(454,360)"><polygon fill="#000000" fill-rule="evenodd" points="0 0 0 8 2 8 2 15 7 6 4 6 7 0" transform="translate(6 2)"/>
</g><g transform="translate(428,148)"><path fill="#010101" fill-rule="evenodd" d="M2,12 L2,-8.8817842e-16 L0,-8.8817842e-16 L0,13 L0,14 L14,14 L14,12 L2,12 Z" transform="translate(2 2)"/>
</g><g transform="translate(180,306)"><polygon fill="#4285F4" fill-opacity=".78" fill-rule="evenodd" points="0 5 5 10 10 5 5 0" transform="translate(4 4)"/>
</g><g transform="translate(518,94)"><g fill="#000000" fill-rule="evenodd" transform="translate(2 2)">
    <path d="M6,8 L8,8 L8,6 L6,6 L6,8 L6,8 Z M6,5 L8,5 L8,3 L6,3 L6,5 L6,5 Z M6,11 L8,11 L8,9 L6,9 L6,11 L6,11 Z M6,14 L8,14 L8,12 L6,12 L6,14 L6,14 Z M3,14 L5,14 L5,12 L3,12 L3,14 L3,14 Z M3,2 L5,2 L5,0 L3,0 L3,2 L3,2 Z M3,8 L5,8 L5,6 L3,6 L3,8 L3,8 Z M12,14 L14,14 L14,12 L12,12 L12,14 L12,14 Z M12,8 L14,8 L14,6 L12,6 L12,8 L12,8 Z M12,11 L14,11 L14,9 L12,9 L12,11 L12,11 Z M12,5 L14,5 L14,3 L12,3 L12,5 L12,5 Z M6,2 L8,2 L8,0 L6,0 L6,2 L6,2 Z M12,0 L12,2 L14,2 L14,0 L12,0 L12,0 Z M9,14 L11,14 L11,12 L9,12 L9,14 L9,14 Z M9,8 L11,8 L11,6 L9,6 L9,8 L9,8 Z M9,2 L11,2 L11,0 L9,0 L9,2 L9,2 Z" opacity=".54"/>
    <polygon points="0 14 2 14 2 0 0 0"/>
  </g>
</g><g transform="translate(572,324)"><path fill="#000000" fill-rule="evenodd" d="M11,6 L4,6 L4,8 L11,8 L11,10 L14,7 L11,4 L11,6 Z M0,0 L2,0 L2,14 L0,14 L0,0 Z M7,0 L9,0 L9,4 L7,4 L7,0 Z M7,10 L9,10 L9,14 L7,14 L7,10 Z" transform="translate(2 2)"/>
</g><g transform="translate(198,90)"><path fill="#000000" fill-rule="evenodd" d="M0,1.0093689 C0,0.451909848 0.443353176,0 1.0093689,0 L2.9906311,0 C3.54809015,0 4,0.443353176 4,1.0093689 L4,2.9906311 C4,3.54809015 3.55664682,4 2.9906311,4 L1.0093689,4 C0.451909848,4 0,3.55664682 0,2.9906311 L0,1.0093689 Z M6,1 L14,1 L14,3 L6,3 L6,1 Z M6,7 L14,7 L14,9 L6,9 L6,7 Z M0,7.0093689 C0,6.45190985 0.443353176,6 1.0093689,6 L2.9906311,6 C3.54809015,6 4,6.44335318 4,7.0093689 L4,8.9906311 C4,9.54809015 3.55664682,10 2.9906311,10 L1.0093689,10 C0.451909848,10 0,9.55664682 0,8.9906311 L0,7.0093689 Z" transform="translate(2 4)"/>
</g><g transform="translate(446,220)"><polygon fill="#000000" fill-rule="evenodd" points="0 0 4 4 8 0" transform="translate(5 7)"/>
</g><g transform="translate(274,90)"><path fill="#000000" fill-rule="evenodd" d="M2,10 L4,12 L4,16 L10,16 L10,12 L12,10 L12,6 L2,6 L2,10 Z M6,0 L8,0 L8,3 L6,3 L6,0 Z M12.5,1 L14,2.5 L12,4.5 L10.5,3 L12.5,1 Z M-6.20335912e-15,2.5 L1.5,1 L3.5,3 L2,4.5 L-6.20335912e-15,2.5 Z" transform="translate(2 1)"/>
</g><g transform="translate(328,262)"><path fill="#0F9D58" fill-rule="evenodd" d="M1.00684547,0 C0.450780073,0 1.57717987e-15,0.447449351 1.57717987e-15,1.0068708 C1.57717987e-15,1.0068708 1.57717987e-15,0.795555556 1.57717987e-15,1.77777778 L1.57717987e-15,12.4444444 C1.57717987e-15,13.4266667 -1.20058952e-09,12.9909959 -1.20058952e-09,12.9909959 C-6.73340566e-09,13.5482535 0.443716635,14 0.999807483,14 L5,14 L7,16 L9,14 L12.1307373,14 L12.9927192,14 C13.549025,14 14,13.5505095 14,12.9998043 L14,1.77777778 C14,0.795555556 14,1.0068708 14,1.0068708 C14,0.45079141 13.5500512,0 12.9931545,0 L1.00684547,0 Z M7,12 L5.796875,8.375 L2.625,7 L5.796875,5.625 L7,2 L8.203125,5.625 L11.375,7 L8.203125,8.375 L7,12 Z" transform="translate(2 1)"/>
</g><g transform="translate(292,360)"><path d="M15.2222222,1 L2.77777778,1 C1.79111111,1 1,1.8 1,2.77777778 L1,15.2222222 C1,16.2 1.79111111,17 2.77777778,17 L15.2222222,17 C16.2088889,17 17,16.2 17,15.2222222 L17,2.77777778 C17,1.8 16.2088889,1 15.2222222,1 Z M7.22222222,13.4444444 L2.77777778,9 L4.03111111,7.74666667 L7.22222222,10.9288889 L13.9688889,4.18222222 L15.2222222,5.44444444 L7.22222222,13.4444444 Z"/>
</g><g transform="translate(18,0)"><g fill="#000000" fill-rule="evenodd" transform="translate(2 2)">
    <path d="M3,8 L5,8 L5,6 L3,6 L3,8 L3,8 Z M0,14 L2,14 L2,12 L0,12 L0,14 L0,14 Z M6,14 L8,14 L8,12 L6,12 L6,14 L6,14 Z M6,11 L8,11 L8,9 L6,9 L6,11 L6,11 Z M3,14 L5,14 L5,12 L3,12 L3,14 L3,14 Z M0,11 L2,11 L2,9 L0,9 L0,11 L0,11 Z M6,8 L8,8 L8,6 L6,6 L6,8 L6,8 Z M0,5 L2,5 L2,3 L0,3 L0,5 L0,5 Z M0,8 L2,8 L2,6 L0,6 L0,8 L0,8 Z M12,8 L14,8 L14,6 L12,6 L12,8 L12,8 Z M12,11 L14,11 L14,9 L12,9 L12,11 L12,11 Z M12,5 L14,5 L14,3 L12,3 L12,5 L12,5 Z M6,5 L8,5 L8,3 L6,3 L6,5 L6,5 Z M9,14 L11,14 L11,12 L9,12 L9,14 L9,14 Z M9,8 L11,8 L11,6 L9,6 L9,8 L9,8 Z M12,14 L14,14 L14,12 L12,12 L12,14 L12,14 Z" opacity=".54"/>
    <polygon points="0 0 0 2 14 2 14 0"/>
  </g>
</g><g transform="translate(446,256)"><path fill="#010101" fill-rule="evenodd" d="M2.8875,3.06 C2.8875,2.6025 2.985,2.18625 3.18375,1.8075 C3.3825,1.42875 3.66,1.10625 4.02,0.84 C4.38,0.57375 4.80375,0.3675 5.29875,0.22125 C5.79375,0.075 6.33375,0 6.92625,0 C7.53375,0 8.085,0.0825 8.58,0.25125 C9.075,0.42 9.49875,0.6525 9.85125,0.95625 C10.20375,1.25625 10.47375,1.6125 10.665,2.02875 C10.85625,2.44125 10.95,2.895 10.95,3.38625 L8.6925,3.38625 C8.6925,3.1575 8.655,2.94375 8.58375,2.74875 C8.5125,2.55 8.4,2.38125 8.25,2.2425 C8.1,2.10375 7.9125,1.99125 7.6875,1.91625 C7.4625,1.8375 7.19625,1.8 6.88875,1.8 C6.5925,1.8 6.3375,1.83375 6.11625,1.8975 C5.89875,1.96125 5.71875,2.05125 5.57625,2.1675 C5.43375,2.28375 5.325,2.41875 5.25375,2.5725 C5.1825,2.72625 5.145,2.895 5.145,3.0675 C5.145,3.4275 5.32875,3.73125 5.69625,3.975 C5.71780203,3.98908066 5.73942012,4.00311728 5.76118357,4.01733315 C6.02342923,4.18863185 6.5,4.5 7,5 L4,5 C4,5 3.21375,4.37625 3.17625,4.30875 C2.985,3.9525 2.8875,3.53625 2.8875,3.06 Z M14,6 L0,6 L0,8 L7.21875,8 C7.35375,8.0525 7.51875,8.105 7.63125,8.15375 C7.90875,8.2775 8.12625,8.40875 8.28375,8.53625 C8.44125,8.6675 8.54625,8.81 8.6025,8.96 C8.65875,9.11375 8.685,9.28625 8.685,9.47375 C8.685,9.65 8.65125,9.815 8.58375,9.965 C8.51625,10.11875 8.41125,10.25 8.2725,10.35875 C8.13375,10.4675 7.95375,10.55375 7.74,10.6175 C7.5225,10.68125 7.27125,10.71125 6.97875,10.71125 C6.6525,10.71125 6.35625,10.6775 6.09,10.61375 C5.82375,10.55 5.59875,10.445 5.41125,10.3025 C5.22375,10.16 5.0775,9.9725 4.9725,9.74375 C4.8675,9.515 4.78125,9.17 4.78125,9 L2.55,9 C2.55,9.2525 2.61,9.6875 2.72625,10.025 C2.8425,10.3625 3.0075,10.66625 3.21375,10.9325 C3.42,11.19875 3.6675,11.4275 3.94875,11.6225 C4.23,11.8175 4.53375,11.9825 4.86375,12.11 C5.19375,12.24125 5.535,12.33875 5.89875,12.39875 C6.25875,12.4625 6.6225,12.4925 6.9825,12.4925 C7.5825,12.4925 8.13,12.425 8.6175,12.28625 C9.105,12.1475 9.525,11.94875 9.87,11.69375 C10.215,11.435 10.48125,11.12 10.6725,10.74125 C10.86375,10.3625 10.95375,9.935 10.95375,9.455 C10.95375,9.005 10.875,8.6 10.72125,8.24375 C10.68375,8.1575 10.6425,8.075 10.59375,7.9925 L14,8 L14,6 Z" transform="translate(2 3)"/>
</g><g transform="translate(274,36)"><path fill="#000000" fill-rule="evenodd" d="M13.5,3 L11.25,3 L9.75,0 L8.25,0 L9.75,3 L7.5,3 L6,0 L4.5,0 L6,3 L3.75,3 L2.25,0 L1.5,0 C0.67125,0 0.0075,0.67125 0.0075,1.5 L0,10.5 C0,11.32875 0.67125,12 1.5,12 L13.5,12 C14.32875,12 15,11.32875 15,10.5 L15,0 L12,0 L13.5,3 Z" transform="translate(2 3)"/>
</g><g transform="translate(18,18)"><path fill="#000000" fill-rule="evenodd" d="M11.74223,10.3496798 L14,8.09190988 L14,14 L8.09190988,14 L10.3496798,11.74223 L1.234568e-13,1.39255019 L1.39255019,0 L11.74223,10.3496798 Z" transform="rotate(-180 8 8)"/>
</g><g transform="translate(554,148)"><path fill="#000000" fill-rule="evenodd" d="M16,1 C16,0.45 15.55,0 15,0 L1,0 C0.45,0 0,0.45 0,1 L0,13 C0,13.55 0.45,14 1,14 L12.5,14 L16,17 L16,1 Z M13,11 L3,11 L3,9 L13,9 L13,11 Z M13,8 L3,8 L3,6 L13,6 L13,8 Z M13,5 L3,5 L3,3 L13,3 L13,5 Z" transform="translate(1 1)"/>
</g><g transform="translate(270,324)"><path fill="#000000" fill-rule="evenodd" d="M0.01,1 L0,15 C0,15.55 0.44,16 1,16 L11,16 C11.55,16 12,15.55 12,15 L12,5 L7,0 L1,0 C0.45,0 0.01,0.45 0.01,1 Z M7,5 L7,1 L11,5 L7,5 L7,5 Z" transform="translate(3 1)"/>
</g><g transform="translate(90,342)"><path d="m9.75,2.25c-3.73,0 -6.75,3.02 -6.75,6.75l-2.25,0l2.92,2.92l0.05,0.11l3.03,-3.03l-2.25,0c0,-2.9 2.35,-5.25 5.25,-5.25s5.25,2.35 5.25,5.25s-2.35,5.25 -5.25,5.25c-1.45,0 -2.76,-0.59 -3.71,-1.54l-1.06,1.06c1.22,1.22 2.9,1.98 4.77,1.98c3.73,0 6.75,-3.02 6.75,-6.75s-3.02,-6.75 -6.75,-6.75zm-0.75,3.75l0,3.75l3.21,1.9l0.54,-0.91l-2.62,-1.56l0,-3.18l-1.13,0z"/>
  <path fill="none" d="m0,0l18,0l0,18l-18,0l0,-18z"/>
</g><g transform="translate(144,144)"><path fill="#F4B400" fill-rule="evenodd" d="M16,1 C16,0.45 15.55,0 15,0 L1,0 C0.45,0 0,0.45 0,1 L0,12 C0,12.55 0.45,13 1,13 L12.5,13 L16,16 L16,1 L16,1 Z" transform="translate(1 1)"/>
</g><g transform="translate(36,108)"><path fill="#000000" fill-rule="evenodd" d="M10,6 C11.6575,6 13,4.6575 13,3 C13,1.3425 11.6575,0 10,0 C8.3425,0 7,1.3425 7,3 C7,4.6575 8.3425,6 10,6 L10,6 Z M4,4 L4,2 L2,2 L2,4 L0,4 L0,6 L2,6 L2,8 L4,8 L4,6 L6,6 L6,4 L4,4 Z M4,10.6666667 L4,12 L16,12 L16,10.6666667 C16,8.89333333 12.0025,8 10,8 C7.9975,8 4,8.89333333 4,10.6666667 Z" transform="translate(1 3)"/>
</g><g transform="translate(328,136)"><polygon fill="#000000" fill-rule="evenodd" points="16 7 3.83 7 9.42 1.41 8 0 0 8 8 16 9.41 14.59 3.83 9 16 9" transform="translate(4 4)"/>
</g><g transform="translate(388,160)"><path fill="#000000" fill-rule="evenodd" d="M2,12 L2,2 L5,2 L5,0 L2,0 C0.89,0 0,0.9 0,2 L0,12 C0,13.1 0.89,14 2,14 L12,14 C13.1,14 14,13.1 14,12 L14,9 L12,9 L12,12 L2,12 Z M7,2 L10.59,2 L4.76,7.83 L6.17,9.24 L12,3.41 L12,7 L14,7 L14,0 L7,0 L7,2 Z" transform="translate(2 2)"/>
</g><g transform="translate(108,18)"><path fill="#000000" fill-rule="evenodd" d="M0,1 C0,0.45 0.45,0 1,0 L15,0 C15.55,0 16,0.45 16,1 L16,12 C16,12.55 15.55,13 15,13 L3.5,13 L0,16 L0,1 Z M3,11 L3,8.53 L9.88,1.65 C10.08,1.45 10.39,1.45 10.59,1.65 L12.36,3.42 C12.56,3.62 12.56,3.93 12.36,4.13 L5.47,11 L3,11 Z M7.5,11 L9.5,9 L13,9 L13,11 L7.5,11 Z" transform="translate(1 1)"/>
</g><g transform="translate(180,216)"><path fill="#000000" fill-rule="evenodd" d="M0,0 L14,0 L14,2 L0,2 L0,0 Z M0,4 L6,4 L6,6 L0,6 L0,4 Z M0,8 L2,8 L2,10 L0,10 L0,8 Z M8,4 L14,4 L14,6 L8,6 L8,4 Z M4,8 L6,8 L6,10 L4,10 L4,8 Z M8,8 L10,8 L10,10 L8,10 L8,8 Z M12,8 L14,8 L14,10 L12,10 L12,8 Z" transform="translate(2 4)"/>
</g><g transform="translate(572,126)"><path fill="#000000" fill-rule="evenodd" d="M0,0 L2,0 L2,4 L0,4 L0,0 Z M0,4 L14,4 L14,6 L0,6 L0,4 Z M4,2 L6,2 L6,4 L4,4 L4,2 Z M8,0 L10,0 L10,4 L8,4 L8,0 Z M12,2 L14,2 L14,4 L12,4 L12,2 Z" transform="translate(2 6)"/>
</g><g transform="translate(198,54)"><g fill="none" fill-rule="evenodd" transform="rotate(-180 9 9)">
    <polygon points="0 0 18 0 18 18 0 18"/>
    <path fill="#000" d="M2,16 L4,16 L4,14 L2,14 L2,16 L2,16 Z M8,16 L10,16 L10,14 L8,14 L8,16 L8,16 Z M5,16 L7,16 L7,14 L5,14 L5,16 L5,16 Z M2,13 L4,13 L4,11 L2,11 L2,13 L2,13 Z M2,7 L4,7 L4,5 L2,5 L2,7 L2,7 Z M2,10 L4,10 L4,8 L2,8 L2,10 L2,10 Z M14,10 L16,10 L16,8 L14,8 L14,10 L14,10 Z M14,13 L16,13 L16,11 L14,11 L14,13 L14,13 Z M14,7 L16,7 L16,5 L14,5 L14,7 L14,7 Z M11,16 L13,16 L13,14 L11,14 L11,16 L11,16 Z M14,16 L16,16 L16,14 L14,14 L14,16 L14,16 Z" opacity=".54"/>
    <polygon fill="#000" points="2 2 2 4 16 4 16 2"/>
  </g>
</g><g transform="translate(370,280)"><g fill="none" fill-rule="evenodd">
    <polygon points="0 0 18 0 18 18 0 18"/>
    <path fill="#000000" d="M8,10 L10.5,12.5 L12,11 L10,9 L10,5 L8,5 L8,10 Z M8.9925,1.5 C4.8525,1.5 1.5,4.86 1.5,9 C1.5,13.14 4.8525,16.5 8.9925,16.5 C13.14,16.5 16.5,13.14 16.5,9 C16.5,4.86 13.14,1.5 8.9925,1.5 Z M9,15 C5.685,15 3,12.315 3,9 C3,5.685 5.685,3 9,3 C12.315,3 15,5.685 15,9 C15,12.315 12.315,15 9,15 Z"/>
  </g>
</g><g transform="translate(216,306)"><path fill="#010101" fill-rule="evenodd" d="M2,12 L2,-8.8817842e-16 L0,-8.8817842e-16 L0,13 L0,14 L14,14 L14,12 L2,12 Z" transform="matrix(-1 0 0 1 16 2)"/>
</g><g transform="translate(446,184)"><g fill="#000000" fill-rule="evenodd">
    <path d="M9.47336146,7.49057294e-05 C7.78895769,0.00993937541 6.38763929,0.954694653 6.06830697,2.20664456 C5.95295215,2.51979542 5.9000001,2.72000003 5.9000001,2.72000003 C5.9000001,2.72000003 5.83519774,2.97603987 5.82999992,3 L5.4000001,5 L4,5 L4,6 L5.30000019,6 L3.77111816,13.5142822 C3.77111816,13.5142822 3.20471191,16.1555176 2.34106445,16.697876 C1.86203272,16.9987011 1.28420828,17.0434054 1.10778809,17.0002441 C1.61537673,16.9339541 2,16.5118938 2,16 C2.00129865,15.9731602 1.99779672,15.8833762 1.99000431,15.8580638 C1.92561788,15.4049216 1.55747359,15.049577 1.09831297,15.0047715 C1.0828693,15.0016337 1.06828346,15 1.0546875,15 C1.0172438,15.0001094 1.00863468,15 1,15 C0.44771525,15 0,15.4477153 0,16 C0,16.0307214 0.00138534577,16.0611193 0.00409697465,16.0911345 C0.00136565662,16.133227 0,16.1764165 0,16.2207031 C0,16.4812866 0.041806143,16.719484 0.125418993,16.9352965 L0.157775879,17.0002441 C0.157775879,17.0002441 0.498168945,17.9716187 2.01452637,17.9716187 C3.53088379,17.9716187 4.26623535,17.0150146 4.81488037,15.9563599 C5.14147315,15.3261728 5.38030073,14.3820163 5.52012378,13.7209903 L5.64728783,13.0575346 C5.65870849,12.9903589 5.66448975,12.9530029 5.66448975,12.9530029 L7,6 L10,6 L10,5 L7.28428531,5 L7.36000045,4.62953661 L7.53528358,3.70156713 C7.61089628,3.30975584 7.70025541,2.96950377 7.80336365,2.68080071 C7.90647188,2.39209765 8.0336368,2.15323382 8.18486221,1.96420206 C8.33608762,1.77517029 8.51652433,1.63597626 8.72617774,1.54661579 C8.93583115,1.45725532 9.18844254,1.41257576 9.48401948,1.41257576 C9.64211877,1.41257576 9.78646813,1.42632332 9.9170719,1.45381885 C9.99447966,1.47011522 10.0658501,1.4870151 10.131184,1.50451869 C10.0477065,1.65057964 10,1.81971859 10,2 C10,2.55228475 10.4477153,3 11,3 C11.457729,3 11.8436297,2.69246635 11.9623514,2.27274965 C11.9871041,2.21708163 12,2.12871738 12,2 C12,1.2154012 11.491731,0.536324697 10.7519032,0.208734959 C10.6774691,0.172852789 10.5949024,0.141523674 10.5031814,0.114805726 C10.2676987,0.0404663358 10.0141658,1.13686838e-13 9.75,1.13686838e-13 C9.71513575,1.13686838e-13 9.68113145,0.000774776085 9.64795887,0.00228742319 C9.59159775,0.000807963799 9.53341957,6.92664413e-05 9.47336146,7.49057294e-05 Z"/>
    <path d="M8,13 L8,14 L11.0040283,14 L11.0028687,13 L10.3999996,13 L12.75,10.5 L14,13 L13.5017395,13 L13.5,14 L16.75,14 L16.75,13 L16.25,13 C15.9738576,13 15.6608113,12.7961402 15.5492527,12.5411491 L14,9 L16.8771837,6.34413815 C17.0830844,6.1540759 17.4709682,6 17.7460842,6 L18,6 L18,5 L14.9978638,5 L15,6 L16.0369873,6 L13.5017395,8.30000019 L12.2235107,6 L12.75,6 L12.75,5 L9.5,5 L9.5,6 L10,6 C10.2761424,6 10.6042297,6.20845938 10.7216387,6.44327742 L12.25,9.5 L9.33556607,12.6386212 C9.15023805,12.8382052 8.76806641,13 8.5,13 L8,13 Z"/>
  </g>
</g><g transform="translate(352,298)"><polygon fill="#000000" fill-rule="evenodd" points="-2 2 2 6 6 2" transform="rotate(-90 8 3)"/>
</g><g transform="translate(364,324)"><path fill="#000000" fill-rule="evenodd" d="M10.5737085,2 L5,2 L5,0 L14,0 L14,1 L14,9 L12,9 L12,3.40213561 L1.70206527,13.7000703 L0.287851709,12.2858568 L10.5737085,2 Z" transform="translate(2 2)"/>
</g><g transform="translate(162,252)"><defs>
    <polygon id="shapes_callout_a" points="1 1 17 1 17 14 10 14 7 17 4 14 1 14"/>
    <mask id="shapes_callout_b" width="16" height="16" x="0" y="0" fill="white">
      <use xlink:href="#shapes_callout_a"/>
    </mask>
  </defs>
  <use fill="none" fill-rule="evenodd" stroke="#000000" stroke-width="2" mask="url(#shapes_callout_b)" xlink:href="#shapes_callout_a"/>
</g><g transform="translate(328,220)"><path fill="#0F9D58" fill-rule="evenodd" d="M7,14 C10.8659932,14 14,10.8659932 14,7 C14,3.13400675 10.8659932,0 7,0 C3.13400675,0 0,3.13400675 0,7 C0,10.8659932 3.13400675,14 7,14 Z M2.25,7.5 L3.3075,6.4425 L5.5,8.6275 L10.6925,3.435 L11.75,4.5 L5.5,10.75 L2.25,7.5 Z" transform="translate(2 2)"/>
</g><g transform="translate(446,274)"><path fill="#000000" fill-rule="evenodd" d="M13,6 L11,6 L11,7 L13,7 L13,8 L12,8 C11.448,8 11,8.4475 11,9 L11,11 L14,11 L14,10 L12,10 L12,9 L13,9 C13.5525,9 14,8.5525 14,8 L14,7 C14,6.4475 13.5525,6 13,6 Z M5,6.5 L8,11 L9.88503367,11 L6.5,5.5 L10,0 L8,0 L5,4.5 L2,0 L0,0 L3.5,5.5 L0,11 L2,11 L5,6.5 Z" transform="translate(2 4)"/>
</g><g transform="translate(36,360)"><path fill="#000000" fill-rule="evenodd" d="M0,1.00684547 C0,0.450780073 0.455664396,0 0.995397568,0 L9.00460243,0 C9.55434533,0 10,0.449948758 10,1.00684547 L10,14 L5,12 L0,14 L0,1.00684547 Z" transform="translate(4 2)"/>
</g><g transform="translate(382,184)"><path fill="#000000" d="M6,12 C8.76,12 11,9.76 11,7 L11,0 L9,0 L9,7 C9,8.75029916 7.49912807,10 6,10 C4.50087193,10 3,8.75837486 3,7 L3,0 L1,0 L1,7 C1,9.76 3.24,12 6,12 Z M0,13 L0,15 L12,15 L12,13 L0,13 Z" transform="translate(3 3)"/>
</g><g transform="translate(256,378)"><g fill="none" fill-rule="evenodd">
    <path d="M0,0 L18,0 L18,18 L0,18 L0,0 Z M0,0 L18,0 L18,18 L0,18 L0,0 Z M0,0 L18,0 L18,18 L0,18 L0,0 Z M0,0 L18,0 L18,18 L0,18 L0,0 Z"/>
    <path fill="#000" fill-rule="nonzero" d="M9,5.25 C11.07,5.25 12.75,6.93 12.75,9 C12.75,9.4875 12.6525,9.945 12.48,10.3725 L14.67,12.5625 C15.8025,11.6175 16.695,10.395 17.2425,9 C15.945,5.7075 12.7425,3.375 8.9925,3.375 C7.9425,3.375 6.9375,3.5625 6.0075,3.9 L7.6275,5.52 C8.055,5.3475 8.5125,5.25 9,5.25 Z M1.5,3.2025 L3.21,4.9125 L3.555,5.2575 C2.31,6.225 1.335,7.515 0.75,9 C2.0475,12.2925 5.25,14.625 9,14.625 C10.1625,14.625 11.2725,14.4 12.285,13.995 L12.6,14.31 L14.7975,16.5 L15.75,15.5475 L2.4525,2.25 L1.5,3.2025 Z M5.6475,7.35 L6.81,8.5125 C6.7725,8.67 6.75,8.835 6.75,9 C6.75,10.245 7.755,11.25 9,11.25 C9.165,11.25 9.33,11.2275 9.4875,11.19 L10.65,12.3525 C10.1475,12.6 9.5925,12.75 9,12.75 C6.93,12.75 5.25,11.07 5.25,9 C5.25,8.4075 5.4,7.8525 5.6475,7.35 L5.6475,7.35 Z M8.88,6.765 L11.2425,9.1275 L11.2575,9.0075 C11.2575,7.7625 10.2525,6.7575 9.0075,6.7575 L8.88,6.765 Z"/>
  </g>
</g><g transform="translate(500,94)"><g fill="none" fill-rule="evenodd">
    <polygon points="0 0 18 0 18 18 0 18"/>
    <path fill="#000" d="M8,16 L10,16 L10,14 L8,14 L8,16 L8,16 Z M5,4 L7,4 L7,2 L5,2 L5,4 L5,4 Z M5,16 L7,16 L7,14 L5,14 L5,16 L5,16 Z M2,7 L4,7 L4,5 L2,5 L2,7 L2,7 Z M2,16 L4,16 L4,14 L2,14 L2,16 L2,16 Z M2,4 L4,4 L4,2 L2,2 L2,4 L2,4 Z M2,13 L4,13 L4,11 L2,11 L2,13 L2,13 Z M14,13 L16,13 L16,11 L14,11 L14,13 L14,13 Z M14,16 L16,16 L16,14 L14,14 L14,16 L14,16 Z M14,7 L16,7 L16,5 L14,5 L14,7 L14,7 Z M14,2 L14,4 L16,4 L16,2 L14,2 L14,2 Z M8,4 L10,4 L10,2 L8,2 L8,4 L8,4 Z M11,4 L13,4 L13,2 L11,2 L11,4 L11,4 Z M11,16 L13,16 L13,14 L11,14 L11,16 L11,16 Z" opacity=".54"/>
    <polygon fill="#000" points="2 10 16 10 16 8 2 8"/>
  </g>
</g><g transform="translate(180,252)"><path fill="#000000" fill-rule="evenodd" d="M0,0 L12,0 L12,12 L0,12 L0,0 Z M2,2 L12,2 L12,12 L2,12 L2,2 Z M4,4 L8,4 L8,8 L4,8 L4,4 Z M8,5 L12,5 L12,7 L8,7 L8,5 Z M7,8 L7,12 L5,12 L5,8 L7,8 Z" transform="translate(3 3)"/>
</g><g transform="translate(252,306)"><path fill="#000000" fill-rule="evenodd" d="M9.5,7 L7,7 L7,0 L5,0 L5,7 L2.5,7 L6,10.5 L9.5,7 L9.5,7 Z M0,12 L0,14 L12,14 L12,12 L0,12 L0,12 Z" transform="translate(3 2)"/>
</g><g transform="translate(162,180)"><path fill="#000000" fill-rule="evenodd" d="M8,0 C3.58,0 0,3.58 0,8 C0,12.42 3.58,16 8,16 C12.42,16 16,12.42 16,8 C16,3.58 12.42,0 8,0 L8,0 Z M12,10.87 L10.87,12 L8,9.13 L5.13,12 L4,10.87 L6.87,8 L4,5.13 L5.13,4 L8,6.87 L10.87,4 L12,5.13 L9.13,8 L12,10.87 L12,10.87 Z" transform="translate(1 1)"/>
</g><g transform="translate(382,324)"><path fill="#000000" fill-rule="evenodd" d="M11,11 L13,11 L13,11.5 L12,11.5 L12,12.5 L13,12.5 L13,13 L11,13 L11,14 L14,14 L14,10 L11,10 L11,11 Z M11,6 L12.8,6 L11,8.1 L11,9 L14,9 L14,8 L12.2,8 L14,5.9 L14,5 L11,5 L11,6 Z M12,4 L13,4 L13,0 L11,0 L11,1 L12,1 L12,4 Z M0,1 L0,3 L9,3 L9,1 L0,1 Z M0,13 L9,13 L9,11 L0,11 L0,13 Z M0,8 L9,8 L9,6 L0,6 L0,8 Z" transform="translate(2 2)"/>
</g><g transform="translate(274,54)"><path fill="#000000" fill-rule="evenodd" d="M0,11 L2,11 L2,11.5 L1,11.5 L1,12.5 L2,12.5 L2,13 L0,13 L0,14 L3,14 L3,10 L0,10 L0,11 Z M0,6 L1.8,6 L0,8.1 L0,9 L3,9 L3,8 L1.2,8 L3,5.9 L3,5 L0,5 L0,6 Z M1,4 L2,4 L2,0 L0,0 L0,1 L1,1 L1,4 Z M5,1 L5,3 L14,3 L14,1 L5,1 Z M5,13 L14,13 L14,11 L5,11 L5,13 Z M5,8 L14,8 L14,6 L5,6 L5,8 Z" transform="translate(2 2)"/>
</g><g transform="translate(54,36)"><path fill="#000000" fill-rule="evenodd" d="M0,8.75 L9.01409691,13 L9.01409691,11 L8,10.5 L8,5.5 L9,5 L9.01409691,3 L0,7.25 L0,8.75 Z M2,8 L6.02,6.13 L6.02,9.87 L2,8 Z M10,3 L12,3 L12,15 L14,15 L14,3 L16,3 L13,0 L10,3 Z" transform="translate(1 1)"/>
</g><g transform="translate(256,396)"><path fill="#000000" fill-rule="evenodd" d="M13,0 L1,0 C0.45,0 0,0.45 0,1 L0,13 C0,13.55 0.45,14 1,14 L13,14 C13.55,14 14,13.55 14,13 L14,1 C14,0.45 13.55,0 13,0 L13,0 Z M11,11 L9,11 L9,8 L11,8 L11,11 L11,11 Z M6,11 L6,3 L8,3 L8,11 L6,11 Z M3,11 L3,6 L5,6 L5,11 L3,11 Z" transform="translate(2 2)"/>
</g><g transform="translate(234,288)"><path fill="#000000" fill-rule="evenodd" d="M14,0 L0,0 L0,2 L14,2 L14,0 Z M0,12 L4,12 L4,10 L0,10 L0,12 Z M11.5,5 L0,5 L0,7 L11.75,7 C12.58,7 13.25,7.67 13.25,8.5 C13.25,9.33 12.58,10 11.75,10 L9,10 L9,8 L6,11 L9,14 L9,12 L11.5,12 C13.43,12 15,10.43 15,8.5 C15,6.57 13.43,5 11.5,5 Z" transform="translate(2 3)"/>
</g><g transform="translate(90,162)"><path fill="#000000" fill-rule="evenodd" d="M13,14 L3,14 L3,11 L0,11 L0,6.00591905 C0,4.89808055 0.894513756,4 1.99406028,4 L14.0059397,4 C15.1072288,4 16,4.88655484 16,6.00591905 L16,11 L13,11 L13,14 Z M5,9 L11,9 L11,12 L5,12 L5,9 Z M3,0 L13,0 L13,3 L3,3 L3,0 Z M12,6 L14,6 L14,8 L12,8 L12,6 Z" transform="translate(1 2)"/>
</g><g transform="translate(382,396)"><path fill="#FFFFFF" fill-rule="evenodd" d="M7.99905882,0 C4.32752941,0 1.23670588,2.49323077 0,6 C1.23670588,9.50676923 4.32752941,12 7.99905882,12 C11.6724706,12 14.7595294,9.50676923 16,6 C14.7595294,2.49323077 11.6724706,0 7.99905882,0 Z M8,10.5 C10.4852814,10.5 12.5,8.48528137 12.5,6 C12.5,3.51471863 10.4852814,1.5 8,1.5 C5.51471863,1.5 3.5,3.51471863 3.5,6 C3.5,8.48528137 5.51471863,10.5 8,10.5 Z M8,8.5 C9.38071187,8.5 10.5,7.38071187 10.5,6 C10.5,4.61928813 9.38071187,3.5 8,3.5 C6.61928813,3.5 5.5,4.61928813 5.5,6 C5.5,7.38071187 6.61928813,8.5 8,8.5 Z" transform="translate(1 3)"/>
</g><g transform="translate(90,72)"><!-- Generator: Sketch 42 (36781) - http://www.bohemiancoding.com/sketch -->
    <title>diagram_icon_18dp</title>
    <desc>Created with Sketch.</desc>
    <defs/>
    <g id="R4-TT" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="diagram_icon_18dp" stroke="#000000">
            <path d="M3.87147764,7.29634576 C3.83986478,7.04746451 3.82352941,6.79330535 3.82352941,6.53501401 C3.82352941,3.47811018 6.11161763,1 8.93411749,1 C11.7566173,1 14.0447056,3.47811018 14.0447056,6.53501401 C14.0447056,6.7730233 14.0308349,7.00752387 14.0039213,7.23761927 C15.7712236,8.04150177 17,9.82263098 17,11.8907563 C17,14.7125137 14.7125137,17 11.8907563,17 C10.8177918,17 9.82207637,16.6692576 9,16.1041629 C8.17792363,16.6692576 7.18220818,17 6.1092437,17 C3.28748632,17 1,14.7125137 1,11.8907563 C1,9.87159112 2.17128577,8.12599438 3.87147764,7.29634576 Z M11.2034468,11.495781 C12.7029812,10.6895952 13.7849896,9.10935699 14.0039213,7.23761927 C13.3599102,6.9446817 12.6443884,6.78151261 11.8907563,6.78151261 C10.8177918,6.78151261 9.82207637,7.11225499 9,7.6773497 C10.2380498,8.52838427 11.0822782,9.91094076 11.2034468,11.495781 Z M3.87147764,7.29634576 C4.11260673,9.19470304 5.24257605,10.7859928 6.79193371,11.5617758 C6.78502142,11.6705463 6.78151261,11.7802415 6.78151261,11.8907563 C6.78151261,13.6395492 7.66012217,15.1831318 9,16.1041629 C10.3398778,15.1831318 11.2184874,13.6395492 11.2184874,11.8907563 C11.2184874,11.7578516 11.2134128,11.6261321 11.2034468,11.495781 C10.5197272,11.8633652 9.74920604,12.070028 8.93411749,12.070028 C8.16916636,12.070028 7.44346846,11.8880076 6.79193371,11.5617758 C6.89440201,9.94935653 7.74479562,8.5401763 9,7.6773497 C8.17792363,7.11225499 7.18220818,6.78151261 6.1092437,6.78151261 C5.30665151,6.78151261 4.54728204,6.9665711 3.87147764,7.29634576 Z" id="circles"/>
        </g>
    </g>
</g><g transform="translate(162,162)"><path fill="#000000" fill-rule="evenodd" d="M7,8 L7,11 L9,11 L9,8 L12,8 L12,6 L9,6 L9,3 L7,3 L7,6 L4,6 L4,8 L7,8 Z M16,1 L16,17 L12.5,14 L1,14 C0.45,14 0,13.55 0,13 L0,1 C0,0.45 0.45,0 1,0 L15,0 C15.55,0 16,0.45 16,1 Z" transform="translate(1 1)"/>
</g><g transform="translate(500,18)"><path fill="#000000" fill-rule="evenodd" d="M2,11.1724719 L2,8 L4,8 L4,5 L5,5 L5,6 L13,6 L13,5 L14,5 L14,15 L13,15 L13,17 L6.31048584,17 L9.5,17 C10.88,17 12,15.88 12,14.5 C12,13.18 10.975,12.11 9.675,12.02 C9.335,10.295 7.82,9 6,9 C4.555,9 3.3,9.82 2.675,11.02 C2.44084231,11.0448938 2.21479402,11.0967787 2,11.1724719 L2,4.00684547 C2,3.44994876 2.4476813,2.99926779 2.99992417,2.99836457 L7.0005835,2.99182129 C7.00058365,2.99182129 7,0.991821289 9,0.991821289 C11,0.991821289 11.0394287,2.99182129 11,3 L15.0001925,3 C15.5562834,3 16,3.44510135 16,3.99416169 C16,3.99416169 16,3.66307129 16,4.49182129 L16,15.9975471 C16,16.5548273 15.551657,17 14.9985978,17 C14.9985978,17 15.32875,17 14.5,17 L3.55749512,17 C2.72874512,17 2.99935535,17 2.99935535,17 C2.99905582,17 2.99875633,16.9999999 2.99845687,16.9999996 C1.34416402,16.9991639 0,15.6544856 0,14 C0,12.6953811 0.834249959,11.5832815 2,11.1724719 Z M8,3 L8,4 L10,4 L10,3 L8,3 Z" transform="translate(1)"/>
</g><g transform="translate(36,252)"><path fill="#000000" fill-rule="evenodd" d="M6,5 L6,2 L5,2 L5,5 L2,5 L2,6 L5,6 L5,12 L6,12 L6,6 L12,6 L12,5 L6,5 Z M0,1.00684547 C0,0.450780073 0.449948758,0 1.00684547,0 L12.9931545,0 C13.5492199,0 14,0.449948758 14,1.00684547 L14,12.9931545 C14,13.5492199 13.5500512,14 12.9931545,14 L1.00684547,14 C0.450780073,14 0,13.5500512 0,12.9931545 L0,1.00684547 Z M2,2 L12,2 L12,12 L2,12 L2,2 Z" transform="translate(2 2)"/>
</g><g transform="translate(572,54)"><path fill="#000000" fill-rule="evenodd" d="M9,14 L18,14 L18,12 L9,12 L9,14 L9,14 Z M9,2 L9,4 L18,4 L18,2 L9,2 L9,2 Z M7.5,4 L4,0.5 L0.5,4 L3,4 L3,12 L0.5,12 L4,15.5 L7.5,12 L5,12 L5,4 L7.5,4 L7.5,4 Z M9,9 L18,9 L18,7 L9,7 L9,9 L9,9 Z" transform="translate(0 1)"/>
</g><g transform="translate(162,90)"><g fill="none" fill-rule="evenodd" transform="translate(0 1)">
    <polygon fill="#4285F4" points="4.71 16 15.24 16 17.85 11 7.29 11"/>
    <polygon fill="#0F9D58" points="5.63 1.45 .14 11.45 2.8 16 8.28 6.02 5.63 1.45"/>
    <polygon fill="#FFC107" points="17.86 10 11.97 0 6.71 0 12.6 10"/>
  </g>
</g><g transform="translate(464,292)"><path fill="#000000" fill-rule="evenodd" d="M1.9,4 C1.9,2.84 2.84,1.9 4,1.9 L8,1.9 L8,0 L4,0 C1.79,0 0,1.79 0,4 C0,6.21 1.79,8 4,8 L8,8 L8,6.1 L4,6.1 C2.84,6.1 1.9,5.16 1.9,4 L1.9,4 Z M14,0 L10,0 L10,1.9 L14,1.9 C15.16,1.9 16.1,2.84 16.1,4 C16.1,5.16 15.16,6.1 14,6.1 L10,6.1 L10,8 L14,8 C16.21,8 18,6.21 18,4 C18,1.79 16.21,0 14,0 L14,0 Z M6,5 L12,5 L12,3 L6,3 L6,5 L6,5 Z" transform="translate(0 5)"/>
</g><g transform="translate(500,54)"><path fill="#000000" fill-rule="evenodd" d="M4,3 L12.0037973,3 C14.2108391,3 16,4.79535615 16,7 C16,8.62283851 15.0346747,10.0199573 13.6472354,10.6472354 L11.9999825,8.99998249 C13.1026715,8.99540428 14,8.1017358 14,7 C14,5.88772964 13.1007504,5 11.9914698,5 L8,5 L8,5 L9,5 L9,3 L6,3 L4,3 L5,4 L13.5,12.5 L12.5,13.5 L10,11 L3.99620271,11 C1.78916089,11 8.8817842e-16,9.20464385 8.8817842e-16,7 C8.8817842e-16,5.37716149 0.965325286,3.98004273 2.3527646,3.3527646 L0.5,1.5 L1.5,0.5 L4,3 Z M8,9 L4.0085302,9 C2.8992496,9 2,8.11227036 2,7 C2,5.8982642 2.8973285,5.00459572 4.00001767,5.00001767 L5,6 L5,8 L7,8 L8,9 L7,9 L7,11 L9,11 L9,10 L10,11 Z M9,6 L11,6 L11,8 L9,6 Z" transform="translate(1 2)"/>
</g><g transform="translate(180,270)"><polygon fill="#000000" fill-rule="evenodd" points="11.53 1.53 10.47 .47 6 4.94 1.53 .47 .47 1.53 4.94 6 .47 10.47 1.53 11.53 6 7.06 10.47 11.53 11.53 10.47 7.06 6" transform="translate(3 3)"/>
</g><g transform="translate(346,0)"><path fill="#000000" fill-rule="evenodd" d="M11.535,13 L10.49,13 L11,13 L11,11.6376102 L11,10 L8,10 L8,8 L11,8 L11,5 L13,5 L13,8 L16,8 L16,10 L13,10 L13,13 L11.535,13 Z M7,8 L7,10 L3.215,10 L2.09,13 L0,13 L5.11,0 L6.965,0 L9.71384615,7 L7.73033937,7 L6.035,2.475 L3.965,8 L3.965,8 L7,8 Z" transform="translate(1 2)"/>
</g><g transform="translate(54,342)"><path fill="#000000" fill-rule="evenodd" d="M8,6 L8,0 L0,0 L0,14 L8,14 L8,8 L10,8 L10,10.5 L13.5,7 L10,3.5 L10,6 L8,6 Z M6,6 L6,2 L2,2 L2,12 L6,12 L6,8 L5,8 L5,6 L6,6 Z M8,6 L8,4 L6,4 L6,6 L8,6 Z M8,8 L8,10 L6,10 L6,8 L8,8 Z" transform="translate(2 2)"/>
</g><g transform="translate(216,288)"><path fill="#000000" fill-rule="evenodd" d="M11.5656391,4.43436088 L9,7 L16,7 L16,0 L13.0418424,2.95815758 C11.5936787,1.73635959 9.72260775,1 7.67955083,1 C4.22126258,1 1.25575599,3.10984908 0,6 L2,7 C2.93658775,4.60974406 5.12943697,3.08011229 7.67955083,3 C9.14881247,3.0528747 10.4994783,3.57862053 11.5656391,4.43436088 Z" transform="matrix(-1 0 0 1 17 5)"/>
</g><g transform="translate(54,234)"><polygon fill="#4285F4" fill-opacity=".78" fill-rule="evenodd" points="0 0 6 6 12 0" transform="translate(3 7)"/>
</g><g transform="translate(54,324)"><path fill="#673AB7" fill-rule="evenodd" d="M15,0 L1,0 C0.45,0 0,0.45 0,1 L0,15 C0,15.55 0.45,16 1,16 L15,16 C15.55,16 16,15.55 16,15 L16,1 C16,0.45 15.55,0 15,0 L15,0 Z M3,4 L5,4 L5,6 L3,6 L3,4 L3,4 Z M3,7 L5,7 L5,9 L3,9 L3,7 L3,7 Z M3,10 L5,10 L5,12 L3,12 L3,10 L3,10 Z M13,12 L6,12 L6,10 L13,10 L13,12 L13,12 Z M13,9 L6,9 L6,7 L13,7 L13,9 L13,9 Z M13,6 L6,6 L6,4 L13,4 L13,6 L13,6 Z" transform="translate(1 1)"/>
</g><g transform="translate(536,130)"><g fill="none" fill-rule="evenodd">
    <polygon points="0 0 18 0 18 18 0 18"/>
    <path fill="#000000" d="M15.4998938,16 C16.604522,16 17.0549708,15.2146543 16.5166382,14.2646557 L9.98336176,2.73534428 C9.44026606,1.7769401 8.55497079,1.78534566 8.01663824,2.73534428 L1.48336176,14.2646557 C0.940266055,15.2230599 1.39005841,16 2.50010618,16 L15.4998938,16 Z M10,14 L8,14 L8,12 L10,12 L10,14 L10,14 Z M10,11 L8,11 L8,7 L10,7 L10,11 L10,11 Z"/>
  </g>
</g><g transform="translate(428,220)"><path fill="#F4B400" fill-rule="evenodd" d="M15,0 L1,0 C0.45,0 0,0.45 0,1 L0,15 C0,15.55 0.45,16 1,16 L15,16 C15.55,16 16,15.55 16,15 L16,1 C16,0.45 15.55,0 15,0 Z M14,12 L2,12 L2,4 L14,4 L14,12 Z" transform="translate(1 1)"/>
</g><g transform="translate(238,144)"><path fill="#000000" fill-rule="evenodd" d="M13,3 L14.9975267,3 C15.5511774,3 16,3.4463114 16,3.99754465 L16,12 L5.00247329,12 C4.44882258,12 4,11.5536886 4,11.0024554 L4,10 L13,10 L13,3 Z M13,12 L16,12 L16,15 L13,12 Z M0,0.997544646 C0,0.446615951 0.455760956,0 1.00247329,0 L10.9975267,0 C11.5511774,0 12,0.446311399 12,0.997544646 L12,8.00245535 C12,8.55338405 11.544239,9 10.9975267,9 L0,9 L0,0.997544646 Z M3,9 L0,9 L0,12 L3,9 Z" transform="translate(1 2)"/>
</g><g transform="translate(36,126)"><path fill="#010101" fill-rule="evenodd" d="M2,4 L2,6 L13,6 L13,7 L2,7 L2,9 L13,9 L13,10 L2,10 L2,12 L14,12 L14,14 L0,14 L0,13 L0,0 L2,0 L2,3 L13,3 L13,4 L2,4 Z" transform="translate(2 2)"/>
</g><g transform="translate(126,90)"><path fill="#000000" fill-rule="evenodd" d="M0,0 L2,0 L2,14 L0,14 L0,0 Z M12,0 L14,0 L14,14 L12,14 L12,0 Z M4,3 L4,5 L7.75,5 C8.58,5 9.25,5.67 9.25,6.5 C9.25,7.33 8.58,8 7.75,8 L6,8 L6,6 L3,9 L6,12 L6,10 L7.5,10 C9.43,10 11,8.43 11,6.5 C11,4.57 9.43,3 7.5,3 L4,3 Z" transform="translate(2 2)"/>
</g><g transform="translate(518,220)"><path fill="#000000" fill-rule="evenodd" d="M14.4926407,0.219669914 L5.11350781,3.58839515 L6.52772138,5.00260871 L7.73977093,4.49766594 L11.2753048,8.03319985 L10.7803301,9.23528137 L12.1845756,10.659463 L15.5533009,1.28033009 L14.4926407,0.219669914 Z M13.6087572,2.16421356 L12.0884776,6.3290725 L9.44389827,3.68449314 L13.6087572,2.16421356 Z M12,11.7573593 L10.5857864,13.1715729 L2.10050506,4.6862915 L0.686291501,6.10050506 L9.17157288,14.5857864 L7.75735931,16 L12,16 L12,11.7573593 Z" transform="translate(1 1)"/>
</g><g transform="translate(144,36)"><defs>
    <polygon id="shapes_arrow_a" points="17 9 9 2 9.062 6 1 6.021 1 12 9.062 12 9.062 16"/>
    <mask id="shapes_arrow_b" width="16" height="14" x="0" y="0" fill="white">
      <use xlink:href="#shapes_arrow_a"/>
    </mask>
  </defs>
  <use fill="none" fill-rule="evenodd" stroke="#000000" stroke-width="2" mask="url(#shapes_arrow_b)" xlink:href="#shapes_arrow_a"/>
</g><g transform="translate(518,184)"><path fill="#000000" fill-rule="evenodd" d="M15,0 L1,0 C0.45,0 0,0.45 0,1 L0,15 C0,15.55 0.45,16 1,16 L15,16 C15.55,16 16,15.55 16,15 L16,1 C16,0.45 15.55,0 15,0 L15,0 Z M6,13 L6,10 L2,10 L2,6 L6,6 L6,3 L11,8 L6,13 L6,13 Z" transform="translate(1 1)"/>
</g><g transform="translate(428,54)"><path fill="#000000" fill-rule="evenodd" d="M2.5180432,3.93225676 C2.35281657,3.97644275 2.17915933,4 2,4 C0.8954305,4 0,3.1045695 0,2 C0,0.8954305 0.8954305,0 2,0 C3.1045695,0 4,0.8954305 4,2 C4,2.17915933 3.97644275,2.35281657 3.93225676,2.5180432 L11.4819568,10.0677432 C11.6471834,10.0235573 11.8208407,10 12,10 C13.1045695,10 14,10.8954305 14,12 C14,13.1045695 13.1045695,14 12,14 C10.8954305,14 10,13.1045695 10,12 C10,11.8208407 10.0235573,11.6471834 10.0677432,11.4819568 L2.5180432,3.93225676 Z" transform="translate(2 2)"/>
</g><g transform="translate(90,324)"><!-- Generator: Sketch 45.1 (43504) - http://www.bohemiancoding.com/sketch -->
    <title>Artboard 2</title>
    <desc>Created with Sketch.</desc>
    <defs/>
    <g id="Page-2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Artboard-2">
            <g id="ic_animation_black_24dp">
                <polygon id="Shape" points="0 0 18 0 18 18 0 18"/>
                <path d="M11.7428571,1.71428571 C9.65228571,1.71428571 7.84714286,2.90228571 6.94457143,4.63028571 C5.95714286,5.14714286 5.13942857,5.95714286 4.63028571,6.94457143 C2.90228571,7.84714286 1.71428571,9.65228571 1.71428571,11.7428571 C1.71428571,14.7282857 4.12885714,17.1428571 7.11428571,17.1428571 C9.20485714,17.1428571 11.01,15.9548571 11.9125714,14.2268571 C12.9,13.71 13.7177143,12.9 14.2268571,11.9125714 C15.9548571,11.01 17.1428571,9.20485714 17.1428571,7.11428571 C17.1428571,4.12885714 14.7282857,1.71428571 11.7428571,1.71428571 Z M7.11428571,15.6 C4.98514286,15.6 3.25714286,13.872 3.25714286,11.7428571 C3.25714286,10.8788571 3.54257143,10.0765714 4.02857143,9.42857143 C4.02857143,12.414 6.44314286,14.8285714 9.42857143,14.8285714 C8.78057143,15.3145714 7.97828571,15.6 7.11428571,15.6 Z M9.42857143,13.2857143 C7.29942857,13.2857143 5.57142857,11.5577143 5.57142857,9.42857143 C5.57142857,8.56457143 5.85685714,7.76228571 6.34285714,7.11428571 C6.34285714,10.092 8.75742857,12.5065714 11.7428571,12.5142857 C11.0948571,13.0002857 10.2925714,13.2857143 9.42857143,13.2857143 Z M11.7369872,10.9965443 C9.60123686,10.9965443 7.86986908,9.26517657 7.86986908,7.12942618 C7.86986908,4.99367579 9.60123686,3.26230801 11.7369872,3.26230801 C13.8727376,3.26230801 15.6041054,4.99367579 15.6041054,7.12942618 C15.6041054,9.26517657 13.8727376,10.9965443 11.7369872,10.9965443 Z" id="Combined-Shape" fill="#000000" fill-rule="nonzero"/>
                <polygon id="Shape" points="0 0 18 0 18 18 0 18"/>
            </g>
        </g>
    </g>
</g><g transform="translate(234,72)"><g fill="#000000" fill-rule="evenodd" transform="translate(2 2)">
    <path fill-opacity=".38" d="M0,1.00684547 C0,0.450780073 0.449948758,0 1.00684547,0 L12.9931545,0 C13.5492199,0 14,0.449948758 14,1.00684547 L14,12.9931545 C14,13.5492199 13.5500512,14 12.9931545,14 L1.00684547,14 C0.450780073,14 0,13.5500512 0,12.9931545 L0,1.00684547 Z M1,1 L13,1 L13,13 L1,13 L1,1 Z"/>
    <polygon points="3 5 7 9 11 5"/>
  </g>
</g><g transform="translate(572,72)"><path fill="#000000" fill-rule="evenodd" d="M0,1.00247329 C0,0.448822582 0.444630861,0 1.00087166,0 L14.9991283,0 C15.5518945,0 16,0.455760956 16,1.00247329 L16,10.9975267 C16,11.5511774 15.5553691,12 14.9991283,12 L1.00087166,12 C0.448105505,12 0,11.544239 0,10.9975267 L0,1.00247329 Z M8,9 L1,4 L1,2 L8,7 L15,2 L15,4 L8,9 Z" transform="translate(1 3)"/>
</g><g transform="translate(536,220)"><path fill="#000000" fill-rule="evenodd" d="M0,0.995397568 C0,0.445654671 0.455760956,0 1.00247329,0 L10.9975267,0 C11.5511774,0 12,0.455664396 12,0.995397568 L12,9.00460243 C12,9.55434533 11.544239,10 10.9975267,10 L1.00247329,10 C0.448822582,10 0,9.5443356 0,9.00460243 L0,0.995397568 Z M2,2 L4,2 L4,4 L2,4 L2,2 Z M5,2 L10,2 L10,4 L5,4 L5,2 Z M2,5 L4,5 L4,8 L2,8 L2,5 Z M5,5 L10,5 L10,8 L5,8 L5,5 Z M3,14 L11,14 L11,12 L3,12 L3,10.5 L0.5,13 L3,15.5 L3,14 Z" transform="translate(3 1)"/>
</g><g transform="translate(328,18)"><path fill="#000000" fill-rule="evenodd" d="M0,5 L14,5 L14,13 L0,13 L0,5 Z M2,5 L12,5 L12,11 L2,11 L2,5 Z M3.5,5 L6,5 L6,0 L8,0 L8,5 L10.5,5 L7,8.5 L3.5,5 L3.5,5 Z" transform="translate(2 2)"/>
</g><g transform="translate(500,148)"><path fill="#000000" fill-rule="evenodd" d="M0,5.00218626 C0,2.23955507 2.22898489,0 5.00218626,0 L12,0 L12,12 L0,12 L0,5.00218626 Z M2,5.00032973 C2,3.34329338 3.3486445,2 5.00032973,2 L12,2 L12,12 L2,12 L2,5.00032973 Z M4,4 L8,4 L8,8 L4,8 L4,4 Z M8,5 L12,5 L12,7 L8,7 L8,5 Z M7,8 L7,12 L5,12 L5,8 L7,8 Z" transform="translate(3 3)"/>
</g><g transform="translate(72,90)"><path fill="#000000" fill-rule="evenodd" d="M6,10.5 C3.51,10.5 1.5,8.49 1.5,6 C1.5,3.51 3.51,1.5 6,1.5 C7.24,1.5 8.36,2.02 9.17,2.83 L7,5 L12,5 L12,0 L10.24,1.76 C9.15,0.68 7.66,0 6,0 C2.69,0 0.01,2.69 0.01,6 C0.01,9.31 2.69,12 6,12 C8.97,12 11.43,9.84 11.9,7 L10.38,7 C9.92,9 8.14,10.5 6,10.5 L6,10.5 Z" transform="matrix(-1 0 0 1 15.01 3)"/>
</g><g transform="translate(364,184)"><path fill="#000000" fill-rule="evenodd" d="M13.9291111,8 C13.4905984,11.0656912 11.0656912,13.4905984 8,13.9291111 L8,8 L13.9291111,8 Z M13.9291111,6 C13.4905984,2.93430884 11.0656912,0.509401622 8,0.0708888585 L8,6 L13.9291111,6 Z M6,0.0708888585 C2.60770586,0.556118519 8.8817842e-16,3.47352809 8.8817842e-16,7 C8.8817842e-16,10.5264719 2.60770586,13.4438815 6,13.9291111 L6,0.0708888585 Z" transform="translate(2 2)"/>
</g><g transform="translate(428,292)"><path fill="#000000" fill-rule="evenodd" d="M10.75,17 L1.55749512,17 C0.728745117,17 0.999355351,17 0.999355351,17 C0.441480327,17 2.84217094e-14,16.5492199 2.84217094e-14,15.9931545 L2.84217094e-14,4.00684547 C2.84217094e-14,3.44994876 0.445472655,2.99927141 0.994991023,2.99837264 C0.994991023,2.99837264 1.92481493,2.99685187 2.38726031,2.99609551 L5.0005835,2.99182129 C5.00058365,2.99182129 5,0.991821289 7,0.991821289 C9,0.991821289 9.03942871,2.99182129 9,3 L13.0001925,3 C13.5562834,3 14,3.44510135 14,3.99416169 C14,3.99416169 14,3.66307129 14,4.49182129 L14,6 L12,6 L12,5 L11,5 L11,6 L3,6 L3,5 L2,5 L2,15 L9,15 L9,17 L10.75,17 Z M6,3 L8,3 L8,4 L6,4 L6,3 Z M14,7 L12,7 C10.895,7 10,7.895 10,9 L10,17 L12,17 L12,15 L14,15 L14,17 L16,17 L16,9 C16,7.895 15.105,7 14,7 Z M12,13 L14,13 L14,9 L12,9 L12,13 Z" transform="translate(1)"/>
</g><g transform="translate(482,18)"><path fill="#000000" fill-rule="evenodd" d="M15,0 L1,0 C0.45,0 0,0.45 0,1 L0,15 C0,15.55 0.45,16 1,16 L15,16 C15.55,16 16,15.55 16,15 L16,1 C16,0.45 15.55,0 15,0 L15,0 Z M2.5,12 L5.25,8.46 L7.21,10.82 L9.96,7.28 L13.5,12 L2.5,12 L2.5,12 Z" transform="translate(1 1)"/>
</g><g transform="translate(198,36)"><path fill="#000000" fill-rule="evenodd" d="M4,14 C4,14.5 4.5,15 5,15 L7,15 C7.5,15 8,14.5 8,14 L8,13 L4,13 L4,14 Z M6,0.5 C3.0975,0.5 0.75,2.8475 0.75,5.75 C0.75,7.535 1.6425,9.1025 3,10.055 L3,11 C3,11.5 3.5,12 4,12 L8,12 C8.5,12 9,11.5 9,11 L9,10.055 C10.3575,9.1025 11.25,7.535 11.25,5.75 C11.25,2.8475 8.9025,0.5 6,0.5 Z" transform="translate(3 1)"/>
</g><g transform="translate(36,198)"><path fill="#000000" fill-rule="evenodd" d="M10.2675644,11 L8,11 L8,1 L3.73243561,1 C3.38662619,0.40219863 2.74028236,0 2,0 C0.8954305,0 0,0.8954305 0,2 C0,3.1045695 0.8954305,4 2,4 C2.74028236,4 3.38662619,3.59780137 3.73243561,3 L6,3 L6,13 L10.2675644,13 C10.6133738,13.5978014 11.2597176,14 12,14 C13.1045695,14 14,13.1045695 14,12 C14,10.8954305 13.1045695,10 12,10 C11.2597176,10 10.6133738,10.4021986 10.2675644,11 Z" transform="translate(2 2)"/>
</g><g transform="translate(216,252)"><path fill="#000000" fill-rule="evenodd" d="M1.00684547,0 C0.450780073,0 1.57717987e-15,0.447449351 1.57717987e-15,1.0068708 C1.57717987e-15,1.0068708 1.57717987e-15,0.795555556 1.57717987e-15,1.77777778 L1.57717987e-15,12.4444444 C1.57717987e-15,13.4266667 -1.20058952e-09,12.9909959 -1.20058952e-09,12.9909959 C-6.73340566e-09,13.5482535 0.443716635,14 0.999807483,14 L5,14 L7,16 L9,14 L12.1307373,14 L12.9927192,14 C13.549025,14 14,13.5505095 14,12.9998043 L14,1.77777778 C14,0.795555556 14,1.0068708 14,1.0068708 C14,0.45079141 13.5500512,0 12.9931545,0 L1.00684547,0 Z M7,12 L5.796875,8.375 L2.625,7 L5.796875,5.625 L7,2 L8.203125,5.625 L11.375,7 L8.203125,8.375 L7,12 Z" transform="translate(2 1)"/>
</g><g transform="translate(500,274)"><defs>
  <path id="dogfood_white_path_1" d="m5.2737 8.6798c-0.7035 0-1.2737-0.5641-1.2737-1.26 0-0.6958 0.5702-1.2599 1.2737-1.2599 0.7034 0 1.2736 0.5641 1.2736 1.2599 0 0.6959-0.5702 1.26-1.2736 1.26zm2.0887-2.1599c-0.7034 0-1.2736-0.5641-1.2736-1.26 0-0.6958 0.5702-1.2599 1.2736-1.2599 0.7035 0 1.2737 0.5641 1.2737 1.2599 0 0.6959-0.5702 1.26-1.2737 1.26zm3.0936 0c-0.7038 0-1.274-0.5641-1.274-1.26 0-0.6958 0.5702-1.2599 1.274-1.2599 0.703 0 1.273 0.5641 1.273 1.2599 0 0.6959-0.57 1.26-1.273 1.26zm2.27 2.1599c-0.703 0-1.273-0.5641-1.273-1.26 0-0.6958 0.57-1.2599 1.273-1.2599 0.704 0 1.274 0.5641 1.274 1.2599 0 0.6959-0.57 1.26-1.274 1.26zm-2.35-0.684c0.407 0.468 0.786 0.9648 1.23 1.4039 0.669 0.6773 1.484 1.3533 1.28 2.3683-0.116 0.576-0.502 1.023-1.113 1.21-0.342 0.108-1.616-0.216-2.9113-0.216-1.2955 0-2.4527 0.317-2.7947 0.216-0.6114-0.187-1.0044-0.634-1.1209-1.21-0.2037-1.015 0.6041-1.691 1.2737-2.3683 0.4367-0.4391 0.8006-0.9359 1.2081-1.4039 0.2548-0.288 0.495-0.5472 0.888-0.6768 0.1746-0.0504 0.3566-0.0792 0.5385-0.0792 0 0 0.4294 0.0216 0.6041 0.0792 0.393 0.1296 0.6625 0.3888 0.9175 0.6768z"/>
 </defs>
 <g fill-rule="evenodd" fill="none">
   <polygon points="0 0 18 0 18 18 0 18"/>
   <mask fill="white">
    <use xlink:href="#dogfood_white_path_1"/>
   </mask>
   <use xlink:href="#dogfood_white_path_1" fill-rule="nonzero" fill="#ffffff"/>
 </g>
</g><g transform="translate(202,360)"><path fill="#000000" fill-rule="evenodd" d="M14.53125,-1.95399252e-13 L2.50909424,-1.95399252e-13 C2.17272949,-1.95399252e-13 2,-1.9485927e-13 1,-1.95126228e-13 C2.84217094e-14,-1.95399252e-13 2.84217094e-14,1.00134277 2.84217094e-14,1.00134277 L2.84217094e-14,4 L2.00195313,4 L2.00195313,2 L14,2 L14,12 L10,12 L10,13.9900513 L14.9996948,13.9900513 C16,13.9900513 16,12.9960327 16,12.9960327 L16,1.00134277 C16,1.00134277 16,-1.95399252e-13 14.9996948,-1.95399252e-13 L14.53125,-1.95399252e-13 Z M0.00390625,8 C3.73140625,7.99456787 6.02288818,10.256936 6,14 L8,14 C8,9.42818604 4.56015625,6.00061035 0.00390625,6.00061035 L0.00390625,8 Z M0.00341796875,14 L2.00366211,14 C2.00366211,12.7533179 1.24125,12 0,12 L0.00341796875,14 Z M0,11 C2.07,10.9772339 3.02642822,12.18 3,14 L5,14 C4.97491455,11.35125 2.89875,9 0,9 L0,11 Z" transform="translate(1 2)"/>
</g><g transform="translate(500,130)"><path fill="#000000" fill-rule="evenodd" d="M9.5,3 L7,3 L7,0 L5,0 L5,3 L2.5,3 L6,6.5 L9.5,3 L9.5,3 Z M0,8 L0,10 L12,10 L12,8 L0,8 L0,8 Z M2.5,15 L5,15 L5,18 L7,18 L7,15 L9.5,15 L6,11.5 L2.5,15 L2.5,15 Z" transform="translate(3)"/>
</g><g transform="translate(198,144)"><defs><linearGradient id="a" x1="50.004%" x2="50.004%" y1="8.586%" y2="100.013%"><stop stop-color="#3E2723" stop-opacity=".2" offset="0%"/><stop stop-color="#3E2723" stop-opacity=".02" offset="100%"/></linearGradient><radialGradient id="b" cx="3.168%" cy="2.718%" r="161.248%" fx="3.168%" fy="2.718%" gradientTransform="matrix(1 0 0 .72222 0 .008)"><stop stop-color="#FFF" offset="0%"/><stop stop-color="#FFF" stop-opacity="0" offset="100%"/></radialGradient></defs><g fill="none" fill-rule="evenodd"><path fill="#DB4437" d="M9.5 2H24l9 9v24.5c0 1.3807119-1.1192881 2.5-2.5 2.5h-21C8.11928813 38 7 36.8807119 7 35.5v-31C7 3.11928813 8.11928813 2 9.5 2z"/><path fill="#3E2723" fill-opacity=".2" d="M7 35c0 1.3807119 1.11928813 2.5 2.5 2.5h21c1.3807119 0 2.5-1.1192881 2.5-2.5v.5c0 1.3807119-1.1192881 2.5-2.5 2.5h-21C8.11928813 38 7 36.8807119 7 35.5V35z"/><path fill="#FFF" fill-opacity=".2" d="M9.5 2H24v.5H9.5C8.11928813 2.5 7 3.61928813 7 5v-.5C7 3.11928813 8.11928813 2 9.5 2z"/><path fill="url(#a)" fill-rule="nonzero" d="M17.5 8l8.5 8.5V9" transform="translate(7 2)"/><path fill="#EDA29B" d="M24 2l9 9h-6.5C25.1192881 11 24 9.88071187 24 8.5V2z"/><path fill="#F1F1F1" fill-rule="nonzero" d="M22 22c0-2.7613636-2.2386364-5-5-5-2.76136364 0-5 2.2386364-5 5s2.23863636 5 5 5c2.7613636 0 5-2.2386364 5-5zm-5 3c-1.6542857 0-3-1.3457143-3-3s1.3457143-3 3-3 3 1.3457143 3 3-1.3457143 3-3 3zm6.5-1c-.5 2-2 3.5-3.5 4v4h8v-8h-4.5z"/><path fill="url(#b)" fill-opacity=".1" d="M2.5 0H17l9 9v24.5c0 1.3807119-1.1192881 2.5-2.5 2.5h-21C1.11928813 36 0 34.8807119 0 33.5v-31C0 1.11928813 1.11928813 0 2.5 0z" transform="translate(7 2)"/></g></g><g transform="translate(126,252)"><path fill="#000000" fill-rule="evenodd" d="M6.25,3.52085955e-16 L2,9.01409691 L4,9.01409691 L4.5,7.8 L9.5,7.8 L10,9 L12,9.01409691 L7.75,2.60237445e-16 L6.25,3.52085955e-16 Z M7,2 L8.87,6.02 L5.13,6.02 L7,2 Z M12,10 L12,12 L0,12 L0,14 L12,14 L12,16 L15,13 L12,10 Z" transform="translate(2 1)"/>
</g><g transform="translate(108,342)"><polygon fill="#000000" fill-rule="evenodd" points="0 0 0 1 6 7 6 12 8 11 8 7 14 1 14 0" transform="translate(2 3)"/>
</g><g transform="translate(464,202)"><path fill="#000000" fill-rule="evenodd" d="M0,1.00684547 C0,0.450780073 0.449948758,0 1.00684547,0 L12.9931545,0 C13.5492199,0 14,0.449948758 14,1.00684547 L14,12.9931545 C14,13.5492199 13.5500512,14 12.9931545,14 L1.00684547,14 C0.450780073,14 0,13.5500512 0,12.9931545 L0,1.00684547 Z M2,2 L12,2 L12,12 L2,12 L2,2 Z M2,10.5 L5,7.5 L7.02877192,9.5 L12,4.5 L12,3 L7,8 L5,6 L2,9 L2,10.5 Z" transform="translate(2 2)"/>
</g><g transform="translate(162,306)"><path fill="#000000" fill-rule="evenodd" d="M1.00684547,0 C0.450780073,0 -1.97553381e-15,0.447449351 -1.97553381e-15,1.0068708 C-1.97553381e-15,1.0068708 -1.97553381e-15,0.795555556 -1.97553381e-15,1.77777778 L-1.97553381e-15,12.4444444 C-1.97553381e-15,13.4266667 -1.20059307e-09,12.9909959 -1.20059307e-09,12.9909959 C-6.73340922e-09,13.5482535 0.443716635,14 0.999807483,14 L5,14 L7,16 L9,14 L12.1307373,14 L12.9927192,14 C13.549025,14 14,13.5505095 14,12.9998043 L14,1.77777778 C14,0.795555556 14,1.0068708 14,1.0068708 C14,0.45079141 13.5500512,0 12.9931545,0 L1.00684547,0 Z M7,12 L5.796875,8.375 L2.625,7 L5.796875,5.625 L7,2 L8.203125,5.625 L11.375,7 L8.203125,8.375 L7,12 Z" transform="translate(2 1)"/>
</g><g transform="translate(18,342)"><path fill="#000000" fill-rule="evenodd" d="M10,7 L10,5 L9,5 L9,7 L7,7 L7,8 L9,8 L9,10 L10,10 L10,8 L12,8 L12,7 L10,7 Z M13,2 L8,2 L6,0 L1,0 C0.45,0 0,0.45 0,1 L0,11 C0,11.55 0.45,12 1,12 L13,12 C13.55,12 14,11.55 14,11 L14,3 C14,2.45 13.55,2 13,2 Z M13,11 L1,11 L1,3 L13,3 L13,11 Z" transform="translate(2 3)" opacity=".38"/>
</g><g transform="translate(36,144)"><path fill="#000000" fill-rule="evenodd" d="M8,4.25 C5.93,4.25 4.25,5.93 4.25,8 C4.25,10.07 5.93,11.75 8,11.75 C10.07,11.75 11.75,10.07 11.75,8 C11.75,5.93 10.07,4.25 8,4.25 L8,4.25 Z M8,0.5 C3.86,0.5 0.5,3.86 0.5,8 C0.5,12.14 3.86,15.5 8,15.5 C12.14,15.5 15.5,12.14 15.5,8 C15.5,3.86 12.14,0.5 8,0.5 L8,0.5 Z M8,14 C4.685,14 2,11.315 2,8 C2,4.685 4.685,2 8,2 C11.315,2 14,4.685 14,8 C14,11.315 11.315,14 8,14 L8,14 Z" transform="translate(1 1)"/>
</g><g transform="translate(518,54)"><defs><linearGradient id="a" x1="50.005%" x2="50.005%" y1="8.586%" y2="100.014%"><stop stop-opacity=".2" offset="0%"/><stop stop-opacity=".02" offset="100%"/></linearGradient><radialGradient id="b" cx="3.168%" cy="2.718%" r="161.248%" fx="3.168%" fy="2.718%" gradientTransform="matrix(1 0 0 .72222 0 .008)"><stop stop-color="#FFF" offset="0%"/><stop stop-color="#FFF" stop-opacity="0" offset="100%"/></radialGradient></defs><g fill="none" fill-rule="evenodd"><path fill="#90A4AE" d="M9.5 2H24l9 9v24.5c0 1.3807119-1.1192881 2.5-2.5 2.5h-21C8.11928813 38 7 36.8807119 7 35.5v-31C7 3.11928813 8.11928813 2 9.5 2z"/><path fill="#000" fill-opacity=".1" d="M7 35c0 1.3807119 1.11928813 2.5 2.5 2.5h21c1.3807119 0 2.5-1.1192881 2.5-2.5v.5c0 1.3807119-1.1192881 2.5-2.5 2.5h-21C8.11928813 38 7 36.8807119 7 35.5V35z"/><path fill="#FFF" fill-opacity=".2" d="M9.5 2H24v.5H9.5C8.11928813 2.5 7 3.61928813 7 5v-.5C7 3.11928813 8.11928813 2 9.5 2z"/><path fill="url(#a)" fill-rule="nonzero" d="M17.5 8l8.5 8.5V9" transform="translate(7 2)"/><path fill="#F1F1F1" d="M24 2l9 9h-6.5C25.1192881 11 24 9.88071187 24 8.5V2zm4.86 25l-5.89-10h-5.26l5.89 10h5.26zm-13.15 6h10.53l2.61-5H18.29l-2.58 5zm.92-14.55l-5.49 10L13.8 33l5.48-9.98-2.65-4.57z"/><path fill="url(#b)" fill-opacity=".1" d="M2.5 0H17l9 9v24.5c0 1.3807119-1.1192881 2.5-2.5 2.5h-21C1.11928813 36 0 34.8807119 0 33.5v-31C0 1.11928813 1.11928813 0 2.5 0z" transform="translate(7 2)"/></g></g><g transform="translate(382,342)"><path fill="#16A764" fill-rule="evenodd" d="M15,0 L1,0 C0.5,0 0,0.5 0,1 L0,15 C0,15.5 0.5,16 1,16 L15,16 C15.5,16 16,15.5 16,15 L16,1 C16,0.5 15.5,0 15,0 L15,0 Z M12.4,13 L10.4,13 L8.2,9.2 L6,13 L4,13 L7.2,8 L4,3 L6,3 L8.2,6.8 L10.4,3 L12.4,3 L9.2,8 L12.4,13 L12.4,13 Z" transform="translate(1 1)"/>
</g><g transform="translate(346,324)"><title>slide_18_18</title>
    <desc>Created with Sketch.</desc>
    <defs/>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="slide_18_18">
            <g id="ic_drive_presentation_black_24dp">
                <polygon id="Shape" points="0 0 18 0 18 18 0 18"/>
                <path d="M14.25,2.25 L3.75,2.25 C2.925,2.25 2.2575,2.925 2.2575,3.75 L2.2575,14.25 C2.2575,15.075 2.925,15.75 3.75,15.75 L14.25,15.75 C15.075,15.75 15.75,15.075 15.75,14.25 L15.75,3.75 C15.75,2.925 15.075,2.25 14.25,2.25 L14.25,2.25 Z M14.25,12 L3.75,12 L3.75,6 L14.25,6 L14.25,12 L14.25,12 Z" id="Shape" fill="#000000" opacity="0.539999962"/>
            </g>
        </g>
    </g>
</g><g transform="translate(216,90)"><g fill="#000000" fill-rule="evenodd" transform="translate(2 2)">
    <path d="M6,14 L8,14 L8,12 L6,12 L6,14 L6,14 Z M3,2 L5,2 L5,0 L3,0 L3,2 L3,2 Z M6,11 L8,11 L8,9 L6,9 L6,11 L6,11 Z M3,14 L5,14 L5,12 L3,12 L3,14 L3,14 Z M0,5 L2,5 L2,3 L0,3 L0,5 L0,5 Z M0,14 L2,14 L2,12 L0,12 L0,14 L0,14 Z M0,2 L2,2 L2,0 L0,0 L0,2 L0,2 Z M0,11 L2,11 L2,9 L0,9 L0,11 L0,11 Z M12,11 L14,11 L14,9 L12,9 L12,11 L12,11 Z M12,14 L14,14 L14,12 L12,12 L12,14 L12,14 Z M12,5 L14,5 L14,3 L12,3 L12,5 L12,5 Z M12,0 L12,2 L14,2 L14,0 L12,0 L12,0 Z M6,2 L8,2 L8,0 L6,0 L6,2 L6,2 Z M9,2 L11,2 L11,0 L9,0 L9,2 L9,2 Z M6,5 L8,5 L8,3 L6,3 L6,5 L6,5 Z M9,14 L11,14 L11,12 L9,12 L9,14 L9,14 Z" opacity=".54"/>
    <polygon points="0 8 14 8 14 6 0 6"/>
  </g>
</g><g transform="translate(446,148)"><path fill="#4285F4" fill-rule="evenodd" d="M15,0 L1,0 C0.45,0 0,0.45 0,1 L0,15 C0,15.55 0.45,16 1,16 L15,16 C15.55,16 16,15.55 16,15 L16,1 C16,0.45 15.55,0 15,0 L15,0 Z M6,13 L6,10 L2,10 L2,6 L6,6 L6,3 L11,8 L6,13 L6,13 Z" transform="translate(1 1)"/>
</g><g transform="translate(144,90)"><path fill="#000000" fill-rule="evenodd" d="M9.01902793,9.72705177 C8.06535219,10.5218555 6.83851442,11 5.5,11 C2.46243388,11 0,8.53756612 0,5.5 C0,2.46243388 2.46243388,0 5.5,0 C8.53756612,0 11,2.46243388 11,5.5 C11,6.83851442 10.5218555,8.06535219 9.72705177,9.01902793 L9.98,9.27 L10.77,9.27 L14.76,13.27 L13.27,14.76 L9.27,10.77 L9.27,9.98 L9.01902793,9.72705177 Z M5.5,9.5 C7.709139,9.5 9.5,7.709139 9.5,5.5 C9.5,3.290861 7.709139,1.5 5.5,1.5 C3.290861,1.5 1.5,3.290861 1.5,5.5 C1.5,7.709139 3.290861,9.5 5.5,9.5 Z M8,6 L6,6 L6,8 L5,8 L5,6 L3,6 L3,5 L5,5 L5,3 L6,3 L6,5 L8,5 L8,6" transform="translate(2 2)"/>
</g><g transform="translate(572,18)"><path fill="#000000" fill-rule="evenodd" d="M0,0 L2,0 L2,14 L0,14 L0,0 Z M12,0 L14,0 L14,14 L12,14 L12,0 Z M4,6 L12,6 L12,8 L4,8 L4,6 Z" transform="translate(2 2)"/>
</g><g transform="translate(388,280)"><g fill="none" fill-rule="evenodd">
    <polygon points="0 0 18 0 18 18 0 18"/>
    <path fill="#000" fill-opacity=".54" d="M2,16 L4,16 L4,14 L2,14 L2,16 L2,16 Z M8,16 L10,16 L10,14 L8,14 L8,16 L8,16 Z M5,16 L7,16 L7,14 L5,14 L5,16 L5,16 Z M2,13 L4,13 L4,11 L2,11 L2,13 L2,13 Z M2,7 L4,7 L4,5 L2,5 L2,7 L2,7 Z M2,10 L4,10 L4,8 L2,8 L2,10 L2,10 Z M14,10 L16,10 L16,8 L14,8 L14,10 L14,10 Z M14,13 L16,13 L16,11 L14,11 L14,13 L14,13 Z M14,7 L16,7 L16,5 L14,5 L14,7 L14,7 Z M11,16 L13,16 L13,14 L11,14 L11,16 L11,16 Z M14,16 L16,16 L16,14 L14,14 L14,16 L14,16 Z"/>
    <polygon fill="#000" points="2 2 2 4 16 4 16 2"/>
  </g>
</g><g transform="translate(554,112)"><path fill="#4285F4" fill-rule="evenodd" d="M0,0 L3,0 L3,12 L0,12 L0,0 Z M7,0 L10,0 L10,12 L7,12 L7,0 Z M3,4 L7,4 L7,7 L3,7 L3,4 Z" transform="translate(4 3)"/>
</g><g transform="translate(36,72)"><path fill="#000000" fill-rule="evenodd" d="M0,0 L12,0 L12,12 L0,12 L0,0 Z M2,2 L12,2 L12,10 L2,10 L2,2 Z M8,5 L12,5 L12,7 L8,7 L8,5 Z M4,4 L8,4 L8,8 L4,8 L4,4 Z" transform="translate(3 3)"/>
</g><g transform="translate(220,360)"><g fill="none" fill-rule="evenodd" transform="translate(0 1)">
    <polygon fill="#4285F4" points="4.71 16 15.24 16 17.85 11 7.29 11"/>
    <polygon fill="#0F9D58" points="5.63 1.45 .14 11.45 2.8 16 8.28 6.02 5.63 1.45"/>
    <polygon fill="#FFC107" points="17.86 10 11.97 0 6.71 0 12.6 10"/>
  </g>
</g><g transform="translate(162,54)"><path fill="#010101" fill-rule="evenodd" d="M2,4 L2,6 L13,6 L13,7 L2,7 L2,9 L13,9 L13,10 L2,10 L2,12 L14,12 L14,14 L0,14 L0,13 L0,0 L2,0 L2,3 L13,3 L13,4 L2,4 Z" transform="matrix(-1 0 0 1 16 2)"/>
</g><g transform="translate(144,306)"><defs>
    <path id="custom_shape_tool_a" d="M-3.55271368e-15,7 L5,0 L14.4935615,0 C15.3255445,0 15.6658422,0.584776089 15.257005,1.30024119 L12,7 L15.257005,12.6997588 C15.6673498,13.4178622 15.3284594,14 14.4935615,14 L5,14 L-3.55271368e-15,7 Z"/>
    <mask id="custom_shape_tool_b" width="15.431" height="14" x="0" y="0" fill="white">
      <use xlink:href="#custom_shape_tool_a"/>
    </mask>
  </defs>
  <use fill="none" fill-rule="evenodd" stroke="#000000" stroke-width="4" mask="url(#custom_shape_tool_b)" xlink:href="#custom_shape_tool_a" transform="translate(1 2)" stroke-linecap="round" stroke-linejoin="bevel"/>
</g><g transform="translate(572,108)"><g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <polygon points="0 0 18 0 18 18 0 18"/>
        <path d="M4.37811019,5.04508453 L0.877344389,8.56945101 C0.838009942,8.60878545 0.775074826,8.71105502 0.775074826,8.77399013 C0.775074826,8.89986036 0.853743721,8.97852926 0.979613952,8.97852926 L6.48643656,8.97852926 C7.38326196,8.97852926 8.05981445,8.30197676 8.05981445,7.40515137 L8.05981445,5.04508453 L4.37811019,5.04508453 Z M17.0794681,9.01671 L11.5726455,9.01671 C10.6758201,9.01671 9.99926758,9.69326249 9.99926758,10.5900879 L9.99926758,12.9501547 L13.6809718,12.9501547 L17.1817376,9.42578825 C17.2210721,9.38645381 17.2840072,9.28418424 17.2840072,9.22124913 C17.2840072,9.0953789 17.2053383,9.01671 17.0794681,9.01671 Z M9.48050682,0.95941242 C9.44117238,0.920077973 9.33890281,0.857142857 9.2759677,0.857142857 C9.15009747,0.857142857 9.07142857,0.935811752 9.07142857,1.06168198 L9.07142857,6.56850459 C9.07142857,7.46532999 9.74798106,8.14188248 10.6448065,8.14188248 L13.0048733,8.14188248 L13.0048733,4.46017822 L9.48050682,0.95941242 Z M5.06655528,10.0251329 L5.06655528,13.7068371 L8.59092175,17.2076029 C8.6302562,17.2469374 8.73252576,17.3098725 8.79546087,17.3098725 C8.92133111,17.3098725 9,17.2312036 9,17.1053334 L9,11.5985107 C9,10.7016853 8.32344751,10.0251329 7.42662211,10.0251329 L5.06655528,10.0251329 Z" fill="#000000"/>
    </g>
</g><g transform="translate(292,126)"><path fill="#000000" fill-rule="evenodd" d="M9,0.75 C4.44375,0.75 0.75,4.44375 0.75,9 C0.75,13.55625 4.44375,17.25 9,17.25 C13.55625,17.25 17.25,13.55625 17.25,9 C17.25,4.44375 13.55625,0.75 9,0.75 L9,0.75 Z M9,14.625 C5.893125,14.625 3.375,12.106875 3.375,9 C3.375,5.893125 5.893125,3.375 9,3.375 C10.516875,3.375 11.7890625,3.9328125 12.763125,4.84125 L11.150625,6.45375 C10.5665625,5.8978125 9.8259375,5.6128125 8.9990625,5.6128125 C7.1634375,5.6128125 5.675625,7.1653125 5.675625,9.0009375 C5.675625,10.8365625 7.164375,12.388125 9,12.388125 C10.6659375,12.388125 11.80125,11.4328125 12.0309375,10.123125 L9,10.123125 L9,7.96875 L14.2978125,7.96875 C14.3634375,8.3390625 14.4009375,8.7253125 14.4009375,9.1275 C14.4009375,12.34125 12.2484375,14.625 9,14.625 L9,14.625 Z"/>
</g><g transform="translate(310,324)"><path fill="#000000" fill-rule="evenodd" d="M0,14 L10,14 L10,12 L0,12 L0,14 Z M10,4 L0,4 L0,6 L10,6 L10,4 Z M0,0 L0,2 L14,2 L14,0 L0,0 Z M0,10 L14,10 L14,8 L0,8 L0,10 Z" transform="translate(2 2)"/>
</g><g transform="translate(216,198)"><path fill="#000000" fill-rule="evenodd" d="M7,0 L5,0 L0.5,12 L2.5,12 L3.62,9 L8.37,9 L9.49,12 L11.49,12 L7,0 L7,0 Z M4.38,7 L6,2.67 L7.62,7 L4.38,7 L4.38,7 Z" transform="translate(3 1)"/>
</g><g transform="translate(90,36)"><g fill="none" fill-rule="evenodd" transform="translate(0 2)">
    <polygon fill="#4285F4" points="9.42 32 30.48 32 35.7 22 14.58 22"/>
    <polygon fill="#0F9D58" points="11.26 2.9 .28 22.9 5.6 32 16.56 12.04 11.26 2.9"/>
    <polygon fill="#FFC107" points="35.72 20 23.94 0 13.42 0 25.2 20"/>
  </g>
</g><g transform="translate(90,252)"><path fill="#000000" fill-rule="evenodd" d="M0,6 C0,2.6862915 2.69303423,0 6,0 L12,0 L12,12 L6,12 C2.6862915,12 0,9.30696577 0,6 Z M2,6 C2,3.790861 3.7877996,2 6.009763,2 L12,2 L12,10 L6.009763,10 C3.79523205,10 2,8.20464385 2,6 Z M9,5 L12,5 L12,7 L9,7 L9,5 Z M5,4 L9,4 L9,8 L5,8 L5,4 Z" transform="translate(3 3)"/>
</g><g transform="translate(572,216)"><path fill="#000000" fill-rule="evenodd" d="M6,0.75875 L1.755,4.99625 C0.585,6.16625 0,7.72625 0,9.22625 C0,10.72625 0.585,12.30875 1.755,13.47875 C2.925,14.64875 4.4625,15.24125 6,15.24125 C7.5375,15.24125 9.075,14.64875 10.245,13.47875 C11.415,12.30875 12,10.72625 12,9.22625 C12,7.72625 11.415,6.16625 10.245,4.99625 L6,0.75875 Z M1.5,9 C1.5,8 1.965,7.04375 2.82,6.19625 L6,3 L9.18,6.23 C10.035,7.08125 10.5,8 10.5,9 L1.5,9 Z" transform="translate(3 1)"/>
</g><g transform="translate(274,0)"><path fill="#000000" fill-rule="evenodd" d="M1.32,2.91 C1.845,2.37375 2.37,1.89375 2.60625,1.99125 C2.9775,2.145 2.59875,2.77125 2.38125,3.135 C2.19375,3.45 0.23625,6.04875 0.23625,7.8675 C0.23625,8.8275 0.5925,9.6225 1.245,10.1025 C1.8075,10.51875 2.54625,10.64625 3.225,10.4475 C4.0275,10.21125 4.6875,9.40125 5.52,8.37375 C6.4275,7.25625 7.6425,5.79375 8.58,5.79375 C9.8025,5.79375 9.8175,6.55125 9.9,7.14 C7.0575,7.6275 5.86125,9.89625 5.86125,11.175 C5.86125,12.45375 6.94125,13.49625 8.265,13.49625 C9.48375,13.49625 11.48625,12.49875 11.7825,8.92125 L13.62375,8.92125 L13.62375,7.04625 L11.77125,7.04625 C11.65875,5.80875 10.95375,3.9 8.74875,3.9 C7.06125,3.9 5.61,5.3325 5.0475,6.03375 C4.6125,6.5775 3.50625,7.89 3.33375,8.0775 C3.1425,8.29875 2.8275,8.7075 2.4975,8.7075 C2.16375,8.7075 1.96125,8.08125 2.22375,7.26375 C2.48625,6.4425 3.27375,5.11875 3.61125,4.6275 C4.2,3.7725 4.5825,3.18375 4.5825,2.1675 C4.5825,0.5175 3.3525,0 2.7,0 C1.71,0 0.8475,0.75 0.65625,0.94125 C0.39,1.21125 0.165,1.4325 -8.8817842e-16,1.635 L1.32,2.91 L1.32,2.91 Z M8.2875,11.6625 C8.055,11.6625 7.73625,11.4675 7.73625,11.11875 C7.73625,10.66875 8.28,9.46875 9.8925,9.045 C9.66,11.06625 8.8125,11.6625 8.2875,11.6625 L8.2875,11.6625 Z" transform="translate(3 2)"/>
</g><g transform="translate(328,0)"><path fill="#000000" fill-rule="evenodd" d="M7,9 L4,9 L4,11.5 L0.5,8 L4,4.5 L4,7 L7,7 L7,4 L9,4 L9,7 L12,7 L12,9 L9,9 L9,12 L11.5,12 L8,15.5 L4.5,12 L7,12 L7,9 Z M12,11.5 L15.5,8 L12,4.5 L12,11.5 Z M4.5,4 L8,0.5 L11.5,4 L4.5,4 Z" transform="translate(1 1)"/>
</g><g transform="translate(454,342)"><path fill="#000000" fill-rule="evenodd" d="M1,0 C0.45,0 0,0.45 0,1 L0,15 C0,15.55 0.45,16 1,16 L15,16 C15.55,16 16,15.55 16,15 L16,1 C16,0.45 15.55,0 15,0 L1,0 Z M2,6 L14,6 L14,14 L2,14 L2,6 Z M11,2 L14,2 L14,4 L11,4 L11,2 Z" transform="translate(1 1)"/>
</g><g transform="translate(90,270)"><path fill="#000000" fill-rule="evenodd" d="M4,8 L4,13 L6,13 L6,2 L8,2 L8,13 L10,13 L10,2 L12,2 L12,0 L4,0 C1.79,0 0,1.79 0,4 C0,6.21 1.79,8 4,8 L4,8 Z" transform="translate(3 3)"/>
</g><g transform="translate(198,216)"><path fill="#000000" fill-rule="evenodd" d="M0,0 L0,14 L14,14 L14,0 L0,0 L0,0 Z M6,12 L2,12 L2,8 L6,8 L6,12 L6,12 Z M6,6 L2,6 L2,2 L6,2 L6,6 L6,6 Z M12,12 L8,12 L8,8 L12,8 L12,12 L12,12 Z M12,6 L8,6 L8,2 L12,2 L12,6 L12,6 Z" transform="translate(2 2)"/>
</g><g transform="translate(36,162)"><polygon fill="#000000" fill-rule="evenodd" points="0 0 4 4 8 0" transform="translate(5 7)"/>
</g><g transform="translate(500,36)"><polygon fill="#000000" fill-rule="evenodd" points="7.53 1.53 6.47 .47 4 2.94 1.53 .47 .47 1.53 2.94 4 .47 6.47 1.53 7.53 4 5.06 6.47 7.53 7.53 6.47 5.06 4" transform="translate(5 5)"/>
</g><g transform="translate(328,36)"><defs><style>.cls-1{fill:none;}</style></defs><title>ic_process_1_18px</title><g id="Layer_2" data-name="Layer 2"><g id="icon"><polygon points="4 6 1 6 1 12 4 12 7 9 4 6"/><polygon points="9 6 6 6 9 9 6 12 9 12 12 9 9 6"/><polygon points="14 6 11 6 14 9 11 12 14 12 17 9 14 6"/><rect class="cls-1" width="18" height="18"/></g></g></g><g transform="translate(36,180)"><path fill="#000000" fill-rule="evenodd" d="M12.4444444,0 L1.55555556,0 C0.692222222,0 0,0.7 0,1.55555556 L0,12.4444444 C0,13.3 0.692222222,14 1.55555556,14 L12.4444444,14 C13.3077778,14 14,13.3 14,12.4444444 L14,1.55555556 C14,0.7 13.3077778,0 12.4444444,0 L12.4444444,0 Z M5.5,10.75 L1.75,7 L2.8075,5.9425 L5.5,8.6275 L11.1925,2.935 L12.25,4 L5.5,10.75 L5.5,10.75 Z" transform="translate(2 2)"/>
</g><g transform="translate(162,198)"><path fill="#000000" fill-rule="evenodd" d="M12.3621139,11.5 C13.3843213,10.2832329 14,8.71351616 14,7 C14,3.13400675 10.8659932,0 7,0 C3.13400675,0 0,3.13400675 0,7 C0,8.71351616 0.615678738,10.2832329 1.63788615,11.5 L0,11.5 L0,13.5 L4.00019251,13.5 C4.55628335,13.5 5,13.0522847 5,12.5 L5,11.5 L4.75,11.5 L4.76344287,11.4731143 C3.12485989,10.6522258 2,8.95747546 2,7 C2,4.23857625 4.23857625,2 7,2 C9.76142375,2 12,4.23857625 12,7 C12,8.95747546 10.8751401,10.6522258 9.23655713,11.4731143 L9.25,11.5 L9,11.5 L9,12.5 C9,13.0561352 9.44762906,13.5 9.99980749,13.5 L14,13.5 L14,11.5 L12.3621139,11.5 Z" transform="translate(2 2.5)"/>
</g><g transform="translate(472,324)"><defs><style>.cls-1{fill:none;}</style></defs><title>ic_timeline_1_18px</title><g id="Layer_2" data-name="Layer 2"><g id="icon"><path d="M15,7a2,2,0,0,0-1.72,1H10.72A2,2,0,0,0,7.28,8H4.72a2,2,0,1,0,0,2H7.28a2,2,0,0,0,3.45,0h2.55A2,2,0,1,0,15,7Z"/><rect class="cls-1" width="18" height="18"/></g></g></g><g transform="translate(292,144)"><path fill="#FFFFFF" fill-rule="evenodd" d="M0,1 C0,0.45 0.45,0 1,0 L15,0 C15.55,0 16,0.45 16,1 L16,12 C16,12.55 15.55,13 15,13 L3.5,13 L0,16 L0,1 Z M3,11 L3,8.53 L9.88,1.65 C10.08,1.45 10.39,1.45 10.59,1.65 L12.36,3.42 C12.56,3.62 12.56,3.93 12.36,4.13 L5.47,11 L3,11 Z M7.5,11 L9.5,9 L13,9 L13,11 L7.5,11 Z" transform="translate(1 1)"/>
</g><g transform="translate(238,396)"><path fill="#000000" fill-rule="evenodd" d="M10,7 L10,5 L9,5 L9,7 L7,7 L7,8 L9,8 L9,10 L10,10 L10,8 L12,8 L12,7 L10,7 Z M13,2 L8,2 L6,0 L1,0 C0.45,0 0,0.45 0,1 L0,11 C0,11.55 0.45,12 1,12 L13,12 C13.55,12 14,11.55 14,11 L14,3 C14,2.45 13.55,2 13,2 Z M13,11 L1,11 L1,3 L13,3 L13,11 Z" transform="translate(2 3)"/>
</g><g transform="translate(90,288)"><polygon fill="#000000" fill-rule="evenodd" points="12 5 3.125 5 7.06 1.06 6 0 0 6 6 12 7.06 10.94 3.125 7 12 7" transform="translate(3 3)"/>
</g><g transform="translate(446,18)"><path fill="#000000" fill-rule="evenodd" d="M8,0.5 C3.85625,0.5 0.5,3.85625 0.5,8 C0.5,12.14375 3.85625,15.5 8,15.5 C12.14375,15.5 15.5,12.14375 15.5,8 C15.5,3.85625 12.14375,0.5 8,0.5 Z M7.25,13.9475 C4.29125,13.58 2,11.06 2,8 C2,7.53875 2.05625,7.08875 2.1575,6.6575 L5.75,10.25 L5.75,11 C5.75,11.82875 6.42125,12.5 7.25,12.5 L7.25,13.9475 Z M12.42125,12.04625 C12.23,11.43875 11.6675,11 11,11 L10.25,11 L10.25,8.75 C10.25,8.3375 9.9125,8 9.5,8 L5,8 L5,6.5 L6.5,6.5 C6.9125,6.5 7.25,6.1625 7.25,5.75 L7.25,4.25 L8.75,4.25 C9.57875,4.25 10.25,3.57875 10.25,2.75 L10.25,2.43875 C12.4475,3.3275 14,5.48 14,8 C14,9.56 13.4,10.9775 12.42125,12.04625 Z" transform="translate(1 1)"/>
</g><g transform="translate(90,198)"><path fill="#000000" fill-rule="evenodd" d="M6,14 L8,14 L8,12 L6,12 L6,14 L6,14 Z M3,8 L5,8 L5,6 L3,6 L3,8 L3,8 Z M3,2 L5,2 L5,0 L3,0 L3,2 L3,2 Z M6,11 L8,11 L8,9 L6,9 L6,11 L6,11 Z M3,14 L5,14 L5,12 L3,12 L3,14 L3,14 Z M0,5 L2,5 L2,3 L0,3 L0,5 L0,5 Z M0,14 L2,14 L2,12 L0,12 L0,14 L0,14 Z M0,2 L2,2 L2,0 L0,0 L0,2 L0,2 Z M0,8 L2,8 L2,6 L0,6 L0,8 L0,8 Z M6,8 L8,8 L8,6 L6,6 L6,8 L6,8 Z M0,11 L2,11 L2,9 L0,9 L0,11 L0,11 Z M12,11 L14,11 L14,9 L12,9 L12,11 L12,11 Z M12,14 L14,14 L14,12 L12,12 L12,14 L12,14 Z M12,8 L14,8 L14,6 L12,6 L12,8 L12,8 Z M12,5 L14,5 L14,3 L12,3 L12,5 L12,5 Z M12,0 L12,2 L14,2 L14,0 L12,0 L12,0 Z M6,2 L8,2 L8,0 L6,0 L6,2 L6,2 Z M9,2 L11,2 L11,0 L9,0 L9,2 L9,2 Z M6,5 L8,5 L8,3 L6,3 L6,5 L6,5 Z M9,14 L11,14 L11,12 L9,12 L9,14 L9,14 Z M9,8 L11,8 L11,6 L9,6 L9,8 L9,8 Z" transform="translate(2 2)" opacity=".54"/>
</g><g transform="translate(162,36)"><g fill="none" fill-rule="evenodd">
    <polygon points="0 0 18 0 18 18 0 18"/>
    <path fill="#000000" d="M4,4 L1,4 L1,6 L4,6 L4,12.0018986 C4,13.1054196 4.88665231,14 5.99797957,14 L11.9995124,14 L11.9995124,17 L13.9970343,17 L13.9970343,14 L17,14 L17,12.0193564 L6,12 L6,1 L4,1 L4,4 Z M7,6 L7,4 L11.9940809,4 C13.1019194,4 14,4.88655484 14,6.00591905 L14,11 L12,11 L12,6.00009578 L7,6 Z"/>
  </g>
</g><g transform="translate(144,198)"><path fill="#FFFFFF" fill-rule="evenodd" d="M3,6.4375 L3,16.125 C3,16.60625 3.39375,17 3.875,17 L16.125,17 C16.60625,17 17,16.60625 17,16.125 L17,3.875 C17,3.39375 16.60625,3 16.125,3 L6.4375,3 L9,3 L9,5 L15,5 L15,15 L5,15 L5,9 L3,9 L3,6.4375 Z M6,14 L6,9 L8,9 L8,14 L6,14 Z M9,14 L9,6 L11,6 L11,14 L9,14 Z M5,3 L5,0 L3,0 L3,3 L0,3 L0,5 L3,5 L3,8 L5,8 L5,5 L8,5 L8,3 L5,3 Z M12,14 L12,10 L14,10 L14,14 L12,14 Z"/>
</g><g transform="translate(54,198)"><path fill="#000000" fill-rule="evenodd" d="M10.25,2 L6,11.0140969 L8,11.0140969 L8.5,9.8 L13.5,9.8 L14,11 L16,11.0140969 L11.75,2 L10.25,2 Z M11,4 L12.87,8.02 L9.13,8.02 L11,4 Z M6,12 L4,12 L4,2.13162821e-14 L2,2.13162821e-14 L2,12 L4.4408921e-15,12 L3,15 L6,12 Z" transform="translate(1 2)"/>
</g><g transform="translate(274,360)"><g fill="none" fill-rule="evenodd" transform="translate(1 2)">
    <path fill="#E3E3E3" fill-rule="nonzero" d="M0,0 L16,0 L16,7 L0,7 L0,0 Z M5,2 C5,2.55613518 5.44358641,3 5.99077797,3 L10.009222,3 C10.5490248,3 11,2.55228475 11,2 C11,1.44386482 10.5564136,1 10.009222,1 L5.99077797,1 C5.45097518,1 5,1.44771525 5,2 Z"/>
    <path fill="#EEEEEE" fill-rule="nonzero" d="M0,7 L16,7 L16,12.9970707 C16,13.5509732 15.5553691,14 14.9991283,14 L1.00087166,14 C0.448105505,14 0,13.5621186 0,12.9970707 L0,7 Z"/>
    <path fill="#DB4437" fill-rule="nonzero" d="M13.9333333,10 C12.8666667,7.8 10.6,6.33333333 8,6.33333333 C5.93333333,6.33333333 4.06666667,7.26666667 2.86666667,8.73333333 L5.06666667,12.5333333 C5.26666667,11.1333333 6.53333333,10 8,10 L13.9333333,10 Z"/>
    <path fill="#FFC107" d="M11,13 C11,13.340036 10.945801,13.6945195 10.8374031,14 L14.5898454,14 C14.6404232,13.6706304 14.6666667,13.3429425 14.6666667,13 C14.6666667,12.3234979 14.5713218,11.636189 14.3806322,11 C14.3806322,11 10.2210197,11.0025248 10.2210197,11.0025248 C10.6928839,11.5295641 11,12.2428452 11,13 Z"/>
    <path fill="#0F9D58" d="M4.68345602,14 C3.84939541,12.5728296 2.18127421,9.70816121 2.18127421,9.70816121 C1.64681549,10.6915234 1.33333333,11.8196382 1.33333333,13 C1.33333333,13.3378006 1.35798093,13.6761884 1.40561092,14 L4.68345602,14 Z"/>
    <path fill="#4285F4" fill-rule="nonzero" d="M9.73533564,14 C9.90371179,13.7069662 10,13.3622127 10,13 C10,11.8954305 9.1045695,11 8,11 C6.8954305,11 6,11.8954305 6,13 C6,13.3643447 6.09742508,13.7059346 6.2676451,14.0001395 L9.73533564,14 Z"/>
  </g>
</g><g transform="translate(126,36)"><g fill="none" fill-rule="evenodd" transform="rotate(-90 9 9)">
    <polygon points="0 0 18 0 18 18 0 18"/>
    <path fill="#000" d="M2,16 L4,16 L4,14 L2,14 L2,16 L2,16 Z M8,16 L10,16 L10,14 L8,14 L8,16 L8,16 Z M5,16 L7,16 L7,14 L5,14 L5,16 L5,16 Z M2,13 L4,13 L4,11 L2,11 L2,13 L2,13 Z M2,7 L4,7 L4,5 L2,5 L2,7 L2,7 Z M2,10 L4,10 L4,8 L2,8 L2,10 L2,10 Z M14,10 L16,10 L16,8 L14,8 L14,10 L14,10 Z M14,13 L16,13 L16,11 L14,11 L14,13 L14,13 Z M14,7 L16,7 L16,5 L14,5 L14,7 L14,7 Z M11,16 L13,16 L13,14 L11,14 L11,16 L11,16 Z M14,16 L16,16 L16,14 L14,14 L14,16 L14,16 Z" opacity=".54"/>
    <polygon fill="#000" points="2 2 2 4 16 4 16 2"/>
  </g>
</g><g transform="translate(382,202)"><path fill="#000000" fill-rule="evenodd" d="M3,3 L14,3 C14.5522847,3 15,3.44771525 15,4 L15,12 C15,12.5522847 14.5522847,13 14,13 L3,13 C2.44771525,13 2,12.5522847 2,12 L2,4 L2,4 C2,3.44771525 2.44771525,3 3,3 L3,3 Z M5,13 L8,13 L5,16 L5,13 Z"/>
</g><g transform="translate(126,180)"><path fill="#000000" fill-rule="evenodd" d="M5.5,12.9170416 C2.66228666,12.4409635 0.5,9.97299629 0.5,7 L2,7 C2,9.48528137 4.01471863,11.5 6.5,11.5 C8.98528137,11.5 11,9.48528137 11,7 L12.5,7 C12.5,9.97299629 10.3377133,12.4409635 7.5,12.9170416 L7.5,16 L5.5,16 L5.5,12.9170416 Z M6.5,10 C5.11666667,10 4,8.88333333 4,7.5 L4,2.5 C4,1.11666667 5.11666667,0 6.5,0 C7.88333333,0 9,1.11666667 9,2.5 L8.99166667,7.5 C8.99166667,8.88333333 7.88333333,10 6.5,10 Z" transform="translate(3 1)"/>
</g><g transform="translate(482,292)"><path fill="#000000" fill-rule="evenodd" d="M10.2675644,11 L9.9951185,11 C8.89300027,11 8,10.1052289 8,8.99967027 L8,5.00032973 C8,2.79517529 6.21014773,1 4.0048815,1 L3.73243561,1 C3.38662619,0.40219863 2.74028236,0 2,0 C0.8954305,0 0,0.8954305 0,2 C0,3.1045695 0.8954305,4 2,4 C2.74028236,4 3.38662619,3.59780137 3.73243561,3 L4.0048815,3 C5.10424652,3 6,3.89841745 6,5.00032973 L6,8.99967027 C6,11.2089124 7.78754286,13 9.9951185,13 L10.2675644,13 C10.6133738,13.5978014 11.2597176,14 12,14 C13.1045695,14 14,13.1045695 14,12 C14,10.8954305 13.1045695,10 12,10 C11.2597176,10 10.6133738,10.4021986 10.2675644,11 Z" transform="translate(2 2)"/>
</g><g transform="translate(54,18)"><g fill="#000000" fill-rule="evenodd" transform="translate(2 2)">
    <path d="M8,3 L6,3 L6,5 L8,5 L8,3 L8,3 Z M11,6 L9,6 L9,8 L11,8 L11,6 L11,6 Z M8,6 L6,6 L6,8 L8,8 L8,6 L8,6 Z M8,9 L6,9 L6,11 L8,11 L8,9 L8,9 Z M5,6 L3,6 L3,8 L5,8 L5,6 L5,6 Z" opacity=".54"/>
    <path d="M0,0 L14,0 L14,14 L0,14 L0,0 Z M12,12 L12,2 L2,2 L2,12 L12,12 Z"/>
  </g>
</g><g transform="translate(572,36)"><path fill="#000000" fill-rule="evenodd" d="M15,3 C15.553,3 16,2.553 16,2 L16,1 C16,0.447 15.553,0 15,0 L13,0 L13,1 L15,1 L15,2 L14,2 C13.447,2 13,2.447 13,3 L13,5 L16,5 L16,4 L14,4 L14,3 L15,3 L15,3 Z M0,5 L3.199,5 L2,13 L4,13 L5,5 L7,5 L7,13 L9,13 L9,5 L11,5 L11,3 L0,3 L0,5 L0,5 Z" transform="translate(1 2)"/>
</g><g transform="translate(446,202)"><g fill="#000000" fill-rule="evenodd" transform="translate(2 2)">
    <path d="M3,14 L5,14 L5,12 L3,12 L3,14 L3,14 Z M0,5 L2,5 L2,3 L0,3 L0,5 L0,5 Z M0,2 L2,2 L2,0 L0,0 L0,2 L0,2 Z M3,8 L5,8 L5,6 L3,6 L3,8 L3,8 Z M3,2 L5,2 L5,0 L3,0 L3,2 L3,2 Z M0,14 L2,14 L2,12 L0,12 L0,14 L0,14 Z M0,8 L2,8 L2,6 L0,6 L0,8 L0,8 Z M0,11 L2,11 L2,9 L0,9 L0,11 L0,11 Z M12,0 L12,2 L14,2 L14,0 L12,0 L12,0 Z M12,8 L14,8 L14,6 L12,6 L12,8 L12,8 Z M12,14 L14,14 L14,12 L12,12 L12,14 L12,14 Z M12,5 L14,5 L14,3 L12,3 L12,5 L12,5 Z M12,11 L14,11 L14,9 L12,9 L12,11 L12,11 Z M9,14 L11,14 L11,12 L9,12 L9,14 L9,14 Z M9,8 L11,8 L11,6 L9,6 L9,8 L9,8 Z M9,2 L11,2 L11,0 L9,0 L9,2 L9,2 Z" opacity=".54"/>
    <polygon points="6 14 8 14 8 0 6 0"/>
  </g>
</g><g transform="translate(180,90)"><defs>
    <path id="shapes_plus_a" d="M11,7 L11,2 L7,2 L7,7 L2,7 L2,11 L7,11 L7,16 L11,16 L11,11 L16,11 L16,7 L11,7 Z"/>
    <mask id="shapes_plus_b" width="14" height="14" x="0" y="0" fill="white">
      <use xlink:href="#shapes_plus_a"/>
    </mask>
  </defs>
  <use fill="none" fill-rule="evenodd" stroke="#000000" stroke-width="2" mask="url(#shapes_plus_b)" xlink:href="#shapes_plus_a"/>
</g><g transform="translate(216,324)"><path fill="#000000" fill-rule="evenodd" d="M6,10.5 C3.51,10.5 1.5,8.49 1.5,6 C1.5,3.51 3.51,1.5 6,1.5 C7.24,1.5 8.36,2.02 9.17,2.83 L7,5 L12,5 L12,0 L10.24,1.76 C9.15,0.68 7.66,0 6,0 C2.69,0 0.01,2.69 0.01,6 C0.01,9.31 2.69,12 6,12 C8.97,12 11.43,9.84 11.9,7 L10.38,7 C9.92,9 8.14,10.5 6,10.5 L6,10.5 Z" transform="matrix(-1 0 0 1 15.01 3)"/>
</g><g transform="translate(162,216)"><path fill="#000000" fill-rule="evenodd" d="M16,1 C16,0.45 15.55,0 15,0 L1,0 C0.45,0 0,0.45 0,1 L0,12 C0,12.55 0.45,13 1,13 L12.5,13 L16,16 L16,1 L16,1 Z" transform="translate(1 1)"/>
</g><g transform="translate(54,72)"><path fill="#000000" fill-rule="evenodd" d="M9.01902793,9.72705177 C8.06535219,10.5218555 6.83851442,11 5.5,11 C2.46243388,11 0,8.53756612 0,5.5 C0,2.46243388 2.46243388,0 5.5,0 C8.53756612,0 11,2.46243388 11,5.5 C11,6.83851442 10.5218555,8.06535219 9.72705177,9.01902793 L9.98,9.27 L10.77,9.27 L14.76,13.27 L13.27,14.76 L9.27,10.77 L9.27,9.98 L9.01902793,9.72705177 Z M5.5,9.5 C7.709139,9.5 9.5,7.709139 9.5,5.5 C9.5,3.290861 7.709139,1.5 5.5,1.5 C3.290861,1.5 1.5,3.290861 1.5,5.5 C1.5,7.709139 3.290861,9.5 5.5,9.5 Z M3,6 L3,5 L8,5 L8,6 L3,6 Z" transform="translate(2 2)"/>
</g><g transform="translate(72,324)"><path fill="#000000" fill-rule="evenodd" d="M0,12.88 L0,16 L3.12,16 L12,7.12 L8.88,4 L0,12.88 L0,12.88 Z M14.76,4.37 C15.09,4.04 15.09,3.52 14.76,3.19 L12.81,1.24 C12.48,0.91 11.96,0.91 11.63,1.24 L10,2.88 L13.12,6 L14.76,4.37 L14.76,4.37 Z" transform="translate(2)"/>
</g><g transform="translate(90,18)"><path fill="#DB4437" fill-rule="evenodd" d="M15,0 L1,0 C0.5,0 0,0.5 0,1 L0,15 C0,15.5 0.5,16 1,16 L15,16 C15.5,16 16,15.5 16,15 L16,1 C16,0.5 15.5,0 15,0 L15,0 Z M13,13 L8,13 L8,8.2 C7.5,8.7 6.8,9 6,9 C4.3,9 3,7.7 3,6 C3,4.3 4.3,3 6,3 C7.7,3 9,4.3 9,6 C9,6.8 8.7,7.5 8.2,8 L13,8 L13,13 L13,13 Z" transform="translate(1 1)"/>
</g><g transform="translate(234,198)"><path fill="#000000" fill-rule="evenodd" d="M3,6.4375 L3,16.125 C3,16.60625 3.39375,17 3.875,17 L16.125,17 C16.60625,17 17,16.60625 17,16.125 L17,3.875 C17,3.39375 16.60625,3 16.125,3 L6.4375,3 L9,3 L9,5 L15,5 L15,15 L5,15 L5,9 L3,9 L3,6.4375 Z M6,14 L6,9 L8,9 L8,14 L6,14 Z M9,14 L9,6 L11,6 L11,14 L9,14 Z M5,3 L5,0 L3,0 L3,3 L0,3 L0,5 L3,5 L3,8 L5,8 L5,5 L8,5 L8,3 L5,3 Z M12,14 L12,10 L14,10 L14,14 L12,14 Z"/>
</g><g transform="translate(198,306)"><path fill="#000000" fill-rule="evenodd" d="M9,2 L7,0 L1,0 C0.45,0 0,0.45 0,1 L0,13 C0,13.55 0.45,14 1,14 L15,14 C15.55,14 16,13.55 16,13 L16,3 C16,2.45 15.55,2 15,2 L9,2 Z M10.5,4.75 C11.47,4.75 12.25,5.53 12.25,6.5 C12.25,7.47 11.47,8.25 10.5,8.25 C9.53,8.25 8.75,7.47 8.75,6.5 C8.75,5.53 9.53,4.75 10.5,4.75 L10.5,4.75 Z M14,12 L7,12 L7,10.75 C7,9.58 9.33,9 10.5,9 C11.67,9 14,9.58 14,10.75 L14,12 L14,12 Z" transform="translate(1 2)"/>
</g><g transform="translate(220,396)"><path fill="#000000" fill-rule="evenodd" d="M2,5 L4.00104344,5 C4.55275191,5 5,4.55733967 5,4.00104344 L5,2.99895656 C5,2.44724809 4.55733967,2 4.00104344,2 L2,2 L2,5 Z M7,3.25 C7,1.52125 6.1025,0 4.375,0 L0,0 L0,12 L2,12 L1.93375,7.0025 L3,7 C3.56875,7 5,9.43125 5,10 L5,12 L7,12 L7,10 C7,9.3875 6.806875,8.190625 5.78125,7.12875 C6.789375,6.61125 7,5.713125 7,4.5 L7,3.25 Z M9,0 L9,1 L11,1 L11,2 L10,2 C9.448,2 9,2.4475 9,3 L9,5 L12,5 L12,4 L10,4 L10,3 L11,3 C11.5525,3 12,2.5525 12,2 L12,1 C12,0.4475 11.5525,0 11,0 L9,0 Z" transform="translate(3 3)"/>
</g><g transform="translate(72,234)"><path fill="#000000" fill-rule="evenodd" d="M12,9 C12,5.67 7,0 7,0 C7,0 6.15,0.97 5.15,2.33 L11.98,9.16 L12,9 L12,9 Z M1.55,1.27 L0.27,2.55 L3.16,5.44 C2.49,6.69 2,7.96 2,9 C2,11.76 4.24,14 7,14 C8.31,14 9.49,13.48 10.39,12.66 L12.73,15 L14,13.73 L1.55,1.27 L1.55,1.27 Z" transform="translate(2 2)"/>
</g><g transform="translate(126,306)"><polygon fill="#000000" fill-rule="evenodd" points="-2 2 2 6 6 2" transform="rotate(-90 8 3)"/>
</g><g transform="translate(36,234)"><g fill="none" fill-rule="evenodd">
    <polygon fill="#000" fill-rule="nonzero" points="4 9 8 9 8 11 10 11 10 9 14 9 14 11 16 11 16 7 15 7 14 7 10 7 8 7 4 7 3 7 2 7 2 11 4 11"/>
    <circle cx="9" cy="15" r="2" fill="#000" fill-rule="nonzero"/>
    <circle cx="15" cy="15" r="2" fill="#000" fill-rule="nonzero"/>
    <circle cx="3" cy="15" r="2" fill="#000" fill-rule="nonzero"/>
    <rect width="18" height="18"/>
    <circle cx="9" cy="3" r="2" fill="#000" fill-rule="nonzero"/>
  </g>
</g><g transform="translate(90,360)"><path fill="#000000" fill-rule="evenodd" d="M9.01902793,9.72705177 C8.06535219,10.5218555 6.83851442,11 5.5,11 C2.46243388,11 0,8.53756612 0,5.5 C0,2.46243388 2.46243388,0 5.5,0 C8.53756612,0 11,2.46243388 11,5.5 C11,6.83851442 10.5218555,8.06535219 9.72705177,9.01902793 L9.98,9.27 L10.77,9.27 L14.76,13.27 L13.27,14.76 L9.27,10.77 L9.27,9.98 L9.01902793,9.72705177 Z M5.5,9.5 C7.709139,9.5 9.5,7.709139 9.5,5.5 C9.5,3.290861 7.709139,1.5 5.5,1.5 C3.290861,1.5 1.5,3.290861 1.5,5.5 C1.5,7.709139 3.290861,9.5 5.5,9.5 Z" transform="translate(2 2)"/>
</g><g transform="translate(144,54)"><path fill="#000000" fill-rule="evenodd" d="M0,14 L3,14 L3,5 L0,5 L0,14 L0,14 Z M14.7325,5 L10,5 L10.48,1.8225 L10.5025,1.5825 C10.5025,1.275 10.375,0.99 10.1725,0.7875 L9.3775,0 L4.4425,4.9425 C4.165,5.2125 4,5.5875 4,6 L4,13.5 C4,14.325 4.675,15 5.5,15 L12.25,15 C12.8725,15 13.405,14.625 13.63,14.085 L15.895,8.7975 C15.9625,8.625 16,8.445 16,8.25 C16,8.25 15.9277936,7.02252557 16,6.5 C16.0052936,6.46169224 16,6.5675 16,6.5675 L16,6.5 C16,5.675 15.5575,5 14.7325,5 Z" transform="rotate(180 8.501 8.5)"/>
</g><g transform="translate(518,36)"><path fill="#000000" fill-rule="evenodd" d="M2,2 L0,2 L0,12.9975267 C0,13.544239 0.448822582,14 1.00247329,14 L12,14 L12,12 L12.9975267,12 C13.544239,12 14,11.5511774 14,10.9975267 L14,1.00247329 C14,0.455760956 13.5511774,0 12.9975267,0 L3.00247329,0 C2.45576096,0 2,0.448822582 2,1.00247329 L2,2 L1,2 L1,13 L12,13 L12,12 L3.00247329,12 C2.44882258,12 2,11.544239 2,10.9975267 L2,2 Z M5,4 L8.59,4 L4.76,7.83 L6.17,9.24 L10,5.41 L10,9 L12,9 L12,2 L5,2 L5,4 Z" transform="translate(2 2)"/>
</g><g transform="translate(72,342)"><polygon fill="#000000" fill-rule="evenodd" points="5 0 .5 4.5 1.56 5.56 5 2.12 8.44 5.56 9.5 4.5" transform="translate(4 6)"/>
</g><g transform="translate(180,324)"><path fill="#000000" fill-rule="evenodd" d="M11.9925,14.0000001 L11.9925,16.0000001 L1.00445557,16.0000001 C1.00445557,16.0000001 0,16.0000001 0,15.0042115 L0,2.00689108 L2,2.00000019 L2.0075,2.00889226 L2.0075,12.9946834 C2.0075,13.7129334 2.16375,14.0000001 2.9925,14.0000001 L11.9925,14.0000001 Z M12,5.00000009 L12,10.4461539 C12,10.9434039 11.9584375,11.0000001 11.475,11.0000001 L5.520625,11.0000001 C5.0371875,11.0000001 5,10.9434039 5,10.4461539 L5.004375,2.55384625 C5.004375,2.05659625 5.0415625,2.00000009 5.525,2.00000009 L9,2.00000009 L9,5.00000009 L12,5.00000009 Z M4,9.27183272e-08 C4,9.27183272e-08 3,9.27183272e-08 3,1.00158713 L3,12.0000002 C3,13.0000001 4.00701904,13.0000001 4,13.0000001 L12.9976196,13.0000001 C13.7592811,13.0000001 14,12.7112604 14,12.0000001 L14,3.44283088 L10.3525798,9.27183272e-08 L4,9.27183272e-08 Z" transform="matrix(1 0 0 -1 2 17)"/>
</g><g transform="translate(274,378)"><path fill="#000000" fill-rule="evenodd" d="M1,8 L1,12 L13,12 L13,0 L1,0 L1,4 L0,4 L0,8 L1,8 Z M3,8 L3,10 L13,10 L13,2 L3,2 L3,4 L4,4 L4,8 L3,8 Z M4,5 L13,5 L13,7 L4,7 L4,5 Z" transform="translate(2 3)"/>
</g><g transform="translate(126,342)"><path fill="#000000" fill-rule="evenodd" d="M0,1.00684547 C0,0.450780073 0.449948758,0 1.00684547,0 L12.9931545,0 C13.5492199,0 14,0.449948758 14,1.00684547 L14,12.9931545 C14,13.5492199 13.5500512,14 12.9931545,14 L1.00684547,14 C0.450780073,14 0,13.5500512 0,12.9931545 L0,1.00684547 Z M2,2 L6,2 L6,6 L2,6 L2,2 Z M2,8 L6,8 L6,12 L2,12 L2,8 Z M8,2 L12,2 L12,6 L8,6 L8,2 Z M8,8 L12,8 L12,12 L8,12 L8,8 Z" transform="translate(2 2)"/>
</g><g transform="translate(310,18)"><path fill="#000000" fill-rule="evenodd" d="M14.0625,0 L0.9375,0 C0.421875,0 0,0.39375 0,0.875 L0,13.125 C0,13.60625 0.421875,14 0.9375,14 L14.0625,14 C14.578125,14 15,13.60625 15,13.125 L15,0.875 C15,0.39375 14.578125,0 14.0625,0 L14.0625,0 Z M5,12 L2,12 L2,6 L5,6 L5,12 L5,12 Z M2,5 L2,2 L13,2 L13,5 L2,5 Z M9,12 L6,12 L6,6 L9,6 L9,12 L9,12 Z M13,12 L10,12 L10,6 L13,6 L13,12 L13,12 Z" transform="translate(2 2)"/>
</g><g transform="translate(108,108)"><path fill="#000000" fill-rule="evenodd" d="M0,6 L14,6 L14,8 L0,8 L0,6 Z M0,0 L14,0 L14,4 L0,4 L0,0 Z M0,11 L14,11 L14,12 L0,12 L0,11 Z" transform="translate(2 3)"/>
</g><g transform="translate(572,198)"><path fill="#000000" fill-rule="evenodd" d="M13,9 L13,5 L8,0 L2,0 C1.45,0 1.01,0.45 1.01,1 L1.00571429,7 L3,7 L3,2 L8,2 L8,5 L11,5 L11,7 L13,7 L13,9 L11,9 L11,11 L13,11 L13,13 L11,13 L11,14 L3,14 L3,13 L1.00142857,13 L1,15 C1,15.55 1.44,16 2,16 L12,16 C12.55,16 13,15.55 13,15 L13,11 L14,11 L14,9 L13,9 Z M0,9 L3,9 L3,11 L0,11 L0,9 Z M5,9 L9,9 L9,11 L5,11 L5,9 Z" transform="translate(2 1)"/>
</g><g transform="translate(500,166)"><path fill="#000000" fill-rule="evenodd" d="M6,10 L14,10 L14,8 L6,8 L6,10 Z M6,6 L14,6 L14,4 L6,4 L6,6 Z M14,12 L0,12 L0,14 L14,14 L14,12 Z M0.5,7 L4,10.5 L4,3.5 L0.5,7 Z M0,0 L0,2 L14,2 L14,0 L0,0 Z" transform="translate(2 2)"/>
</g><g transform="translate(572,234)"><path fill="#000000" fill-rule="evenodd" d="M0,0 L16,0 L16,16 L0,16 L0,0 Z M1,1 L5,1 L5,15 L1,15 L1,1 Z M6,1 L10,1 L10,15 L6,15 L6,1 Z M11,1 L15,1 L15,15 L11,15 L11,1 Z" transform="translate(1 1)"/>
</g><g transform="translate(328,244)"><g fill="#000000" fill-rule="evenodd" transform="translate(8 2)">
    <rect width="2" height="14" fill-opacity=".3"/>
    <path d="M0,2 L2,2 L2,4 L0,4 L0,2 Z M0,6 L2,6 L2,8 L0,8 L0,6 Z M0,10 L2,10 L2,12 L0,12 L0,10 Z"/>
  </g>
</g><g transform="translate(108,270)"><path fill="#000000" fill-rule="evenodd" d="M4,6 L4,11 L6,11 L6,1 L7,1 L7,11 L9,11 L9,1 L10,1 L10,0 L4,0 C2.34,0 1,1.34 1,3 C1,4.66 2.34,6 4,6 Z M14,14 L11,11 L11,13 L0,13 L0,15 L11,15 L11,17 L14,14 Z" transform="translate(2 1)"/>
</g><g transform="translate(446,292)"><path fill="#000000" fill-rule="evenodd" d="M15,0 L1,0 C0.5,0 0,0.5 0,1 L0,15 C0,15.5 0.5,16 1,16 L15,16 C15.5,16 16,15.5 16,15 L16,1 C16,0.5 15.5,0 15,0 L15,0 Z M13,13 L8,13 L8,8.2 C7.5,8.7 6.8,9 6,9 C4.3,9 3,7.7 3,6 C3,4.3 4.3,3 6,3 C7.7,3 9,4.3 9,6 C9,6.8 8.7,7.5 8.2,8 L13,8 L13,13 L13,13 Z" transform="translate(1 1)"/>
</g><g transform="translate(446,0)"><path fill="#000000" fill-rule="evenodd" d="M6,10.5 C3.51,10.5 1.5,8.49 1.5,6 C1.5,3.51 3.51,1.5 6,1.5 C7.24,1.5 8.36,2.02 9.17,2.83 L7,5 L12,5 L12,0 L10.24,1.76 C9.15,0.68 7.66,0 6,0 C2.69,0 0.01,2.69 0.01,6 C0.01,9.31 2.69,12 6,12 C8.97,12 11.43,9.84 11.9,7 L10.38,7 C9.92,9 8.14,10.5 6,10.5 L6,10.5 Z" transform="translate(3 3)"/>
</g><g transform="translate(346,220)"><path fill="#000000" fill-rule="evenodd" d="M6,10 L14,10 L14,8 L6,8 L6,10 L6,10 Z M3.5,7 L0,3.5 L0,10.5 L3.5,7 L3.5,7 Z M0,14 L14,14 L14,12 L0,12 L0,14 L0,14 Z M0,0 L0,2 L14,2 L14,0 L0,0 L0,0 Z M6,6 L14,6 L14,4 L6,4 L6,6 L6,6 Z" transform="translate(2 2)"/>
</g><g transform="translate(310,0)"><polygon fill="#000000" fill-rule="evenodd" points="10 0 0 0 0 1.8 5.5 7 0 12.2 0 14 10 14 10 12 3.1 12 8 7 3.1 2 10 2" transform="translate(4 2)"/>
</g><g transform="translate(288,324)"><path fill="#000000" fill-rule="nonzero" d="M5.32790732,7.48070712 L4.31270732,6.45830712 C3.66470732,7.29350712 3.26150732,8.25830712 3.13910732,9.25910712 L4.59350732,9.25910712 C4.69430732,8.63270712 4.94630732,8.02070712 5.32790732,7.48070712 L5.32790732,7.48070712 Z M4.59350732,10.6991071 L3.13910732,10.6991071 C3.26150732,11.6999071 3.65750732,12.6647071 4.30550732,13.4999071 L5.32070732,12.4775071 C4.94630732,11.9375071 4.69430732,11.3327071 4.59350732,10.6991071 L4.59350732,10.6991071 Z M5.32070732,14.5295071 C6.15590732,15.1775071 7.12790732,15.5663071 8.12870732,15.6887071 L8.12870732,14.2271071 C7.50230732,14.1191071 6.89750732,13.8743071 6.35750732,13.4855071 L5.32070732,14.5295071 Z M9.56870732,4.26950712 L9.56870732,2.05910712 L6.29270732,5.33510712 L9.56870732,8.53910712 L9.56870732,5.72390712 C11.6135073,6.06950712 13.1687073,7.84070712 13.1687073,9.97910712 C13.1687073,12.1175071 11.6135073,13.8887071 9.56870732,14.2343071 L9.56870732,15.6887071 C12.4127073,15.3359071 14.6087073,12.9167071 14.6087073,9.97910712 C14.6087073,7.04150712 12.4127073,4.62230712 9.56870732,4.26950712 Z" transform="translate(8.873907, 8.873907) rotate(-45.000000) translate(-8.873907, -8.873907) "/>
</g><g transform="translate(198,72)"><path fill="#000000" fill-rule="evenodd" d="M7.99905882,0 C4.32752941,0 1.23670588,2.49323077 0,6 C1.23670588,9.50676923 4.32752941,12 7.99905882,12 C11.6724706,12 14.7595294,9.50676923 16,6 C14.7595294,2.49323077 11.6724706,0 7.99905882,0 Z M8,10.5 C10.4852814,10.5 12.5,8.48528137 12.5,6 C12.5,3.51471863 10.4852814,1.5 8,1.5 C5.51471863,1.5 3.5,3.51471863 3.5,6 C3.5,8.48528137 5.51471863,10.5 8,10.5 Z M8,8.5 C9.38071187,8.5 10.5,7.38071187 10.5,6 C10.5,4.61928813 9.38071187,3.5 8,3.5 C6.61928813,3.5 5.5,4.61928813 5.5,6 C5.5,7.38071187 6.61928813,8.5 8,8.5 Z" transform="translate(1 3)"/>
</g><g transform="translate(572,270)"><path fill="#4285F4" fill-rule="evenodd" d="M0,1.00684547 C0,0.450780073 0.455664396,0 0.995397568,0 L9.00460243,0 C9.55434533,0 10,0.449948758 10,1.00684547 L10,14 L5,12 L0,14 L0,1.00684547 Z" transform="translate(4 2)"/>
</g><g transform="translate(108,324)"><polygon fill="#000000" fill-rule="evenodd" points="6.4 5.4 9.8 8.8 16.1 1.6 14.9 .4 9.9 6.3 6.4 2.8 0 9.3 1.3 10.6" transform="translate(1 4)"/>
</g><g transform="translate(446,166)"><path fill="#000000" fill-rule="evenodd" d="M8,0 C3.57647059,0 0,3.57647059 0,8 C0,12.4235294 3.57647059,16 8,16 C12.4235294,16 16,12.4235294 16,8 C16,3.57647059 12.4235294,0 8,0 L8,0 Z M8,14.5 C4.46323529,14.5 1.5,11.5367647 1.5,8 C1.5,4.46323529 4.46323529,1.5 8,1.5 C11.5367647,1.5 14.5,4.46323529 14.5,8 C14.5,11.5367647 11.5367647,14.5 8,14.5 L8,14.5 Z M9,12 C9,12.553 8.553,13 8,13 C7.447,13 7,12.553 7,12 C7,11.447 7.447,11 8,11 C8.553,11 9,11.447 9,12 Z M5,6 C5.12023926,6.01531982 6.5,6.01849365 6.5,6.01849365 C6.5,5.26839431 7.00215776,4.51556396 8.00002153,4.5 C8.9978853,4.48443604 9.54266357,5.38989258 9.5,6.00317383 C9.45733643,6.61645508 9.17871094,7.05200195 8.77172852,7.29766846 C8.36474609,7.54333496 7.25,8.14459229 7.25,9.23382568 C7.25,9.23382568 7.25,9.98764352 7.25,9.98764352 L8.75,9.98764352 C8.75,9.98764352 8.75,9.37896729 8.77172852,9.37896729 C8.77081299,8.65667725 11.0114136,8.30938721 11,6.01849365 C10.9900891,4.02920829 9.45849609,3 7.99993896,3 C6.54138184,3 5,4.18981934 5,6 Z" transform="translate(1 1)"/>
</g><g transform="translate(500,256)"><path fill="#000000" fill-rule="evenodd" d="M5.08196626,9.37727765 L5.99955419,8 L5.0815666,6.62291941 C4.62836385,6.85296857 4.11835777,6.98725058 3.57817961,6.99913216 C3.55122348,7.00107303 3.52503985,7.00208268 3.49974771,7.00208268 C3.44586152,7.00208268 3.39177256,7.00013278 3.33755121,6.99630282 C1.49448789,6.91222827 0.0231246995,5.40247032 0.000269927977,3.54391978 C6.92469045e-05,3.53097907 -1.98246352e-05,3.51804863 3.68240513e-06,3.50512942 C1.22774602e-06,3.50341893 0,3.50170815 0,3.49999708 C0,3.49829098 1.22063079e-06,3.49658516 3.66106665e-06,3.49487963 C-1.97109738e-05,3.48199716 6.88547616e-05,3.4691033 0.000268397483,3.45619902 C0.0235736053,1.55561775 1.56164322,0.0197518958 3.46297798,0.00018898828 C3.47380467,4.84500079e-05 3.48462385,-1.38757964e-05 3.49543495,2.57804707e-06 C3.49686604,8.59511651e-07 3.49829733,0 3.49972882,0 C3.50116736,0 3.5026057,8.6800093e-07 3.50404384,2.60350766e-06 C3.51491367,-1.40208481e-05 3.52579267,4.89893362e-05 3.53668024,0.000191057626 C5.38088977,0.0192698907 6.88328742,1.46490662 6.99303415,3.28618166 C6.99855951,3.35770064 7.00076239,3.42903042 6.99947653,3.5000104 C7.0295035,3.93876004 6.99947671,4.50000957 6.99947671,4.50000957 L16,14.0000016 L16,14.9952401 L12.9990117,14.9952401 L8,10.000005 L6.52775458,10.7439587 C6.82709523,11.2590932 6.9987709,11.8576041 6.99945558,12.4961622 C6.99946966,12.4974444 6.99947671,12.4987247 6.99947671,12.5000029 C6.99947671,12.505048 6.99945964,12.5100948 6.99942556,12.5151433 C6.99127795,14.4383685 5.43207235,15.9954569 3.50814196,15.9999843 C3.50534334,15.9999948 3.50254523,16 3.49974765,16 C3.49697112,16 3.49419407,15.9999948 3.49141651,15.9999845 C1.56239166,15.9954995 0,14.4302204 0,12.4999971 C0,10.5670021 1.56688196,9 3.49972882,9 C4.06913668,9 4.60678373,9.13599458 5.08196626,9.37727765 Z M12.9843643,1.04188257 L8.99932157,5.00000915 L11.0037439,7.00000748 L15.9987792,2.00001165 L15.9987792,1.00001249 L12.9843643,1.04188257 Z M3.49974771,5.00000915 C4.32811065,5.00000915 4.99963149,4.32843684 4.99963149,3.5000104 C4.99963149,2.67158397 4.32811065,2.00001165 3.49974771,2.00001165 C2.67138477,2.00001165 1.99986393,2.67158397 1.99986393,3.5000104 C1.99986393,4.32843684 2.67138477,5.00000915 3.49974771,5.00000915 Z M3.49974771,14.0000016 C4.32811065,14.0000016 4.99963149,13.3284293 4.99963149,12.5000029 C4.99963149,11.6715765 4.32811065,11.0000041 3.49974771,11.0000041 C2.67138477,11.0000041 1.99986393,11.6715765 1.99986393,12.5000029 C1.99986393,13.3284293 2.67138477,14.0000016 3.49974771,14.0000016 Z" transform="translate(1 1)"/>
</g><g transform="translate(108,162)"><path fill="#FF7537" fill-rule="evenodd" d="M8.5,5 L6,5 L6,8 L8.5,8 C9.32842712,8 10,7.32842712 10,6.5 C10,5.67157288 9.32842712,5 8.5,5 Z M8.5,10 L6,10 L6,13 L4,13 L4,3 L5,3 L8.5,3 C10.4329966,3 12,4.56700338 12,6.5 C12,8.43299662 10.4329966,10 8.5,10 Z M1,0 C0.5,0 0,0.5 0,1 L0,15 C0,15.5 0.5,16 1,16 L15,16 C15.5,16 16,15.5 16,15 L16,1 C16,0.5 15.5,0 15,0 L1,0 Z" transform="translate(1 1)"/>
</g><g transform="translate(370,298)"><path fill="#000000" fill-rule="evenodd" d="M6.5,3.62 L0,10.12 L0,13 L2.88,13 L9.38,6.5 L6.5,3.62 Z M11.85,4.02 C12.05,3.82 12.05,3.51 11.85,3.31 L9.68,1.14 C9.48,0.94 9.17,0.94 8.97,1.14 L7.62,2.5 L10.5,5.38 L11.85,4.02 L11.85,4.02 Z" transform="translate(4)"/>
</g><g transform="translate(162,324)"><path fill="#000000" fill-rule="evenodd" d="M2,11.8994949 L11.8994949,2 L14.0208153,4.12132034 L4.12132034,14.0208153 L2,11.8994949 Z M5.59875,1.88625 C3.15,3.04875 1.39125,5.43 1.125,8.25 L0,8.25 C0.3825,3.63 4.245,0 8.9625,0 C9.13125,0 9.2925,0.015 9.46125,0.02625 L6.6,2.8875 L5.59875,1.88625 Z M10.32625,14.11375 C12.775,12.95125 14.53375,10.57 14.8,7.75 L15.925,7.75 C15.5425,12.37 11.68,16 6.9625,16 C6.79375,16 6.6325,15.985 6.46375,15.97375 L9.325,13.1125 L10.32625,14.11375 L10.32625,14.11375 Z" transform="translate(1 1)"/>
</g><g transform="translate(554,130)"><polygon fill="#4285F4" fill-opacity=".78" fill-rule="evenodd" points="0 0 0 10 5 5" transform="translate(7 4)"/>
</g><g transform="translate(216,270)"><path fill="#000000" fill-rule="evenodd" d="M9.82929429,4 C9.41745788,5.16519237 8.30621883,6 7,6 C5.69378117,6 4.58254212,5.16519237 4.17070571,4 L0,4 L0,2 L4.17070571,2 C4.58254212,0.834807627 5.69378117,0 7,0 C8.30621883,0 9.41745788,0.834807627 9.82929429,2 L14,2 L14,4 L9.82929429,4 Z" transform="translate(2 6)"/>
</g><g transform="translate(198,324)"><path fill="#000000" fill-rule="evenodd" d="M0,2.90909091 L0,7.27272727 C0,11.3090909 2.98666667,15.0836364 7,16 C11.0133333,15.0836364 14,11.3090909 14,7.27272727 L14,2.90909091 L7,0 L0,2.90909091 Z M6,4 L8,4 L8,9 L6,9 L6,4 Z M6,10 L8,10 L8,12 L6,12 L6,10 Z" transform="translate(2 1)"/>
</g><g transform="translate(256,0)"><path fill="#000000" fill-rule="evenodd" d="M15,0 L1,0 C0.45,0 0,0.45 0,1 L0,15 C0,15.55 0.45,16 1,16 L15,16 C15.55,16 16,15.55 16,15 L16,1 C16,0.45 15.55,0 15,0 Z M6,12 L4,12 L4,7 L6,7 L6,12 Z M9,12 L7,12 L7,4 L9,4 L9,12 Z M12,12 L10,12 L10,8 L12,8 L12,12 Z" transform="translate(1 1)"/>
</g><g transform="translate(518,130)"><path fill="#000000" fill-rule="evenodd" d="M9,3 C9.03942871,2.99182129 9,0.991821289 7,0.991821289 C5,0.991821289 5.00058365,2.99182129 5.0005835,2.99182129 L0.999924169,2.99836457 C0.447681299,2.99926779 1.95399252e-14,3.44994876 1.95399252e-14,4.00684547 L1.95399252e-14,15.9931545 C1.95399252e-14,16.5492199 0.441480327,17 0.999355351,17 C0.999355351,17 0.728745117,17 1.55749512,17 L12.5,17 C13.32875,17 12.9985978,17 12.9985978,17 C13.551657,17 14,16.5548273 14,15.9975471 L14,4.49182129 C14,3.66307129 14,3.99416169 14,3.99416169 C14,3.44510135 13.5562834,3 13.0001925,3 L9,3 Z M6,3 L8,3 L8,4 L6,4 L6,3 Z M2,15 L2,5 L3,5 L3,6 L11,6 L11,5 L12,5 L12,15 L2,15 Z" transform="translate(2)"/>
</g><g transform="translate(274,108)"><g fill="none" fill-rule="evenodd">
    <polygon points="0 0 18 0 18 18 0 18"/>
    <path fill="#000000" d="M9.2250619,17 L4.57,17 L7.15,12 L7.59068811,12 C7.5311233,12.3242762 7.5,12.6585035 7.5,13 C7.5,14.5760283 8.16288947,15.9972305 9.2250619,17 Z M16.3110756,8.60793815 L11.83,1 L6.57,1 L10.6956718,8.00453613 C11.3965725,7.68068268 12.1771859,7.5 13,7.5 C14.2431442,7.5 15.3899589,7.91243556 16.3110756,8.60793815 Z M5.49,2.45 L0,12.45 L2.66,17 L8.14,7.02 L5.49,2.45 L5.49,2.45 Z M14,9 L12,9 L12,12 L9,12 L9,14 L12,14 L12,17 L14,17 L14,14 L17,14 L17,12 L14,12 L14,9 Z"/>
  </g>
</g><g transform="translate(328,324)"><path fill="#000000" fill-rule="evenodd" d="M2.5,14 L9.5,14 C10.32875,14 11,13.3607143 11,12.5714286 L11,4 L1,4 L1,12.5714286 C1,13.3607143 1.67125,14 2.5,14 Z M9.125,1 L8.375,0 L3.625,0 L2.875,1 L0,1 L0,3 L12,3 L12,1 L9.125,1 Z" transform="translate(3 2)"/>
</g><g transform="translate(180,162)"><path fill="#000000" d="M11.0119846,13 L16,13 L16,15 L11.0119846,15 L11.0119846,16.9944804 L9.03685276,15.0409865 L7.98433567,14 L9.03289155,12.9629313 L11.0119846,11.0055196 L11.0119846,13 Z M1,8 L2.99632026,8 L2.99632026,10 L1,10 L1,8 Z M3.99264052,3.99686968 C3.99264052,2.34174426 5.33709312,1 6.98804084,1 C8.64235476,1 9.98344117,2.34739093 9.98344117,3.99686968 L9.98344117,7.00313032 C9.98344117,8.65825574 8.63898856,10 6.98804084,10 C5.33372692,10 3.99264052,8.65260907 3.99264052,7.00313032 L3.99264052,3.99686968 Z M5.5,3.99857602 L5.5,7.00142398 C5.5,7.83497024 6.17157288,8.5 7,8.5 C7.83420277,8.5 8.5,7.82906466 8.5,7.00142398 L8.5,3.99857602 C8.5,3.16502976 7.82842712,2.5 7,2.5 C6.16579723,2.5 5.5,3.17093534 5.5,3.99857602 Z"/>
</g><g transform="translate(108,234)"><path fill="#000000" fill-rule="evenodd" d="M1,3 L3,3 L3,12 L1,12 L1,3 Z M11,5 L13,5 L13,12 L11,12 L11,5 Z M6,9 L8,9 L8,12 L6,12 L6,9 Z M10,2 L14,2 L14,4 L10,4 L10,2 Z M0,0 L4,0 L4,2 L0,2 L0,0 Z M0,12 L14,12 L14,14 L0,14 L0,12 Z M5,6 L9,6 L9,8 L5,8 L5,6 Z" transform="translate(2 2)"/>
</g><g transform="translate(468,72)"><path fill="#000000" fill-rule="evenodd" d="M0.27,1.55 L5.43,6.7 L3,12 L5.5,12 L7.14,8.42 L11.73,13 L13,11.73 L1.55,0.27 L0.27,1.55 L0.27,1.55 Z M3.82,0 L5.82,2 L7.58,2 L7.03,3.21 L8.74,4.92 L10.08,2 L14,2 L14,0 L3.82,0 L3.82,0 Z" transform="translate(2 3)"/>
</g><g transform="translate(18,324)"><path fill="#000000" fill-rule="evenodd" d="M11,0 L14,0 L14,2 L11,2 L11,0 Z M7,3 L14,3 L14,5 L7,5 L7,3 Z M7,6 L14,6 L14,8 L7,8 L7,6 Z M7,9 L14,9 L14,11 L7,11 L7,9 Z M0,2 L4,2 L4,11 L6,11 L6,2 L10,2 L10,0 L0,0 L0,2 Z" transform="translate(2 3)"/>
</g><g transform="translate(0,18)"><defs>
    <rect id="shapes_rectangle_a" width="16" height="14" x="1" y="2"/>
    <mask id="shapes_rectangle_b" width="16" height="14" x="0" y="0" fill="white">
      <use xlink:href="#shapes_rectangle_a"/>
    </mask>
  </defs>
  <use fill="none" fill-rule="evenodd" stroke="#000000" stroke-width="2" mask="url(#shapes_rectangle_b)" xlink:href="#shapes_rectangle_a"/>
</g><g transform="translate(464,238)"><path fill="#000000" fill-rule="evenodd" d="M8,0 C3.57647059,0 0,3.57647059 0,8 C0,12.4235294 3.57647059,16 8,16 C12.4235294,16 16,12.4235294 16,8 C16,3.57647059 12.4235294,0 8,0 L8,0 Z M9,12 C9,12.553 8.553,13 8,13 C7.447,13 7,12.553 7,12 C7,11.447 7.447,11 8,11 C8.553,11 9,11.447 9,12 Z M5,6 C5.12023926,6.01531982 6.5,6.01849365 6.5,6.01849365 C6.5,5.26839431 7.00215776,4.51556396 8.00002153,4.5 C8.9978853,4.48443604 9.54266357,5.38989258 9.5,6.00317383 C9.45733643,6.61645508 9.17871094,7.05200195 8.77172852,7.29766846 C8.36474609,7.54333496 7.25,8.14459229 7.25,9.23382568 C7.25,9.23382568 7.25,9.98764352 7.25,9.98764352 L8.75,9.98764352 C8.75,9.98764352 8.75,9.37896729 8.77172852,9.37896729 C8.77081299,8.65667725 11.0114136,8.30938721 11,6.01849365 C10.9900891,4.02920829 9.45849609,3 7.99993896,3 C6.54138184,3 5,4.18981934 5,6 Z" transform="translate(1 1)"/>
</g><g transform="translate(18,252)"><path fill="#000000" fill-rule="evenodd" d="M11.5656391,4.43436088 L9,7 L16,7 L16,0 L13.0418424,2.95815758 C11.5936787,1.73635959 9.72260775,1 7.67955083,1 C4.22126258,1 1.25575599,3.10984908 0,6 L2,7 C2.93658775,4.60974406 5.12943697,3.08011229 7.67955083,3 C9.14881247,3.0528747 10.4994783,3.57862053 11.5656391,4.43436088 Z" transform="translate(1 5)"/>
</g><g transform="translate(464,166)"><path fill="#0F9D58" fill-rule="evenodd" d="M2,10 L4,12 L4,16 L10,16 L10,12 L12,10 L12,6 L2,6 L2,10 Z M6,0 L8,0 L8,3 L6,3 L6,0 Z M12.5,1 L14,2.5 L12,4.5 L10.5,3 L12.5,1 Z M-6.20335912e-15,2.5 L1.5,1 L3.5,3 L2,4.5 L-6.20335912e-15,2.5 Z" transform="translate(2 1)"/>
</g><g transform="translate(288,342)"><path fill="#000000" fill-rule="evenodd" d="M4,14 L14,14 L14,12 L4,12 L4,14 Z M0,10 L14,10 L14,8 L0,8 L0,10 Z M0,0 L0,2 L14,2 L14,0 L0,0 Z M4,6 L14,6 L14,4 L4,4 L4,6 Z" transform="translate(2 2)"/>
</g><g transform="translate(482,202)"><path fill="#000000" fill-rule="evenodd" d="M16,6.25 L6.98590309,2 L6.98590309,4 L8,4.5 L8,9.5 L7,10 L6.98590309,12 L16,7.75 L16,6.25 Z M14,7 L9.98,8.87 L9.98,5.13 L14,7 Z M6,12 L4,12 L4,1.77635684e-15 L2,1.77635684e-15 L2,12 L8.8817842e-16,12 L3,15 L6,12 Z" transform="translate(1 1)"/>
</g><g transform="translate(482,220)"><polygon fill="#000000" fill-rule="evenodd" points="1.415 0 0 1.415 4.585 6 0 10.585 1.415 12 7.415 6" transform="translate(5 3)"/>
</g><g transform="translate(54,162)"><path fill="#000000" fill-rule="evenodd" d="M13,0 L1,0 C0.45,0 0,0.45 0,1 L0,13 C0,13.55 0.45,14 1,14 L13,14 C13.55,14 14,13.55 14,13 L14,1 C14,0.45 13.55,0 13,0 L13,0 Z M6,11 L3,11 L3,9 L6,9 L6,11 L6,11 Z M11,8 L3,8 L3,6 L11,6 L11,8 L11,8 Z M3,5 L3,3 L8,3 L8,5 L3,5 Z" transform="translate(2 2)"/>
</g><g transform="translate(234,270)"><path fill="#F4B400" fill-rule="evenodd" d="M-8.8817842e-16,1.00247329 C-8.8817842e-16,0.448822582 0.443988033,0 1.00605299,0 L8.73769782,0 C9.29332554,0 10.0186468,0.357358932 10.3566484,0.796753675 L13.7463141,5.20324633 C14.0848081,5.64328123 14.0843157,6.35735893 13.7463141,6.79675367 L10.3566484,11.2032463 C10.0181544,11.6432812 9.29976278,12 8.73769782,12 L1.00605299,12 C0.450425264,12 -8.8817842e-16,11.544239 -8.8817842e-16,10.9975267 L-8.8817842e-16,1.00247329 Z" transform="matrix(-1 0 0 1 16 3)"/>
</g><g transform="translate(464,130)"><path fill="#4986E7" fill-rule="evenodd" d="M15,0 L1,0 C0.5,0 0,0.5 0,1 L0,15 C0,15.5 0.5,16 1,16 L15,16 C15.5,16 16,15.5 16,15 L16,1 C16,0.5 15.5,0 15,0 Z M11.4,13 L9.9,13 L7.9,5.5 L5.9,13 L4.4,13 L2,3 L3.7,3 L5.24,10.505 L7.2,3 L8.6,3 L10.57,10.505 L12.1,3 L13.8,3 L11.4,13 Z" transform="translate(1 1)"/>
</g><g transform="translate(108,198)"><path d="M0 0h18v18H0z" fill="none"/>
    <path d="M9 5.5c.83 0 1.5-.67 1.5-1.5S9.83 2.5 9 2.5 7.5 3.17 7.5 4 8.17 5.5 9 5.5zm0 2c-.83 0-1.5.67-1.5 1.5s.67 1.5 1.5 1.5 1.5-.67 1.5-1.5S9.83 7.5 9 7.5zm0 5c-.83 0-1.5.67-1.5 1.5s.67 1.5 1.5 1.5 1.5-.67 1.5-1.5-.67-1.5-1.5-1.5z"/>
</g><g transform="translate(328,160)"><path fill="#000000" fill-rule="evenodd" d="M0,6 L3,6 L3,12 L5,12 L5,6 L8,6 L8,4 L0,4 L0,6 L0,6 Z M6,0 L6,2 L10,2 L10,12 L12,12 L12,2 L16,2 L16,0 L6,0 L6,0 Z" transform="translate(1 3)"/>
</g><g transform="translate(0,36)"><polygon fill="#000000" fill-rule="evenodd" points="11.53 1.53 10.47 .47 6 4.94 1.53 .47 .47 1.53 4.94 6 .47 10.47 1.53 11.53 6 7.06 10.47 11.53 11.53 10.47 7.06 6" transform="translate(3 3)"/>
</g><g transform="translate(500,184)"><path fill="#000000" fill-rule="evenodd" d="M8,0.03 C3.58,0.03 0,3.61 0,8.03 C0,12.45 3.58,16.03 8,16.03 C12.42,16.03 16,12.45 16,8.03 C16,3.61 12.42,0.03 8,0.03 L8,0.03 Z M9,12 L7,12 L7,10 L9,10 L9,12 L9,12 Z M9,9 L7,9 L7,4 L9,4 L9,9 L9,9 Z" transform="translate(1 1)"/>
</g><g transform="translate(572,162)"><path fill="#F4B400" fill-rule="evenodd" d="M-8.8817842e-16,1.00247329 C-8.8817842e-16,0.448822582 0.443988033,0 1.00605299,0 L8.73769782,0 C9.29332554,0 10.0186468,0.357358932 10.3566484,0.796753675 L13.7463141,5.20324633 C14.0848081,5.64328123 14.0843157,6.35735893 13.7463141,6.79675367 L10.3566484,11.2032463 C10.0181544,11.6432812 9.29976278,12 8.73769782,12 L1.00605299,12 C0.450425264,12 -8.8817842e-16,11.544239 -8.8817842e-16,10.9975267 L-8.8817842e-16,1.00247329 Z" transform="translate(2 3)"/>
</g><g transform="translate(554,94)"><path fill="#000000" fill-rule="evenodd" d="M13.9170416,9 C13.4955231,11.5125049 11.5125049,13.4955231 9,13.9170416 L9,9 L13.9170416,9 Z M13.9170416,7 C13.4955231,4.48749513 11.5125049,2.50447689 9,2.08295844 L9,7 L13.9170416,7 Z M7,2.08295844 C4.16228666,2.55903653 2,5.02700371 2,8 C2,10.9729963 4.16228666,13.4409635 7,13.9170416 L7,2.08295844 Z M8,16 C12.418278,16 16,12.418278 16,8 C16,3.581722 12.418278,0 8,0 C3.581722,0 0,3.581722 0,8 C0,12.418278 3.581722,16 8,16 Z" transform="translate(1 1)"/>
</g><g transform="translate(252,270)"><polygon fill="#FFFFFF" fill-rule="evenodd" points="11.53 1.53 10.47 .47 6 4.94 1.53 .47 .47 1.53 4.94 6 .47 10.47 1.53 11.53 6 7.06 10.47 11.53 11.53 10.47 7.06 6" transform="translate(3 3)"/>
</g><g transform="translate(162,144)"><path fill="#000000" fill-rule="evenodd" d="M1,0 L17,0 C17.5522847,-1.01453063e-16 18,0.44771525 18,1 L18,15 C18,15.5522847 17.5522847,16 17,16 L1,16 C0.44771525,16 6.76353751e-17,15.5522847 0,15 L0,1 L0,1 C-6.76353751e-17,0.44771525 0.44771525,1.01453063e-16 1,0 L1,0 Z M2,2 L2,14 L16,14 L16,2 L2,2 Z M13,5.5 L16,8 L13,10.5 L13,5.5 Z M5,5.5 L5,10.5 L2,8 L5,5.5 Z M11.5,5 L6.5,5 L9,2 L11.5,5 Z M6.5,11 L11.5,11 L9,14 L6.5,11 Z" transform="translate(0 1)"/>
</g><g transform="translate(216,72)"><path fill="#FFFFFF" fill-rule="evenodd" d="M6,4 L6,0 L4,0 L4,4 L0,4 L0,6 L4,6 L4,10 L6,10 L6,6 L10,6 L10,4 L6,4 Z" transform="translate(4 4)"/>
</g><g transform="translate(370,160)"><path fill="#000000" fill-rule="evenodd" d="M9.01902793,9.72705177 C8.06535219,10.5218555 6.83851442,11 5.5,11 C2.46243388,11 0,8.53756612 0,5.5 C0,2.46243388 2.46243388,0 5.5,0 C8.53756612,0 11,2.46243388 11,5.5 C11,6.83851442 10.5218555,8.06535219 9.72705177,9.01902793 L9.98,9.27 L10.77,9.27 L14.76,13.27 L13.27,14.76 L9.27,10.77 L9.27,9.98 L9.01902793,9.72705177 Z M5.5,9.5 C7.709139,9.5 9.5,7.709139 9.5,5.5 C9.5,3.290861 7.709139,1.5 5.5,1.5 C3.290861,1.5 1.5,3.290861 1.5,5.5 C1.5,7.709139 3.290861,9.5 5.5,9.5 Z M8,6 L6,6 L6,8 L5,8 L5,6 L3,6 L3,5 L5,5 L5,3 L6,3 L6,5 L8,5 L8,6" transform="translate(2 2)"/>
</g><g transform="translate(292,72)"><path fill="#000000" fill-rule="evenodd" d="M0,1.00684547 C0,0.450780073 0.455664396,-4.56419617e-27 0.995397568,7.17464814e-43 L10,7.6146463e-26 L14,4 L14,13.0046024 C14,13.5543453 13.5500512,14 12.9931545,14 L1.00684547,14 C0.450780073,14 0,13.5500512 0,12.9931545 L0,1.00684547 Z M2,2 L8,2 L8,5 L2,5 L2,2 Z M7,11 C8.1045695,11 9,10.1045695 9,9 C9,7.8954305 8.1045695,7 7,7 C5.8954305,7 5,7.8954305 5,9 C5,10.1045695 5.8954305,11 7,11 Z" transform="translate(2 2)"/>
</g><g transform="translate(126,108)"><path fill="#000000" fill-rule="evenodd" d="M6,4 L6,0 L4,0 L4,4 L0,4 L0,6 L4,6 L4,10 L6,10 L6,6 L10,6 L10,4 L6,4 Z" transform="translate(4 4)"/>
</g><g transform="translate(482,148)"><path fill="#4285F4" fill-rule="evenodd" d="M9,0.75 C4.44375,0.75 0.75,4.44375 0.75,9 C0.75,13.55625 4.44375,17.25 9,17.25 C13.55625,17.25 17.25,13.55625 17.25,9 C17.25,4.44375 13.55625,0.75 9,0.75 L9,0.75 Z M9,14.625 C5.893125,14.625 3.375,12.106875 3.375,9 C3.375,5.893125 5.893125,3.375 9,3.375 C10.516875,3.375 11.7890625,3.9328125 12.763125,4.84125 L11.150625,6.45375 C10.5665625,5.8978125 9.8259375,5.6128125 8.9990625,5.6128125 C7.1634375,5.6128125 5.675625,7.1653125 5.675625,9.0009375 C5.675625,10.8365625 7.164375,12.388125 9,12.388125 C10.6659375,12.388125 11.80125,11.4328125 12.0309375,10.123125 L9,10.123125 L9,7.96875 L14.2978125,7.96875 C14.3634375,8.3390625 14.4009375,8.7253125 14.4009375,9.1275 C14.4009375,12.34125 12.2484375,14.625 9,14.625 L9,14.625 Z"/>
</g><g transform="translate(310,342)"><polygon fill="#0F9D58" fill-rule="evenodd" points="12 5 3.125 5 7.06 1.06 6 0 0 6 6 12 7.06 10.94 3.125 7 12 7" transform="translate(3 3)"/>
</g><g transform="translate(108,180)"><path fill="#000000" fill-rule="evenodd" d="M9,14 L18,14 L18,12 L9,12 L9,14 L9,14 Z M9,2 L9,4 L18,4 L18,2 L9,2 L9,2 Z M7.5,4 L4,0.5 L0.5,4 L3,4 L3,12 L0.5,12 L4,15.5 L7.5,12 L5,12 L5,4 L7.5,4 L7.5,4 Z M9,9 L18,9 L18,7 L9,7 L9,9 L9,9 Z" transform="matrix(-1 0 0 1 18 1)"/>
</g><g transform="translate(518,202)"><path fill="#000000" fill-rule="evenodd" d="M11.9925,14.0000001 L11.9925,16.0000001 L1.00445557,16.0000001 C1.00445557,16.0000001 0,16.0000001 0,15.0042115 L0,2.00689108 L2,2.00000019 L2.0075,2.00889226 L2.0075,12.9946834 C2.0075,13.7129334 2.16375,14.0000001 2.9925,14.0000001 L11.9925,14.0000001 Z M4,9.27183201e-08 C4,9.27183201e-08 3,9.27183201e-08 3,1.00158713 L3,12.0000002 C3,13.0000001 4.00701904,13.0000001 4,13.0000001 L12.9976196,13.0000001 C13.7592811,13.0000001 14,12.7112604 14,12.0000001 L14,1.00247339 C14,0.448822675 13.5527547,9.27183201e-08 13.0033264,9.27183201e-08 L10.3525798,9.27183201e-08 L4,9.27183201e-08 Z M5.525,2.00000009 C5.0415625,2.00000009 5.004375,2.05659625 5.004375,2.55384625 L5,10.4461539 C5,10.9434039 5.0371875,11.0000001 5.520625,11.0000001 L11.475,11.0000001 C11.9584375,11.0000001 12,10.9434039 12,10.4461539 C12,10.4461539 12,3.00771678 12,2.55384627 C12,2.09997577 11.4749999,2.00000009 11.4749999,2.00000009 L5.525,2.00000009 Z" transform="matrix(1 0 0 -1 2 17)"/>
</g><g transform="translate(202,396)"><path fill="#000000" fill-rule="evenodd" d="M0,4.5 L4,0 L12,0 L12,12 L0,12 L0,4.5 Z M2,5.5 L5,2 L12,2 L12,12 L2,12 L2,5.5 Z M4,4 L8,4 L8,8 L4,8 L4,4 Z M8,5 L12,5 L12,7 L8,7 L8,5 Z M7,8 L7,12 L5,12 L5,8 L7,8 Z" transform="translate(3 3)"/>
</g><g transform="translate(418,342)"><path fill="#000000" fill-rule="evenodd" d="M5,5 L9,5 L9,13 L5,13 L5,5 Z M10,13 L12.4,13 C13.28,13 14,12.3454545 14,11.5454545 L14,5 L10,5 L10,13 Z M12.5263158,0 L1.47368421,0 C0.663157895,0 0,0.72 0,1.6 L0,4 L14,4 L14,1.6 C14,0.72 13.3368421,0 12.5263158,0 Z M0,11.5454545 C0,12.3454545 0.72,13 1.6,13 L4,13 L4,5 L0,5 L0,11.5454545 Z" transform="translate(2 2)"/>
</g><g transform="translate(198,108)"><path fill="#000000" fill-rule="evenodd" d="M8,0.03 C3.58,0.03 0,3.61 0,8.03 C0,12.45 3.58,16.03 8,16.03 C12.42,16.03 16,12.45 16,8.03 C16,3.61 12.42,0.03 8,0.03 L8,0.03 Z M9,12 L7,12 L7,10 L9,10 L9,12 L9,12 Z M9,9 L7,9 L7,4 L9,4 L9,9 L9,9 Z" transform="translate(1 1)" opacity=".38"/>
</g><g transform="translate(328,342)"><path fill="#000000" fill-rule="evenodd" d="M10,7 L10,5 L9,5 L9,7 L7,7 L7,8 L9,8 L9,10 L10,10 L10,8 L12,8 L12,7 L10,7 Z M13,2 L8,2 L6,0 L1,0 C0.45,0 0,0.45 0,1 L0,11 C0,11.55 0.45,12 1,12 L13,12 C13.55,12 14,11.55 14,11 L14,3 C14,2.45 13.55,2 13,2 Z M13,11 L1,11 L1,3 L13,3 L13,11 Z" transform="translate(2 3)" opacity=".54"/>
</g><g transform="translate(464,18)"><g fill="#000000" fill-rule="evenodd" transform="translate(2 2)">
    <rect width="9" height="2" x="5" y="1"/>
    <rect width="9" height="2" x="5" y="6"/>
    <rect width="9" height="2" x="5" y="11"/>
    <circle cx="2" cy="2" r="1.5"/>
    <circle cx="2" cy="7" r="1.5"/>
    <circle cx="2" cy="12" r="1.5"/>
  </g>
</g><g transform="translate(572,252)"><path fill="#4285F4" fill-rule="evenodd" d="M15,0 L1,0 C0.45,0 0,0.45 0,1 L0,15 C0,15.55 0.45,16 1,16 L15,16 C15.55,16 16,15.55 16,15 L16,1 C16,0.45 15.55,0 15,0 L15,0 Z M9,12 L4,12 L4,10 L9,10 L9,12 L9,12 Z M12,9 L4,9 L4,7 L12,7 L12,9 L12,9 Z M12,6 L4,6 L4,4 L12,4 L12,6 L12,6 Z" transform="translate(1 1)"/>
</g><g transform="translate(234,306)"><path fill="#000000" fill-rule="evenodd" d="M0,14 L3,14 L3,5 L0,5 L0,14 L0,14 Z M14.7325,5 L10,5 L10.48,1.8225 L10.5025,1.5825 C10.5025,1.275 10.375,0.99 10.1725,0.7875 L9.3775,0 L4.4425,4.9425 C4.165,5.2125 4,5.5875 4,6 L4,13.5 C4,14.325 4.675,15 5.5,15 L12.25,15 C12.8725,15 13.405,14.625 13.63,14.085 L15.895,8.7975 C15.9625,8.625 16,8.445 16,8.25 C16,8.25 15.9277936,7.02252557 16,6.5 C16.0052936,6.46169224 16,6.5675 16,6.5675 L16,6.5 C16,5.675 15.5575,5 14.7325,5 Z" transform="translate(1 1)"/>
</g><g transform="translate(346,342)"><path fill="#000000" fill-rule="evenodd" d="M15,0 L1,0 C0.45,0 0,0.45 0,1 L0,15 C0,15.55 0.45,16 1,16 L15,16 C15.55,16 16,15.55 16,15 L16,1 C16,0.45 15.55,0 15,0 L15,0 Z M2.5,12 L5.25,8.46 L7.21,10.82 L9.96,7.28 L13.5,12 L2.5,12 L2.5,12 Z" transform="translate(1 1)"/>
</g><g transform="translate(144,342)"><path fill="#000000" fill-rule="evenodd" d="M0.459262274,1.72702923 L3.82798751,11.1061621 L5.24220107,9.69194854 L4.7372583,8.47989899 L8.27279221,4.94436508 L9.47487373,5.43933983 L10.8990553,4.03509429 L1.51992245,0.666369055 L0.459262274,1.72702923 Z M2.40380592,2.6109127 L6.56866486,4.13119228 L3.9240855,6.77577164 L2.40380592,2.6109127 Z M11.7573593,4 L13.1715729,5.41421356 L4.6862915,13.8994949 L6.10050506,15.3137085 L14.5857864,6.82842712 L16,8.24264069 L16,4 L11.7573593,4 Z" transform="translate(1 1)"/>
</g><g transform="translate(162,126)"><path fill="#000000" fill-rule="evenodd" d="M6,10 L14,10 L14,8 L6,8 L6,10 L6,10 Z M3.5,7 L0,3.5 L0,10.5 L3.5,7 L3.5,7 Z M0,14 L14,14 L14,12 L0,12 L0,14 L0,14 Z M0,0 L0,2 L14,2 L14,0 L0,0 L0,0 Z M6,6 L14,6 L14,4 L6,4 L6,6 L6,6 Z" transform="matrix(-1 0 0 1 16 2)"/>
</g><g transform="translate(274,126)"><polygon fill="#4285F4" fill-opacity=".78" fill-rule="evenodd" points="5 0 5 10 0 5" transform="translate(6 4)"/>
</g><g transform="translate(346,160)"><polygon fill="#FFFFFF" fill-rule="evenodd" points="8 0 6.59 1.41 12.17 7 0 7 0 9 12.17 9 6.59 14.59 8 16 16 8" transform="translate(4 4)"/>
</g><g transform="translate(54,360)"><path fill="#000000" fill-rule="evenodd" d="M12.7101213,4 L14.9975267,4 C15.5511774,4 16,4.45576096 16,5.00247329 L16,14.9975267 C16,15.5511774 15.544239,16 14.9975267,16 L5.00247329,16 C4.44882258,16 4,15.544239 4,14.9975267 L4,12.7101213 C4.63370941,12.8987225 5.30502739,13 6,13 L6,14 L14,14 L14,6 L13,6 C13,5.30502739 12.8987225,4.63370941 12.7101213,4 Z M6,12 C9.3137085,12 12,9.3137085 12,6 C12,2.6862915 9.3137085,0 6,0 C2.6862915,0 0,2.6862915 0,6 C0,9.3137085 2.6862915,12 6,12 Z M6,10 C8.209139,10 10,8.209139 10,6 C10,3.790861 8.209139,2 6,2 C3.790861,2 2,3.790861 2,6 C2,8.209139 3.790861,10 6,10 Z" transform="translate(1 1)"/>
</g><g transform="translate(90,234)"><path fill="#000000" fill-rule="evenodd" d="M4.94,5 L0,5 L0,13 L14,13 L14,5 L9.06,5 L10.53,6.47 L9.47,7.53 L7,5.06 L4.53,7.53 L3.47,6.47 L4.94,5 L2,5 L2,11 L12,11 L12,5 L9.06,5 L8.06,4 L10.53,1.53 L9.47,0.47 L7,2.94 L4.53,0.47 L3.47,1.53 L5.94,4 L4.94,5 Z" transform="translate(2 2)"/>
</g><g transform="translate(572,144)"><polygon fill="#000000" fill-rule="evenodd" points="0 2 1.5 .5 6 5 10.5 .5 12 2 6 8" transform="translate(3 5)"/>
</g><g transform="translate(500,238)"><path fill="#000000" fill-rule="evenodd" d="M6,5 L2,9 L3,10 L0,13 L4,13 L5,12 L5,12 L6,13 L10,9 L6,5 L6,5 Z M10.2937851,0.706214905 C10.6838168,0.316183183 11.3138733,0.313873291 11.7059121,0.705912054 L14.2940879,3.29408795 C14.6839524,3.68395241 14.6796852,4.32031476 14.2937851,4.7062149 L11,8 L7,4 L10.2937851,0.706214905 Z" transform="translate(2)"/>
</g><g transform="translate(572,90)"><polygon fill="#000000" fill-rule="evenodd" points="1.415 0 0 1.415 4.585 6 0 10.585 1.415 12 7.415 6" transform="matrix(-1 0 0 1 12.415 3)"/>
</g><g transform="translate(0,0)"><path fill="#000000" fill-rule="evenodd" d="M0,1.00684547 C0,0.450780073 0.444630861,0 1.00087166,0 L14.9991283,0 C15.5518945,0 16,0.449948758 16,1.00684547 L16,12.9931545 C16,13.5492199 15.5553691,14 14.9991283,14 L1.00087166,14 C0.448105505,14 0,13.5500512 0,12.9931545 L0,1.00684547 Z M2,2 L7,2 L7,6 L2,6 L2,2 Z M2,8 L7,8 L7,12 L2,12 L2,8 Z M9,2 L14,2 L14,6 L9,6 L9,2 Z M9,8 L14,8 L14,12 L9,12 L9,8 Z" transform="translate(1 2)"/>
</g><g transform="translate(428,72)"><defs><linearGradient id="a" x1="50.005%" x2="50.005%" y1="8.586%" y2="100.014%"><stop stop-color="#1A237E" stop-opacity=".2" offset="0%"/><stop stop-color="#1A237E" stop-opacity=".02" offset="100%"/></linearGradient><radialGradient id="b" cx="3.168%" cy="2.718%" r="161.248%" fx="3.168%" fy="2.718%" gradientTransform="matrix(1 0 0 .72222 0 .008)"><stop stop-color="#FFF" offset="0%"/><stop stop-color="#FFF" stop-opacity="0" offset="100%"/></radialGradient></defs><g fill="none" fill-rule="evenodd"><path fill="#4285F4" d="M9.5 2H24l9 9v24.5c0 1.3807119-1.1192881 2.5-2.5 2.5h-21C8.11928813 38 7 36.8807119 7 35.5v-31C7 3.11928813 8.11928813 2 9.5 2z"/><path fill="#1A237E" fill-opacity=".2" d="M7 35c0 1.3807119 1.11928813 2.5 2.5 2.5h21c1.3807119 0 2.5-1.1192881 2.5-2.5v.5c0 1.3807119-1.1192881 2.5-2.5 2.5h-21C8.11928813 38 7 36.8807119 7 35.5V35z"/><path fill="#FFF" fill-opacity=".2" d="M9.5 2H24v.5H9.5C8.11928813 2.5 7 3.61928813 7 5v-.5C7 3.11928813 8.11928813 2 9.5 2z"/><path fill="url(#a)" fill-rule="nonzero" d="M17.5 8l8.5 8.5V9" transform="translate(7 2)"/><path fill="#A1C2FA" d="M24 2l9 9h-6.5C25.1192881 11 24 9.88071187 24 8.5V2z"/><path fill="#F1F1F1" d="M13 18h14v2H13v-2zm0 4h14v2H13v-2zm0 4h14v2H13v-2zm0 4h10v2H13v-2z"/><path fill="url(#b)" fill-opacity=".1" d="M2.5 0H17l9 9v24.5c0 1.3807119-1.1192881 2.5-2.5 2.5h-21C1.11928813 36 0 34.8807119 0 33.5v-31C0 1.11928813 1.11928813 0 2.5 0z" transform="translate(7 2)"/></g></g><g transform="translate(90,144)"><path fill="#000000" fill-rule="evenodd" d="M18,0 L18,4 L17,4 L17,14 L18,14 L18,18 L14,18 L14,17 L4,17 L4,18 L0,18 L0,14 L1,14 L1,4 L0,4 L0,0 L4,0 L4,1 L14,1 L14,0 L18,0 Z M1,1 L1,3 L3,3 L3,1 L1,1 Z M15,1 L15,3 L17,3 L17,1 L15,1 Z M1,15 L1,17 L3,17 L3,15 L1,15 Z M15,15 L15,17 L17,17 L17,15 L15,15 Z M4,15 L14,15 L14,14 L15,14 L15,4 L14,4 L14,3 L4,3 L4,4 L3,4 L3,14 L4,14 L4,15 Z M5,5 L13,5 L13,7 L5,7 L5,5 Z M8,7 L10,7 L10,13 L8,13 L8,7 Z"/>
</g><g transform="translate(292,54)"><path fill="#000000" fill-rule="evenodd" d="M0.01,1 L0,15 C0,15.55 0.44,16 1,16 L11,16 C11.55,16 12,15.55 12,15 L12,5 L7,0 L1,0 C0.45,0 0.01,0.45 0.01,1 Z M6,10 L6,7 L5,7 L5,10 L2,10 L2,11 L5,11 L5,14 L6,14 L6,11 L9,11 L9,10 L6,10 Z M7,5 L7,1 L11,5 L7,5 Z" transform="translate(3 1)"/>
</g><g transform="translate(256,360)"><g fill="#000000" fill-rule="evenodd" transform="translate(2 2)">
    <path d="M0,2 L2,2 L2,0 L0,0 L0,2 L0,2 Z M3,2 L5,2 L5,0 L3,0 L3,2 L3,2 Z M3,8 L5,8 L5,6 L3,6 L3,8 L3,8 Z M3,14 L5,14 L5,12 L3,12 L3,14 L3,14 Z M0,5 L2,5 L2,3 L0,3 L0,5 L0,5 Z M0,8 L2,8 L2,6 L0,6 L0,8 L0,8 Z M0,14 L2,14 L2,12 L0,12 L0,14 L0,14 Z M0,11 L2,11 L2,9 L0,9 L0,11 L0,11 Z M9,8 L11,8 L11,6 L9,6 L9,8 L9,8 Z M6,14 L8,14 L8,12 L6,12 L6,14 L6,14 Z M9,14 L11,14 L11,12 L9,12 L9,14 L9,14 Z M6,2 L8,2 L8,0 L6,0 L6,2 L6,2 Z M9,2 L11,2 L11,0 L9,0 L9,2 L9,2 Z M6,11 L8,11 L8,9 L6,9 L6,11 L6,11 Z M6,5 L8,5 L8,3 L6,3 L6,5 L6,5 Z M6,8 L8,8 L8,6 L6,6 L6,8 L6,8 Z" opacity=".54"/>
    <polygon points="12 0 12 14 14 14 14 0"/>
  </g>
</g><g transform="translate(346,184)"><g fill="none" fill-rule="evenodd">
    <path fill="#000000" d="M14.5,8.87 C14.5,8.87 13,10.49 13,11.49 C13,12.32 13.67,12.99 14.5,12.99 C15.33,12.99 16,12.32 16,11.49 C16,10.5 14.5,8.87 14.5,8.87 L14.5,8.87 Z M12.71,6.79 L5.91,0 L4.85,1.06 L6.44,2.65 L2.29,6.79 C1.9,7.18 1.9,7.81 2.29,8.2 L6.79,12.7 C6.99,12.9 7.24,13 7.5,13 C7.76,13 8.01,12.9 8.21,12.71 L12.71,8.21 C13.1,7.82 13.1,7.18 12.71,6.79 L12.71,6.79 Z M4.21,7 L7.5,3.71 L10.79,7 L4.21,7 L4.21,7 Z"/>
  </g>
</g><g transform="translate(292,162)"><g fill="#000000" fill-rule="evenodd" transform="rotate(-180 8 8)">
    <rect width="9" height="2" x="5" y="1"/>
    <rect width="9" height="2" x="5" y="6"/>
    <rect width="9" height="2" x="5" y="11"/>
    <circle cx="2" cy="2" r="1.5"/>
    <circle cx="2" cy="7" r="1.5"/>
    <circle cx="2" cy="12" r="1.5"/>
  </g>
</g><g transform="translate(352,280)"><path fill="#000000" fill-rule="evenodd" d="M9,0 L1,0 C0.45,0 0,0.45 0,1 L0,4 C0,4.55 0.45,5 1,5 L9,5 C9.55,5 10,4.55 10,4 L10,3 L11,3 L11,6 L4,6 L4,14 L6,14 L6,8 L13,8 L13,2 L10,2 L10,1 C10,0.45 9.55,0 9,0 Z" transform="translate(3 2)"/>
</g><g transform="translate(216,342)"><path fill="#000000" fill-rule="evenodd" d="M2,0.5 C1.17,0.5 0.5,1.17 0.5,2 C0.5,2.83 1.17,3.5 2,3.5 C2.83,3.5 3.5,2.83 3.5,2 C3.5,1.17 2.83,0.5 2,0.5 L2,0.5 Z M12,0.5 C11.17,0.5 10.5,1.17 10.5,2 C10.5,2.83 11.17,3.5 12,3.5 C12.83,3.5 13.5,2.83 13.5,2 C13.5,1.17 12.83,0.5 12,0.5 L12,0.5 Z M7,0.5 C6.17,0.5 5.5,1.17 5.5,2 C5.5,2.83 6.17,3.5 7,3.5 C7.83,3.5 8.5,2.83 8.5,2 C8.5,1.17 7.83,0.5 7,0.5 L7,0.5 Z" transform="translate(2 7)"/>
</g><g transform="translate(464,112)"><path fill="#000000" fill-rule="evenodd" d="M1.88,7.25 C2.84649831,7.25 3.63,6.46649831 3.63,5.5 C3.63,4.53350169 2.84649831,3.75 1.88,3.75 C0.913501688,3.75 0.13,4.53350169 0.13,5.5 C0.13,6.46649831 0.913501688,7.25 1.88,7.25 Z M4.75,4.25 C5.71649831,4.25 6.5,3.46649831 6.5,2.5 C6.5,1.53350169 5.71649831,0.75 4.75,0.75 C3.78350169,0.75 3,1.53350169 3,2.5 C3,3.46649831 3.78350169,4.25 4.75,4.25 Z M9,4.25 C9.96649831,4.25 10.75,3.46649831 10.75,2.5 C10.75,1.53350169 9.96649831,0.75 9,0.75 C8.03350169,0.75 7.25,1.53350169 7.25,2.5 C7.25,3.46649831 8.03350169,4.25 9,4.25 Z M12.12,7.25 C13.0864983,7.25 13.87,6.46649831 13.87,5.5 C13.87,4.53350169 13.0864983,3.75 12.12,3.75 C11.1535017,3.75 10.37,4.53350169 10.37,5.5 C10.37,6.46649831 11.1535017,7.25 12.12,7.25 Z M8.89,6.3 C8.54,5.9 8.17,5.54 7.63,5.36 C7.39,5.28 6.8,5.25 6.8,5.25 C6.55,5.25 6.3,5.29 6.06,5.36 C5.52,5.54 5.19,5.9 4.84,6.3 C4.28,6.95 3.78,7.64 3.18,8.25 C2.26,9.19 1.15,10.13 1.43,11.54 C1.59,12.34 2.13,12.96 2.97,13.22 C3.44,13.36 5.03,12.92 6.81,12.92 C8.59,12.92 10.34,13.37 10.81,13.22 C11.65,12.96 12.18,12.34 12.34,11.54 C12.62,10.13 11.5,9.19 10.58,8.25 C9.97,7.64 9.45,6.95 8.89,6.3 Z" transform="translate(2 2)"/>
</g><g transform="translate(274,180)"><polygon fill="#000000" fill-rule="evenodd" points="0 0 0 11 3 8 6.5 14 8 13 4.5 7 9 7" transform="translate(5 2)"/>
</g><g transform="translate(406,280)"><path fill="#0F9D58" fill-rule="evenodd" d="M15,0 L1,0 C0.45,0 0,0.45 0,1 L0,15 C0,15.55 0.45,16 1,16 L15,16 C15.55,16 16,15.55 16,15 L16,1 C16,0.45 15.55,0 15,0 Z M15,7 L7,7 L7,14 L5,14 L5,7 L2,7 L2,5 L5,5 L5,2 L7,2 L7,5 L15,5 L15,7 Z" transform="translate(1 1)"/>
</g><g transform="translate(446,112)"><path fill="#000000" fill-rule="evenodd" d="M15,0 L1,0 C0.45,0 0,0.45 0,1 L0,15 C0,15.55 0.45,16 1,16 L15,16 C15.55,16 16,15.55 16,15 L16,1 C16,0.45 15.55,0 15,0 L15,0 Z M3,4 L5,4 L5,6 L3,6 L3,4 L3,4 Z M3,7 L5,7 L5,9 L3,9 L3,7 L3,7 Z M3,10 L5,10 L5,12 L3,12 L3,10 L3,10 Z M13,12 L6,12 L6,10 L13,10 L13,12 L13,12 Z M13,9 L6,9 L6,7 L13,7 L13,9 L13,9 Z M13,6 L6,6 L6,4 L13,4 L13,6 L13,6 Z" transform="translate(1 1)"/>
</g><g transform="translate(536,94)"><path fill="#000000" fill-rule="evenodd" d="M11.1984243,12.7314433 C10.1705378,13.2376004 9.01347324,13.51339 7.81008922,13.51339 C3.76079474,13.51339 0.486938477,10.4180495 0.486938477,6.60969321 C0.486938477,5.38242903 0.829564562,4.20636592 1.45265328,3.18078427 C1.16977004,2.84491791 1,2.41529191 1,1.94706351 C1,0.871730025 1.8954305,-1.42108547e-14 3,-1.42108547e-14 C4.1045695,-1.42108547e-14 5,0.871730025 5,1.94706351 C5,3.0129437 4.12024382,3.87878446 3.02908196,3.89392536 C2.52395643,4.68714001 2.23341168,5.61633656 2.23341168,6.60969321 C2.23341168,9.52166659 4.73017526,11.8822874 7.81008922,11.8822874 C8.61187491,11.8822874 9.37414032,11.7223074 10.0631246,11.4342674 C10.0219176,11.2785555 10,11.1153013 10,10.9470635 C10,9.87173003 10.8954305,9 12,9 C13.1045695,9 14,9.87173003 14,10.9470635 C14,12.022397 13.1045695,12.894127 12,12.894127 C11.7149481,12.894127 11.4438245,12.8360715 11.1984243,12.7314433 Z" transform="translate(2 2)"/>
</g><g transform="translate(108,144)"><path fill="#000000" fill-rule="evenodd" d="M0,0 L0,1 L6,7 L6,12 L8,11 L8,7 L14,1 L14,0 L0,0 Z M4,3 L10,3 L7,6 L4,3 Z" transform="translate(2 3)"/>
</g><g transform="translate(446,238)"><polygon fill="#000000" fill-rule="evenodd" points="4.75 8.127 1.623 5 .561 6.061 4.75 10.25 13.75 1.25 12.689 .189" transform="translate(2 4)"/>
</g></svg>