<?xml version="1.0" encoding="UTF-8"?>
<!--
   ====================================================================
   Licensed to the Apache Software Foundation (ASF) under one
   or more contributor license agreements.  See the NOTICE file
   distributed with this work for additional information
   regarding copyright ownership.  The ASF licenses this file
   to you under the Apache License, Version 2.0 (the
   "License"); you may not use this file except in compliance
   with the License.  You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing,
   software distributed under the License is distributed on an
   "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
   KIND, either express or implied.  See the License for the
   specific language governing permissions and limitations
   under the License.
   ====================================================================

   This software consists of voluntary contributions made by many
   individuals on behalf of the Apache Software Foundation.  For more
   information on the Apache Software Foundation, please see
   <http://www.apache.org />.
 --><project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.apache.httpcomponents</groupId>
    <artifactId>httpcomponents-client</artifactId>
    <version>4.5.8</version>
  </parent>
  <artifactId>httpclient</artifactId>
  <name>Apache HttpClient</name>
  <description>
   Apache HttpComponents Client
  </description>
  <url>http://hc.apache.org/httpcomponents-client</url>
  <packaging>jar</packaging>

  <dependencies>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpcore</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>commons-logging</groupId>
      <artifactId>commons-logging</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>commons-codec</groupId>
      <artifactId>commons-codec</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <filtering>true</filtering>
        <includes>
            <include>**/*.properties</include>
        </includes>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>add-source</id>
            <phase>generate-sources</phase>
            <goals>
              <goal>add-source</goal>
            </goals>
            <configuration>
              <sources>
                <source>src/main/java-deprecated</source>
              </sources>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>com.googlecode.maven-download-plugin</groupId>
        <artifactId>download-maven-plugin</artifactId>
        <version>1.2.1</version>
        <executions>
          <execution>
            <id>download-public-suffix-list</id>
            <phase>generate-sources</phase>
            <goals>
              <goal>wget</goal>
            </goals>
            <configuration>
              <url>https://publicsuffix.org/list/effective_tld_names.dat</url>
              <outputDirectory>${project.build.outputDirectory}/mozilla</outputDirectory>
              <outputFileName>public-suffix-list.txt</outputFileName>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <id>default-jar</id>
            <phase>package</phase>
            <goals>
              <goal>jar</goal>
              <goal>test-jar</goal>
            </goals>
            <configuration>
              <archive combine.children="append">
                <manifestEntries>
                  <Automatic-Module-Name>org.apache.httpcomponents.httpclient</Automatic-Module-Name>
                </manifestEntries>
              </archive>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <reporting>
    <plugins>

      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>${hc.javadoc.version}</version>
        <configuration>
          <!-- reduce console output. Can override with -Dquiet=false -->
          <quiet>true</quiet>
          <source>${maven.compiler.source}</source>
          <links>
            <link>http://docs.oracle.com/javase/6/docs/api/</link>
            <link>http://hc.apache.org/httpcomponents-core-ga/httpcore/apidocs/</link>
          </links>
        </configuration>
        <reportSets>
          <reportSet>
            <reports>
              <report>javadoc</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>

      <plugin>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <inherited>false</inherited>
        <reportSets>
          <reportSet>
            <reports>
              <report>dependencies</report>
              <report>dependency-info</report>
              <report>summary</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>

      <plugin>
        <artifactId>maven-jxr-plugin</artifactId>
      </plugin>

      <plugin>
        <artifactId>maven-surefire-report-plugin</artifactId>
      </plugin>

    </plugins>
  </reporting>

  <profiles>
    <profile>
      <id>release</id>
      <build>
        <plugins>
          <plugin>
            <groupId>com.googlecode.maven-download-plugin</groupId>
            <artifactId>download-maven-plugin</artifactId>
            <configuration>
              <overwrite>true</overwrite>
              <skipCache>true</skipCache>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

</project>
