<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~ Copyright 2005-2006 The Apache Software Foundation.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <!-- Shared parent. Doesn't define a lot of things about Apache like general mailing lists, but does
       define the settings common to all projects at Apache -->
  <groupId>org.apache</groupId>
  <artifactId>apache</artifactId>
  <version>3</version>
  <packaging>pom</packaging>
  <name>The Apache Software Foundation</name>
  <description>
    The Apache Software Foundation provides support for the Apache community of open-source software projects.
    The Apache projects are characterized by a collaborative, consensus based development process, an open and
    pragmatic software license, and a desire to create high quality software that leads the way in its field.
    We consider ourselves not simply a group of projects sharing a server, but rather a community of developers
    and users.
  </description>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <organization>
    <name>Apache Software Foundation</name>
    <url>http://www.apache.org/</url>
  </organization>
  <url>http://www.apache.org/</url>
  <repositories>
    <repository>
      <id>apache.snapshots</id>
      <name>Apache Snapshot Repository</name>
      <url>http://people.apache.org/repo/m2-snapshot-repository</url>
      <releases>
        <enabled>false</enabled>
      </releases>
    </repository>
  </repositories>
  <distributionManagement>
    <!-- Site omitted - each project must provide their own -->
    <repository>
      <id>apache.releases</id>
      <name>Apache Release Distribution Repository</name>
      <url>scp://people.apache.org/www/people.apache.org/repo/m2-ibiblio-rsync-repository</url>
    </repository>
    <snapshotRepository>
      <id>apache.snapshots</id>
      <name>Apache Development Snapshot Repository</name>
      <url>scp://people.apache.org/www/people.apache.org/repo/m2-snapshot-repository</url>
    </snapshotRepository>
  </distributionManagement>
  <mailingLists>
    <mailingList>
      <name>Apache Announce List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>http://mail-archives.apache.org/mod_mbox/www-announce/</archive>
    </mailingList>
  </mailingLists>
</project>

