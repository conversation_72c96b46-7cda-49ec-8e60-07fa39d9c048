<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>camunda-engine-rest-root</artifactId>
    <groupId>org.camunda.bpm</groupId>
    <version>7.17.0</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>camunda-engine-rest-jaxrs2</artifactId>
  <name>Camunda Platform - engine - REST JAX RS 2.0</name>
  <version>7.17.0</version>
  <build>
    <plugins>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <trimStackTrace>false</trimStackTrace>
          <redirectTestOutputToFile>true</redirectTestOutputToFile>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <promoteTransitiveDependencies>true</promoteTransitiveDependencies>
              <artifactSet>
                <includes>
                  <include>org.camunda.bpm:camunda-engine-rest-core</include>
                </includes>
              </artifactSet>
              <filters>
                <filter>
                  <artifact>org.camunda.bpm:camunda-engine-rest-core</artifact>
                  <excludes>
                    <exclude>org/camunda/bpm/engine/rest/impl/CamundaRestResources.class</exclude>
                  </excludes>
                </filter>
              </filters>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>distro</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <skipTests>true</skipTests>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>resteasy</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>build-helper-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>add-test-source</id>
                <phase>generate-test-sources</phase>
                <goals>
                  <goal>add-test-source</goal>
                </goals>
                <configuration>
                  <sources>
                    <source>src/test/java-resteasy3</source>
                  </sources>
                </configuration>
              </execution>
              <execution>
                <id>add-test-resource</id>
                <phase>generate-test-resources</phase>
                <goals>
                  <goal>add-test-resource</goal>
                </goals>
                <configuration>
                  <resources>
                    <resource>
                      <directory>src/test/resources-resteasy3</directory>
                    </resource>
                  </resources>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
      <dependencies>
        <dependency>
          <groupId>io.netty</groupId>
          <artifactId>netty-all</artifactId>
          <version>4.1.42.Final</version>
          <scope>test</scope>
        </dependency>
        <dependency>
          <groupId>org.jboss.resteasy</groupId>
          <artifactId>resteasy-netty4</artifactId>
          <version>${version.resteasy}</version>
          <scope>test</scope>
        </dependency>
        <dependency>
          <groupId>org.jboss.resteasy</groupId>
          <artifactId>resteasy-jaxrs</artifactId>
          <version>${version.resteasy}</version>
          <scope>test</scope>
          <exclusions>
            <exclusion>
              <artifactId>jboss-jaxrs-api_2.1_spec</artifactId>
              <groupId>org.jboss.spec.javax.ws.rs</groupId>
            </exclusion>
          </exclusions>
        </dependency>
        <dependency>
          <groupId>org.jboss.resteasy</groupId>
          <artifactId>resteasy-client</artifactId>
          <version>${version.resteasy}</version>
          <scope>test</scope>
        </dependency>
      </dependencies>
      <properties>
        <version.jaxrs>2.1</version.jaxrs>
      </properties>
    </profile>
    <profile>
      <id>jersey2</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>build-helper-maven-plugin</artifactId>
            <version>1.9.1</version>
            <executions>
              <execution>
                <id>attach-deps</id>
                <phase>package</phase>
                <goals>
                  <goal>attach-artifact</goal>
                </goals>
                <configuration>
                  <skipAttach>true</skipAttach>
                  <artifacts>
                    <artifact>
                      <file>/home/<USER>/workspace/7.17/7.17-DEPLOY-maven-central-camunda-bpm-platform/engine-rest/engine-rest-jaxrs2/target/dependencies.txt</file>
                      <type>txt</type>
                      <classifier>third-party-bom</classifier>
                    </artifact>
                  </artifacts>
                </configuration>
              </execution>
              <execution>
                <id>add-test-source</id>
                <phase>generate-test-sources</phase>
                <goals>
                  <goal>add-test-source</goal>
                </goals>
                <configuration>
                  <sources>
                    <source>src/test/java-jersey2</source>
                  </sources>
                </configuration>
              </execution>
              <execution>
                <id>add-test-resource</id>
                <phase>generate-test-resources</phase>
                <goals>
                  <goal>add-test-resource</goal>
                </goals>
                <configuration>
                  <resources>
                    <resource>
                      <directory>src/test/resources-jersey2</directory>
                    </resource>
                  </resources>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
      <dependencies>
        <dependency>
          <groupId>org.glassfish.jersey.containers</groupId>
          <artifactId>jersey-container-grizzly2-servlet</artifactId>
          <version>2.34</version>
          <scope>test</scope>
        </dependency>
        <dependency>
          <groupId>org.glassfish.jersey.inject</groupId>
          <artifactId>jersey-hk2</artifactId>
          <version>2.34</version>
          <scope>test</scope>
        </dependency>
      </dependencies>
    </profile>
  </profiles>
  <dependencies>
    <dependency>
      <groupId>commons-fileupload</groupId>
      <artifactId>commons-fileupload</artifactId>
      <version>1.4</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.8.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.jaxrs</groupId>
      <artifactId>jackson-jaxrs-json-provider</artifactId>
      <version>2.13.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.jaxrs</groupId>
      <artifactId>jackson-jaxrs-base</artifactId>
      <version>2.13.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.module</groupId>
      <artifactId>jackson-module-jaxb-annotations</artifactId>
      <version>2.13.1</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>jakarta.xml.bind-api</artifactId>
          <groupId>jakarta.xml.bind</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jakarta.activation-api</artifactId>
          <groupId>jakarta.activation</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>2.13.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
      <version>2.13.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
      <version>2.13.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.datatype</groupId>
      <artifactId>jackson-datatype-jsr310</artifactId>
      <version>2.13.1</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
  <properties>
    <version.jaxrs>2.0</version.jaxrs>
  </properties>
</project>
