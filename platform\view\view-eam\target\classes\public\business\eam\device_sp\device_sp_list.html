<!--
/**
 * 备件清单 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2023-08-08 12:13:41
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
    <title th:text="${lang.translate('备件清单')}">备件清单</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 备件类型 , type ,typeName=select_box, isHideInSearch=true -->
                    <!-- 备件状态 , status ,typeName=radio_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('备件状态')}" class="search-label status-label">备件状态</span><span class="search-colon">:</span></div>


                        <div id="status" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.DeviceSpStatusEnum')}" style="width:180px"></div>
                    </div>
                    <!-- 规格型号 , model ,typeName=text_input, isHideInSearch=true -->
                    <!-- 存放位置 , locId ,typeName=select_box, isHideInSearch=true -->
                    <!-- 图片 , pictureId ,typeName=upload, isHideInSearch=true -->
                    <!-- 数量 , spNumber ,typeName=number_input, isHideInSearch=true -->
                    <!-- 备注 , notes ,typeName=text_area, isHideInSearch=true -->
                    <!-- 是否锁定 , locked ,typeName=text_input, isHideInSearch=true -->
                    <!-- 保管人员 , managerUserId ,typeName=button, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('保管人员')}" class="search-label managerUserId-label">保管人员</span><span class="search-colon">:</span></div>
                            <input lay-filter="managerUserId" id="managerUserId" name="managerUserId"  type="hidden" class="layui-input"   />
                            <button id="managerUserId-button" type="button" action-type="emp-dialog" class="layui-btn layui-btn-primary   " style="width: 180px"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择人员')}" th:default-label="${lang.translate('请选择人员')}">按钮文本</span></button>
                    </div>
                    <!-- 使用场景 , usageRange ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('使用场景')}" class="search-label usageRange-label">使用场景</span><span class="search-colon">:</span></div>
                        <div id="usageRange" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_sp_usage_range'}" style="width:180px" extraParam="{}"></div>
                    </div>
                    <!-- 供应厂商 , supplier ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('供应厂商')}" class="search-label supplier-label">供应厂商</span><span class="search-colon">:</span></div>
                        <input id="supplier" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 备件编号 , code ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('备件编号')}" class="search-label code-label">备件编号</span><span class="search-colon">:</span></div>
                        <input id="code" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 备件名称 , name ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('备件名称')}" class="search-label name-label">备件名称</span><span class="search-colon">:</span></div>
                        <input id="name" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 备件序列 , sn ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('备件序列')}" class="search-label sn-label">备件序列</span><span class="search-colon">:</span></div>
                        <input id="sn" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 来源描述 , sourceDesc ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('来源描述')}" class="search-label sourceDesc-label">来源描述</span><span class="search-colon">:</span></div>
                        <input id="sourceDesc" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 所在仓库 , warehouseId ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('所在仓库')}" class="search-label warehouseId-label">所在仓库</span><span class="search-colon">:</span></div>
                        <div id="warehouseId" th:data="${'/service-eam/eam-warehouse/query-paged-list'}" style="width:180px" extraParam="{}"></div>
                    </div>
                    <!-- 适配信息 , adaptingDevice ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('适配信息')}" class="search-label adaptingDevice-label">适配信息</span><span class="search-colon">:</span></div>
                        <input id="adaptingDevice" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 入库时间 , insertTime ,typeName=date_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('入库时间')}" class="search-label insertTime-label">入库时间</span><span class="search-colon">:</span></div>
                            <input type="text" id="insertTime-begin" style="width: 180px" lay-verify="date" th:placeholder="${lang.translate('开始日期')}" autocomplete="off" class="layui-input search-input search-date-input"  readonly >
                            <span class="search-dash">-</span>
                            <input type="text" id="insertTime-end"  style="width: 180px"  lay-verify="date" th:placeholder="${lang.translate('结束日期')}" autocomplete="off" class="layui-input search-input search-date-input" readonly>
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 物品档案 , goodId ,typeName=button, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('物品档案')}" class="search-label goodId-label">物品档案</span><span class="search-colon">:</span></div>
                            <input lay-filter="goodId" id="goodId" name="goodId"  type="hidden" class="layui-input"    lay-verify="|required"   />
                            <button id="goodId-button" type="button" action-type="" class="layui-btn layui-btn-primary " style="width: 180px"><span th:text="${lang.translate('物品选择')}" th:default-label="${lang.translate('物品选择')}">按钮文本</span></button>
                    </div>
                    <!-- 物品档案编号 , goodsFillCode ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('物品档案编号')}" class="search-label goodsFillCode-label">物品档案编号</span><span class="search-colon">:</span></div>
                        <input id="goodsFillCode" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 物品档案型号 , goodsFillModel ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('物品档案型号')}" class="search-label goodsFillModel-label">物品档案型号</span><span class="search-colon">:</span></div>
                        <input id="goodsFillModel" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>


                </div>
            </div>


            <!-- 按钮区域 -->
            <div id="search-area" class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索','','cmp:table.search')}">搜索</span></button>
                <button id="search-button-advance" class="layui-btn layui-btn-primary icon-btn search-button-advance"><i class="layui-icon">&#xe671;</i><span th:text="${lang.translate('更多','','cmp:table.search')}">更多</span></button>
            </div>
        </div>

        <div id="table-area" style="margin-top: 84px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
        <button th:if="${perm.checkAuth('eam_device_sp:create')}" id="add-button" class="layui-btn icon-btn layui-btn-sm create-new-button " lay-event="create"><i class="layui-icon">&#xe654;</i><span th:text="${lang.translate('新建','','cmp:table.button')}">新建</span></button>
        <button id="bath-sure"  th:if="${perm.checkAuth('eam_device_sp:batchsure')}"class="layui-btn icon-btn layui-btn-sm  device-bath-sure-button " lay-event="tool-bath-sure"><span th:text="${lang.translate('批量确认','','cmp:table.button')}">批量确认</span></button>
    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">

    <button th:if="${perm.checkAuth('eam_device_sp:view-form')}" class="layui-btn layui-btn-primary layui-btn-xs ops-view-button " lay-event="view"  data-id="{{d.id}}"> <span th:text="${lang.translate('查看','','cmp:table.ops')}">查看</span></button>
    <button th:if="${perm.checkAnyAuth('eam_device_sp:update','eam_device_sp:save')}" class="layui-btn layui-btn-primary layui-btn-xs ops-edit-button " lay-event="edit"data-id="{{d.id}}"><span th:text="${lang.translate('修改','','cmp:table.ops')}">修改</span></button>


    <button th:if="${perm.checkAuth('eam_device_sp:delete')}" class="layui-btn layui-btn-xs layui-btn-danger ops-delete-button " lay-event="del" data-id="{{d.id}}"><span th:text="${lang.translate('删除','','cmp:table.ops')}">删除</span></button>

    <button th:if="${perm.checkAuth('eam_device_sp:status')}"class="layui-btn layui-btn-xs  device-status-button " lay-event="modify-status" data-id="{{d.id}}"><span th:text="${lang.translate('状态','','cmp:table.ops')}">状态</span></button>
    <button th:if="${perm.checkAuth('eam_device_sp:detail')}"class="layui-btn layui-btn-xs  device-detail-button " lay-event="used-detail" data-id="{{d.id}}"><span th:text="${lang.translate('使用记录','','cmp:table.ops')}">使用记录</span></button>

</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
    var RADIO_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.DeviceSpStatusEnum')}]];
    var AUTH_PREFIX="eam_device_sp";


</script>

<script th:src="'/business/eam/device_sp/device_sp_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/device_sp/device_sp_list.js?'+${cacheKey}"></script>

</body>
</html>