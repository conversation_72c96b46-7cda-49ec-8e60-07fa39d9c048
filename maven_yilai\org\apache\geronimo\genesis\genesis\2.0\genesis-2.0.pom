<?xml version="1.0" encoding="UTF-8"?>
<!--
    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at
    
     http://www.apache.org/licenses/LICENSE-2.0
    
    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.
-->

<!-- $Rev: 782833 $ $Date: 2009-06-08 17:12:50 -0700 (Mon, 08 Jun 2009) $ -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.apache</groupId>
        <artifactId>apache</artifactId>
        <version>6</version>
    </parent>

    <groupId>org.apache.geronimo.genesis</groupId>
    <artifactId>genesis</artifactId>
    <name>Genesis</name>
    <packaging>pom</packaging>

    <version>2.0</version>

    <description>
        Genesis provides build support for Apache Geronimo's Maven projects.
    </description>

    <!-- NOTE: until the  source-release assembly descriptor is released you need to build "clean install" before
    using the -Papache-release profile -->

    <url>http://geronimo.apache.org</url>

    <organization>
        <name>The Apache Software Foundation</name>
        <url>http://www.apache.org</url>
    </organization>

    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <inceptionYear>2003</inceptionYear>

    <scm>
        <connection>scm:svn:http://svn.apache.org/repos/asf/geronimo/genesis/tags/genesis-2.0</connection>
        <developerConnection>scm:svn:https://svn.apache.org/repos/asf/geronimo/genesis/tags/genesis-2.0</developerConnection>
        <url>http://svn.apache.org/viewvc/geronimo/geronimo/genesis/tags/genesis-2.0</url>
    </scm>

    <issueManagement>
        <system>jira</system>
        <url>https://issues.apache.org/jira/browse/GERONIMO</url>
    </issueManagement>

    <!--
    TODO: Nabble forum bits need to be upgraded.

          http://www.nabble.com/help/Answer.jtp?id=42&skin=134
    -->

    <mailingLists>
        <mailingList>
            <name>Geronimo User List</name>
            <subscribe><EMAIL></subscribe>
            <unsubscribe><EMAIL></unsubscribe>
            <post>mailto:<EMAIL></post>
            <archive>http://mail-archives.apache.org/mod_mbox/geronimo-user</archive>
            <otherArchives>
                <otherArchive>http://www.nabble.com/Apache-Geronimo---Users-f135.html</otherArchive>
            </otherArchives>
        </mailingList>

        <mailingList>
            <name>Geronimo Developer List</name>
            <subscribe><EMAIL></subscribe>
            <unsubscribe><EMAIL></unsubscribe>
            <post>mailto:<EMAIL></post>
            <archive>http://mail-archives.apache.org/mod_mbox/geronimo-dev</archive>
            <otherArchives>
                <otherArchive>http://www.nabble.com/Apache-Geronimo---Dev-f136.html</otherArchive>
            </otherArchives>
        </mailingList>

        <mailingList>
            <name>Source Control List</name>
            <subscribe><EMAIL></subscribe>
            <unsubscribe><EMAIL></unsubscribe>
            <post><EMAIL></post>
            <archive>http://mail-archives.apache.org/mod_mbox/geronimo-scm</archive>
        </mailingList>

    </mailingLists>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <siteId>genesis</siteId>
        <site.deploy.url>scp://people.apache.org:/www/geronimo.apache.org</site.deploy.url>
    </properties>

    <distributionManagement>
        <site>
            <id>apache-website</id>
            <url>${site.deploy.url}/maven/${siteId}/${version}</url>
        </site>
    </distributionManagement>

    <repositories>
        <repository>
            <id>codehaus.snapshots</id>
            <name>Codehaus Snapshots Repository</name>
            <url>http://snapshots.repository.codehaus.org</url>
            <layout>default</layout>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>daily</updatePolicy>
                <checksumPolicy>ignore</checksumPolicy>
            </snapshots>
            <releases>
                <enabled>false</enabled>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>

        <pluginRepository>
            <id>apache.snapshots</id>
            <name>Apache Snapshots Repository</name>
            <url>http://people.apache.org/repo/m2-snapshot-repository</url>
            <layout>default</layout>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>daily</updatePolicy>
                <checksumPolicy>ignore</checksumPolicy>
            </snapshots>
            <releases>
                <enabled>false</enabled>
            </releases>
        </pluginRepository>

        <pluginRepository>
            <id>codehaus.snapshots</id>
            <name>Codehaus Snapshots Repository</name>
            <url>http://snapshots.repository.codehaus.org</url>
            <layout>default</layout>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>daily</updatePolicy>
                <checksumPolicy>ignore</checksumPolicy>
            </snapshots>
            <releases>
                <enabled>false</enabled>
            </releases>
        </pluginRepository>
    </pluginRepositories>

    <modules>
        <module>apache-source-release-assembly-descriptor</module>
        <module>genesis-maven-plugin</module>
        <module>genesis-packaging</module>
        <module>genesis-default-flava</module>
        <!--<module>genesis-skin</module>-->
    </modules>

    <build>
        <defaultGoal>install</defaultGoal>

        <pluginManagement>
            <plugins>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-archetype-plugin</artifactId>
                    <version>2.0-alpha-4</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>2.2-beta-4</version>
                    <configuration>
                        <tarLongFileMode>gnu</tarLongFileMode>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-changes-plugin</artifactId>
                    <version>2.1</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-changelog-plugin</artifactId>
                    <version>2.1</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-checkstyle-plugin</artifactId>
                    <version>2.2</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>2.1</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-ear-plugin</artifactId>
                    <version>2.3</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-ejb-plugin</artifactId>
                    <version>2.1</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-help-plugin</artifactId>
                    <version>2.1</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jxr-plugin</artifactId>
                    <version>2.1</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-pmd-plugin</artifactId>
                    <version>2.4</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-rar-plugin</artifactId>
                    <version>2.2</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-reactor-plugin</artifactId>
                    <version>1.0</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-release-plugin</artifactId>
                    <configuration>
                        <autoVersionSubmodules>true</autoVersionSubmodules>
                        <allowTimestampedSnapshots>false</allowTimestampedSnapshots>
                        <preparationGoals>clean install</preparationGoals>
                        <goals>deploy</goals>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>1.2</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-report-plugin</artifactId>
                    <version>2.4.3</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-verifier-plugin</artifactId>
                    <version>1.0-beta-1</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-war-plugin</artifactId>
                    <version>2.1-alpha-2</version>
                </plugin>

                <plugin>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <version>2.0.0</version>
                    <extensions>true</extensions>
                </plugin>

                <plugin>
                    <groupId>org.codehaus.groovy.maven</groupId>
                    <artifactId>gmaven-plugin</artifactId>
                    <version>1.0-rc-3</version>
                </plugin>

                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>1.1</version>
                </plugin>

                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>cobertura-maven-plugin</artifactId>
                    <version>2.2</version>
                </plugin>

                <plugin>
                    <groupId>org.codehaus.mojo.jspc</groupId>
                    <artifactId>jspc-maven-plugin</artifactId>
                    <version>2.0-alpha-2</version>
                </plugin>

                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>ianal-maven-plugin</artifactId>
                    <version>1.0-alpha-1</version>
                </plugin>

                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>jaxb2-maven-plugin</artifactId>
                    <version>1.2</version>
                </plugin>

                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>rat-maven-plugin</artifactId>
                    <version>1.0-alpha-3</version>
                </plugin>

                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>selenium-maven-plugin</artifactId>
                    <version>1.0-beta-3</version>
                </plugin>

                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>shitty-maven-plugin</artifactId>
                    <version>1.0-alpha-3</version>
                </plugin>

                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>sql-maven-plugin</artifactId>
                    <version>1.1</version>
                </plugin>

                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>xmlbeans-maven-plugin</artifactId>
                    <version>2.3.1</version>
                </plugin>

                <plugin>
                    <groupId>com.google.code.maven-license-plugin</groupId>
                    <artifactId>maven-license-plugin</artifactId>
                    <version>1.3.1</version>
                </plugin>

                <!-- INTERNAL -->

                <plugin>
                    <groupId>org.apache.geronimo.genesis</groupId>
                    <artifactId>genesis-maven-plugin</artifactId>
                    <version>2.0</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <profiles>
        <profile>
            <id>apache-release</id>
            <activation>
                <property>
                    <name>apache-release</name>
                </property>
            </activation>
            <build>
                <plugins>
                    <!-- waiting for release -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>single</goal>
                                </goals>
                                <phase>package</phase>
                                <configuration>
                                    <runOnlyAtExecutionRoot>true</runOnlyAtExecutionRoot>
                                    <descriptorRefs>
                                        <descriptorRef>
                                            source-release
                                        </descriptorRef>
                                    </descriptorRefs>
                                </configuration>
                            </execution>
                        </executions>
                        <dependencies>
                            <dependency>
                                <!-- apache version not yet released -->
                                <!--<groupId>org.apache</groupId>-->
                                <groupId>org.apache.geronimo.genesis</groupId>
                                <artifactId>apache-source-release-assembly-descriptor</artifactId>
                                <!-- apache version not yet known -->
                                <version>2.0</version>
                            </dependency>
                        </dependencies>
                    </plugin>

                </plugins>
            </build>
        </profile>

        <profile>
            <id>strict-enforcement</id>
            <activation>
                <property>
                    <name>enforce</name>
                    <value>strict</value>
                </property>
            </activation>

            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-enforcer-plugin</artifactId>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>enforce</goal>
                                </goals>
                                <configuration>
                                    <rules>
                                        <requirePluginVersions>
                                            <banLatest>true</banLatest>
                                            <banRelease>true</banRelease>
                                            <banSnapshots>true</banSnapshots>
                                            <banTimestamps>true</banTimestamps>
                                        </requirePluginVersions>
                                        <requireReleaseDeps>
                                            <searchTransitive>true</searchTransitive>
                                        </requireReleaseDeps>
                                    </rules>
                                    <fail>true</fail>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>

