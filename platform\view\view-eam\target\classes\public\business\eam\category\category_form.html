<!-- 
/**
 * 资产分类 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2021-07-31 06:43:11
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
	<title th:text="${lang.translate('资产分类属性')}">资产分类属性</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/assets/css/admin.css"/>
    <link rel="stylesheet" href="/assets/css/foxnic-web.css">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" th:href="@{/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css}" rel="stylesheet"/>
</head>

<body style="overflow-y: hidden">

<form id="data-form" lay-filter="data-form" class="layui-form model-form"  >


    <div class="layui-form-item" >
        <label class="layui-form-label" th:text="${lang.translate('主键')}">主键</label>
        <div class="layui-input-block">
            <input readonly="readonly" lay-filter="id" id="id" name="id" th:placeholder="${  lang.translate('系统自动填充') }" type="text" class="layui-input"   />
        </div>
    </div>


    <div class="layui-form-item" >
        <label class="layui-form-label" th:text="${lang.translate('分类路径')}">分类路径</label>
        <div class="layui-input-block">
            <input readonly="readonly" lay-filter="hierarchyName" id="hierarchyName" name="hierarchyName" th:placeholder="${lang.translate('系统自动填充') }" type="text" class="layui-input"   />
        </div>
    </div>


    <div class="layui-form-item" >
        <label class="layui-form-label" th:text="${lang.translate('编码')}">编码</label>
        <div class="layui-input-block">
            <input lay-filter="categoryCode" id="categoryCode" name="categoryCode" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('编码') }" type="text" class="layui-input"   />
        </div>
    </div>



    <div class="layui-form-item" >
        <label class="layui-form-label" th:text="${lang.translate('名称')}">名称</label>
        <div class="layui-input-block">
            <input lay-filter="categoryName" id="categoryName" name="categoryName" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('名称') }" type="text" class="layui-input"   />
        </div>
    </div>

    <div class="layui-form-item" >
        <label class="layui-form-label" th:text="${lang.translate('使用期限')}">使用期限</label>
        <div class="layui-input-block">
            <input lay-filter="serviceLife" id="serviceLife" name="serviceLife" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('使用期限(月)') }" type="number" class="layui-input"   />
        </div>
    </div>



    <div class="layui-form-item" >
        <label class="layui-form-label" th:text="${lang.translate('排序')}">排序</label>
        <div class="layui-input-block">
            <input lay-filter="sort" id="sort" name="sort" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('排序') }" type="number" class="layui-input"   />
        </div>
    </div>

    
    <div class="layui-form-item" >
        <label class="layui-form-label" th:text="${lang.translate('备注')}">备注</label>
        <div class="layui-input-block">
            <input lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('备注') }" type="text" class="layui-input"   />
        </div>
    </div>


    <div class="layui-form-item model-form-footer">
        <button th:if="${perm.checkAnyAuth('eam_category:create','eam_category:update','eam_category:save')}" class="layui-btn"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存')}">保存</button>
    </div>
</form>

<script type="text/javascript" src="/module/global.js"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js"></script>

<script src="/business/eam/category/category_form.js"></script>

</body>
</html>