<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.codehaus.plexus</groupId>
    <artifactId>plexus</artifactId>
    <version>6.1</version>
  </parent>

  <artifactId>plexus-archiver</artifactId>
  <version>4.2.2</version>
  <name>Plexus Archiver Component</name>

  <scm>
    <connection>scm:git:**************:codehaus-plexus/plexus-archiver.git</connection>
    <developerConnection>scm:git:**************:codehaus-plexus/plexus-archiver.git</developerConnection>
    <url>http://github.com/codehaus-plexus/plexus-archiver/tree/${project.scm.tag}/</url>
    <tag>plexus-archiver-4.2.2</tag>
  </scm>
  <issueManagement>
    <system>jira</system>
    <url>https://github.com/codehaus-plexus/plexus-archiver/issues</url>
  </issueManagement>
  <distributionManagement>
    <site>
      <id>github:gh-pages</id>
      <url>${project.scm.developerConnection}</url>
    </site>
  </distributionManagement>

  <properties>
    <javaVersion>7</javaVersion>
    <project.build.outputTimestamp>2020-02-29T18:20:48Z</project.build.outputTimestamp>
  </properties>

  <contributors>
    <contributor>
      <name>Dan Tran</name>
    </contributor>
    <contributor>
      <name>Richard van der Hoff</name>
    </contributor>
    <contributor>
      <name>Tomasz 'Trog' Welman</name>
      <email><EMAIL></email>
    </contributor>
  </contributors>

  <dependencies>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-container-default</artifactId>
      <version>1.0-alpha-30</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-utils</artifactId>
      <version>3.3.0</version>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-io</artifactId>
      <version>3.2.0</version>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-compress</artifactId>
      <version>1.20</version>
    </dependency>
    <dependency>
      <groupId>org.iq80.snappy</groupId>
      <artifactId>snappy</artifactId>
      <version>0.4</version>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.12</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>jsr305</artifactId>
      <version>3.0.2</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.tukaani</groupId>
      <artifactId>xz</artifactId>
      <version>1.8</version>
      <scope>runtime</scope>
    </dependency>
  </dependencies>


  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-release-plugin</artifactId>
        <configuration>
          <!-- olamy: exclude files with strange names as failed here on osx -->
          <checkModificationExcludes>
            <checkModificationExclude>**/src/test/resources/utf8/**</checkModificationExclude>
          </checkModificationExcludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-component-metadata</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>generate-metadata</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-scm-publish-plugin</artifactId>
        <configuration>
          <content>${project.reporting.outputDirectory}</content><!-- mono-module doesn't require site:stage -->
        </configuration>
        <executions>
          <execution>
            <id>scm-publish</id>
            <phase>site-deploy</phase><!-- deploy site with maven-scm-publish-plugin -->
            <goals>
              <goal>publish-scm</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

</project>
