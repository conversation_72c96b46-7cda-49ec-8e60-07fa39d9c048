<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 2012, 2019 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Public License v. 2.0, which is available at
    http://www.eclipse.org/legal/epl-2.0.

    This Source Code may also be made available under the following Secondary
    Licenses when the conditions for such availability set forth in the
    Eclipse Public License v. 2.0 are satisfied: GNU General Public License,
    version 2 with the GNU Classpath Exception, which is available at
    https://www.gnu.org/software/classpath/license.html.

    SPDX-License-Identifier: EPL-2.0 OR GPL-2.0 WITH Classpath-exception-2.0

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.glassfish.jersey.media</groupId>
        <artifactId>project</artifactId>
        <version>2.30.1</version>
    </parent>

    <artifactId>jersey-media-json-jackson</artifactId>
    <packaging>jar</packaging>
    <name>jersey-media-json-jackson</name>

    <description>
        Jersey JSON Jackson (2.x) entity providers support module.
    </description>

    <licenses>
        <license>
            <name>EPL 2.0</name>
            <url>http://www.eclipse.org/legal/epl-2.0</url>
            <distribution>repo</distribution>
            <comments>Except for Guava, JSR-166 files, Dropwizard Monitoring inspired classes, ASM and Jackson JAX-RS Providers.
                See also https://github.com/eclipse-ee4j/jersey/blob/master/NOTICE.md</comments>
        </license>
        <license>
            <name>The GNU General Public License (GPL), Version 2, With Classpath Exception</name>
            <url>https://www.gnu.org/software/classpath/license.html</url>
            <distribution>repo</distribution>
            <comments>Except for Jackson JAX-RS Providers.
                See also https://github.com/jersey/jersey/blob/master/NOTICE.md</comments>
        </license>
        <license>
            <name>Apache License, 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.html</url>
            <distribution>repo</distribution>
            <comments>Jackson JAX-RS Providers @ org.glassfish.jersey.jackson.internal.jackson.jaxrs</comments>
        </license>
    </licenses>

    <build>
        <plugins>
            <plugin>
                <groupId>com.sun.istack</groupId>
                <artifactId>istack-commons-maven-plugin</artifactId>
                <inherited>true</inherited>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <inherited>true</inherited>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <inherited>true</inherited>
                <extensions>true</extensions>
                <configuration>
                    <instructions>
                        <Export-Package>org.glassfish.jersey.jackson.*</Export-Package>
                        <Import-Package>${javax.annotation.osgi.version},*</Import-Package>
                    </instructions>
                    <unpackBundle>true</unpackBundle>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>org.glassfish.jersey.core</groupId>
            <artifactId>jersey-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jersey.ext</groupId>
            <artifactId>jersey-entity-filtering</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!--<dependency>-->
            <!--<groupId>com.fasterxml.jackson.jaxrs</groupId>-->
            <!--<artifactId>jackson-jaxrs-base</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>com.fasterxml.jackson.jaxrs</groupId>-->
            <!--<artifactId>jackson-jaxrs-json-provider</artifactId>-->
        <!--</dependency>-->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-jaxb-annotations</artifactId>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
