<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>camunda-engine-dmn-root</artifactId>
    <groupId>org.camunda.bpm.dmn</groupId>
    <version>7.17.0</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>camunda-engine-feel-juel</artifactId>
  <name>camunda DMN - engine FEEL - JUEL</name>
  <build>
    <plugins>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <createDependencyReducedPom>true</createDependencyReducedPom>
              <createSourcesJar>true</createSourcesJar>
              <promoteTransitiveDependencies>true</promoteTransitiveDependencies>
              <artifactSet>
                <excludes>
                  <exclude>org.camunda.commons:*</exclude>
                  <exclude>org.camunda.bpm.dmn:camunda-engine-feel-api</exclude>
                  <exclude>org.slf4j:*</exclude>
                </excludes>
              </artifactSet>
              <relocations>
                <relocation>
                  <pattern>de.odysseus.el</pattern>
                  <shadedPattern>camundafeel.de.odysseus.el</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>javax.el</pattern>
                  <shadedPattern>camundafeel.javax.el</shadedPattern>
                </relocation>
              </relocations>
              <transformers>
                <transformer>
                  <resources>
                    <resource>META-INF/services/javax.el.ExpressionFactory</resource>
                  </resources>
                </transformer>
              </transformers>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <executions>
          <execution>
            <id>bundle-manifest</id>
            <phase>process-classes</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <unpackBundle>false</unpackBundle>
          <instructions>
            <Import-Package>!de.odysseus.el*,
              !javax.el,
              *</Import-Package>
            <Bundle-ClassPath>.</Bundle-ClassPath>
            <Embed-Dependency>de.odysseus.el*;inline=true,
              javax.el*;inline=true</Embed-Dependency>
          </instructions>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>check-api-compatibility</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>clirr-maven-plugin</artifactId>
            <version>2.8</version>
            <executions>
              <execution>
                <id>all</id>
                <phase>verify</phase>
                <goals>
                  <goal>check-no-fork</goal>
                </goals>
                <configuration>
                  <textOutputFile>/home/<USER>/workspace/7.17/7.17-DEPLOY-maven-central-camunda-bpm-platform/engine-dmn/feel-juel/target/clirr-all.txt</textOutputFile>
                  <failOnWarning>false</failOnWarning>
                  <failOnError>false</failOnError>
                  <comparisonVersion>7.16.0</comparisonVersion>
                  <logResults>true</logResults>
                  <excludes>
                    <exclude>org/camunda/bpm/dmn/feel/impl/**</exclude>
                    <exclude>camundafeel/**</exclude>
                  </excludes>
                </configuration>
              </execution>
              <execution>
                <id>restrictive</id>
                <phase>verify</phase>
                <goals>
                  <goal>check-no-fork</goal>
                </goals>
                <configuration>
                  <textOutputFile>/home/<USER>/workspace/7.17/7.17-DEPLOY-maven-central-camunda-bpm-platform/engine-dmn/feel-juel/target/clirr-restrictive.txt</textOutputFile>
                  <failOnWarning>true</failOnWarning>
                  <ignoredDifferencesFile>../.clirr-jenkins-ignore.xml</ignoredDifferencesFile>
                  <comparisonVersion>7.16.0</comparisonVersion>
                  <logResults>true</logResults>
                  <excludes>
                    <exclude>org/camunda/bpm/dmn/feel/impl/**</exclude>
                    <exclude>camundafeel/**</exclude>
                  </excludes>
                </configuration>
              </execution>
            </executions>
            <configuration>
              <comparisonVersion>7.16.0</comparisonVersion>
              <logResults>true</logResults>
              <excludes>
                <exclude>org/camunda/bpm/dmn/feel/impl/**</exclude>
                <exclude>camundafeel/**</exclude>
              </excludes>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
  <dependencies>
    <dependency>
      <groupId>org.camunda.bpm.dmn</groupId>
      <artifactId>camunda-engine-feel-api</artifactId>
      <version>7.17.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.camunda.commons</groupId>
      <artifactId>camunda-commons-logging</artifactId>
      <version>1.10.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.26</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.camunda.commons</groupId>
      <artifactId>camunda-commons-typed-values</artifactId>
      <version>7.17.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.camunda.commons</groupId>
      <artifactId>camunda-commons-utils</artifactId>
      <version>1.10.0</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
  <properties>
    <camunda.artifact>org.camunda.bpm.dmn.feel.impl.juel</camunda.artifact>
    <skip-third-party-bom>false</skip-third-party-bom>
  </properties>
</project>
