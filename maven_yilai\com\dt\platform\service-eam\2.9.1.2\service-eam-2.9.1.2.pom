<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dt.platform</groupId>
        <artifactId>parent-eam</artifactId>
        <version>*******</version>
        <relativePath>../../pom.xml</relativePath>
        <!-- lookup parent from repository -->
    </parent>
    <artifactId>service-eam</artifactId>
    <name>service-eam</name>
    <description>Service EAM</description>
    <properties>
        <skipTests>true</skipTests>
        <java.version>1.8</java.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    </properties>
    <dependencies>



        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-jexl3</artifactId>
            <version>3.2.1</version>
        </dependency>

        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
            <version>2.3.2</version>
        </dependency>

        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <version>1.33</version>
        </dependency>


        <!-- itext7 全家桶 -->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext7-core</artifactId>
            <version>7.2.4</version>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>framework-eam</artifactId>
            <version>${platform.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>3.3.0</version>
        </dependency>
        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>domain-eam</artifactId>
            <version>${platform.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>proxy-eam</artifactId>
            <version>${platform.version}</version>
        </dependency>

        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>service-common</artifactId>
            <version>${foxnic-web-plus.version}</version>
               <exclusions>
                   <exclusion>
                       <groupId>org.apache.poi</groupId>
                       <artifactId>ooxml-schemas</artifactId>
                   </exclusion>
               </exclusions>
        </dependency>

        <!-- -引入模板引擎 https://mvnrepository.com/artifact/com.deepoove/poi-tl -->
        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
            <version>1.9.1</version>
        </dependency>


        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
            <version>4.1.2</version>
        </dependency>

        <dependency>
            <groupId>org.apache.xmlbeans</groupId>
            <artifactId>xmlbeans</artifactId>
            <version>3.0.2</version>
        </dependency>


        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-base</artifactId>
            <version>4.4.0</version>
        </dependency>
        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>proxy-web</artifactId>
            <version>${foxnic-web-plus.version}</version>

        </dependency>

        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>domain-ops</artifactId>
            <version>${foxnic-ops.version}</version>

        </dependency>

    </dependencies>
    <build>
        <defaultGoal>compile</defaultGoal>
        <resources>
            <resource>
                <directory>${basedir}/src/main/java</directory>
                <includes>
                    <include>**/*.tql</include>
                </includes>
            </resource>
            <resource>
                <directory>${basedir}/src/main/resources</directory>
                <includes>
                    <include>**/**</include>
                </includes>
            </resource>
        </resources>
    </build>
</project>
