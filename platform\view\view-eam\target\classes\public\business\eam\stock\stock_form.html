<!--
/**
 * 资产库存 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2021-11-22 13:52:32
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
	<title th:text="${lang.translate('资产库存')}">资产库存</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" th:href="@{/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css}" rel="stylesheet"/>
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon"> <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
    <style>
    </style>
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="display:none">

        <input name="id" id="id"  type="hidden"/>

        <!--开始：group 循环-->



        <div class="layui-row form-row" id="random-4423-content">

            <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('单据名称')}">单据名称</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="stockName" id="stockName" name="stockName" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('单据名称') }" type="text" class="layui-input"    lay-verify="|required"  />
                    </div>
                </div>

                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('批次号')}">批次号</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="stockBatchCode" id="stockBatchCode" name="stockBatchCode" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('批次号') }" type="text" class="layui-input"  />
                    </div>
                </div>

                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('接收人')}">接收人</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="receiverUserName" id="receiverUserName" name="receiverUserName" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('接收人') }" type="text" class="layui-input"  />
                    </div>
                </div>

                <!--结束：栏次内字段循环-->
            </div>
            <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >


                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('来源')}">来源</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="sourceId" input-type="select" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_source'}" extraParam="{}"></div>
                    </div>
                </div>



                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('供应商')}">供应商</div></div>
                    <div class="layui-input-block ">
                        <div id="supplierId" input-type="select" th:data="${'/service-eam/eam-supplier/query-list'}" extraParam="{}"></div>
                    </div>
                </div>



                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('仓库')}">仓库</div></div>
                    <div class="layui-input-block ">
                        <div id="warehouseId" input-type="select" th:data="${'/service-eam/eam-warehouse/query-list'}" extraParam="{}"></div>
                    </div>
                </div>


                <!--结束：栏次内字段循环-->
            </div>
            <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('所属公司')}">所属公司</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="ownCompanyId" id="ownCompanyId" name="ownCompanyId"  type="hidden" class="layui-input"   />
                        <button id="ownCompanyId-button" type="button" action-type="org-dialog" class="layui-btn   " style="width: 100%"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择公司')}" th:default-label="${lang.translate('请选择公司')}">按钮文本</span></button>
                    </div>
                </div>

                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('管理人')}">管理人</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="managerId" id="managerId" name="managerId"  type="hidden" class="layui-input"   />
                        <button id="managerId-button" type="button" action-type="emp-dialog" class="layui-btn   " style="width: 100%"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择人员')}" th:default-label="${lang.translate('请选择人员')}">按钮文本</span></button>
                    </div>
                </div>


                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('总金额')}">总金额</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="amount" id="amount" name="amount" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('总金额') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="false" step="1.0"   scale="2" />
                    </div>
                </div>


                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--开始：group 循环-->



        <div class="layui-row form-row" id="random-7397-content">

            <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column"  style="padding-top: 0px" >

                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('备注')}">备注</div></div>
                    <div class="layui-input-block ">

                        <textarea lay-filter="stockNotes" id="stockNotes" name="stockNotes" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('备注') }" class="layui-textarea" style="height: 120px" ></textarea>


                    </div>
                </div>


                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('附件')}">附件</div></div>
                    <div class="layui-upload layui-input-block ">
                        <input input-type="upload" id="attachId"  name="attachId" lay-filter="attachId" style="display: none">
                        <button type="button" class="layui-btn" id="attachId-button" th:text="${lang.translate('选择附件')}">选择附件</button>
                        <div class="layui-upload-list" id="attachId-file-list"></div>
                    </div>
                </div>



                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--开始：group 循环-->

        <fieldset class="layui-elem-field layui-field-title form-group-title" id="random-0148-fieldset">
           <legend th:text="${lang.translate('资产列表')}" >资产列表</legend>
        </fieldset>
        <div class="layui-row form-row" style="text-align: center;" id="random-0148-content">
            <div style="display: inline-block;padding-right: 8px;padding-left: 8px" class="layui-col-xs12">
                <iframe js-fn="assetSelectList" class="form-iframe" frameborder="0" style="width: 100%"></iframe>
            </div>
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 100px"></div>


    </form>

</div>


<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消')}" >取消</button>
    <button class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>

<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetHandleStatusEnum')}]];
    var VALIDATE_CONFIG={"sourceId":{"labelInForm":"来源","inputType":"select_box","required":true},"businessDate":{"date":true,"labelInForm":"业务时间","inputType":"date_input"},"stockName":{"labelInForm":"单据名称","inputType":"text_input","required":true}};
    var AUTH_PREFIX= [[${authPrefix}]];

    // 单据ID
    var BILL_ID = [[${billId}]] ;
    // 单据类型
    var BILL_TYPE = [[${billType}]] ;

    var STOCK_TYPE = [[${stockType}]] ;

    var OWNER_CODE = [[${ownerCode}]] ;


    var ASSET_DEFAULT_OWN_COMPANY =  [[${assetDefaultOwnCompany}]];

</script>



<script th:src="'/business/eam/stock/stock_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/stock/stock_form.js?'+${cacheKey}"></script>

</body>
</html>
