<!--
/**
 * 财务分类 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2025-05-12 12:35:19
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
	<title th:text="${lang.translate('财务分类')}">财务分类</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="opacity:0">


         <!--开始：group 循环-->


        <div class="layui-row form-row" id="default-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column" >

                <!-- text_input : 主键 ,  id -->
                <div class="layui-form-item" style="display: none"inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('主键')}">主键</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="id" id="id" name="id" th:placeholder="${ lang.translate('请输入'+'主键') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- text_input : 状态 ,  status -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('状态')}">状态</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="status" id="status" name="status" th:placeholder="${ lang.translate('请输入'+'状态') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- text_input : 名称 ,  categoryName -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('名称')}">名称</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="categoryName" id="categoryName" name="categoryName" th:placeholder="${ lang.translate('请输入'+'名称') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- text_input : 全称 ,  categoryFullname -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('全称')}">全称</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="categoryFullname" id="categoryFullname" name="categoryFullname" th:placeholder="${ lang.translate('请输入'+'全称') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- text_input : 编码 ,  categoryCode -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('编码')}">编码</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="categoryCode" id="categoryCode" name="categoryCode" th:placeholder="${ lang.translate('请输入'+'编码') }" type="text" class="layui-input"    lay-verify="|required"  />
                    </div>
                </div>
            
                <!-- number_input : 使用期限 ,  serviceLife  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('使用期限')}">使用期限</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="serviceLife" id="serviceLife" name="serviceLife" th:placeholder="${ lang.translate('请输入'+'使用期限') }" type="text" class="layui-input"    lay-verify="|required"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="0" />
                    </div>
                </div>
            
                <!-- text_input : 父节点 ,  parentId -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('父节点')}">父节点</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="parentId" id="parentId" name="parentId" th:placeholder="${ lang.translate('请输入'+'父节点') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- number_input : 排序 ,  sort  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('排序')}">排序</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="sort" id="sort" name="sort" th:placeholder="${ lang.translate('请输入'+'排序') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="true" decimal="false" allow-negative="true" step="1.0"   scale="0" />
                    </div>
                </div>
            
                <!-- text_area : 节点路径 ,  hierarchy  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('节点路径')}">节点路径</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <textarea lay-filter="hierarchy" id="hierarchy" name="hierarchy" th:placeholder="${ lang.translate('请输入'+'节点路径') }" class="layui-textarea" style="height: 120px" ></textarea>
                    </div>
                </div>
            
                <!-- text_area : 节点路径名称 ,  hierarchyName  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('节点路径名称')}">节点路径名称</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <textarea lay-filter="hierarchyName" id="hierarchyName" name="hierarchyName" th:placeholder="${ lang.translate('请输入'+'节点路径名称') }" class="layui-textarea" style="height: 120px" ></textarea>
                    </div>
                </div>
            
                <!-- text_input : 备注 ,  notes -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('备注')}">备注</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入'+'备注') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- text_input : 修改人ID ,  updateBy -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('修改人ID')}">修改人ID</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="updateBy" id="updateBy" name="updateBy" th:placeholder="${ lang.translate('请输入'+'修改人ID') }" type="text" class="layui-input"  />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消','','form.button')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('eam_category_finance:create','eam_category_finance:update','eam_category_finance:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存','','form.button')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var VALIDATE_CONFIG={"serviceLife":{"labelInForm":"使用期限","inputType":"number_input","required":true},"categoryCode":{"labelInForm":"编码","inputType":"text_input","required":true}};
    var AUTH_PREFIX="eam_category_finance";


</script>



<script th:src="'/business/eam/category_finance/category_finance_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/category_finance/category_finance_form.js?'+${cacheKey}"></script>

</body>
</html>