<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
--><project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <artifactId>doxia</artifactId>
    <groupId>org.apache.maven.doxia</groupId>
    <version>1.9</version>
    <relativePath>../pom.xml</relativePath>
  </parent>

  <artifactId>doxia-modules</artifactId>

  <name>Doxia :: Modules</name>
  <packaging>pom</packaging>

  <description>Doxia modules for several markup languages.</description>

  <modules>
    <module>doxia-module-apt</module>
    <module>doxia-module-confluence</module>
    <module>doxia-module-docbook-simple</module>
    <module>doxia-module-fml</module>
    <module>doxia-module-fo</module>
    <module>doxia-module-itext</module>
    <module>doxia-module-latex</module>
    <module>doxia-module-rtf</module>
    <module>doxia-module-twiki</module>
    <module>doxia-module-xdoc</module>
    <module>doxia-module-xhtml</module>
    <module>doxia-module-xhtml5</module>
    <!-- this has a dep on xhtml module, so needs to be built last! -->
    <module>doxia-module-markdown</module>
  </modules>

  <dependencies>
    <dependency>
      <groupId>org.apache.maven.doxia</groupId>
      <artifactId>doxia-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.doxia</groupId>
      <artifactId>doxia-sink-api</artifactId>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-component-annotations</artifactId>
    </dependency>

    <!-- test -->
    <dependency>
      <groupId>org.apache.maven.doxia</groupId>
      <artifactId>doxia-core</artifactId>
      <type>test-jar</type>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
