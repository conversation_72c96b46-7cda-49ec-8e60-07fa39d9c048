<!--
/**
 * 库存资产 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2024-05-06 09:57:50
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
    <title th:text="${lang.translate('库存资产')}">库存资产</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 任务 , taskId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 资产 , assetId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 盘点状态 , inventoryStatus ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('盘点状态')}" class="search-label inventoryStatus-label">盘点状态</span><span class="search-colon">:</span></div>
                        <div id="inventoryStatus" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.AssetInventoryDetailStatusEnum')}" style="width:180px" extraParam="{}"></div>
                    </div>
                    <!-- 差异数量 , assetNumber ,typeName=number_input, isHideInSearch=true -->
                    <!-- 盘点时间 , operTime ,typeName=date_input, isHideInSearch=true -->
                    <!-- 修改人ID , updateBy ,typeName=text_input, isHideInSearch=true -->
                    <!-- 物品类型 , withGoodsStockType ,typeName=text_input, isHideInSearch=true -->
                    <!-- 仓库 , withWarehouse ,typeName=text_input, isHideInSearch=true -->
                    <!-- 库位 , withPosition ,typeName=text_input, isHideInSearch=true -->
                    <!-- 名称 , withName ,typeName=text_input, isHideInSearch=true -->
                    <!-- 编号 , withCode ,typeName=text_input, isHideInSearch=true -->
                    <!-- 型号 , withModel ,typeName=text_input, isHideInSearch=true -->
                    <!-- 数量 , withNumber ,typeName=text_input, isHideInSearch=true -->
                    <!-- 厂商 , withManufacturer ,typeName=text_input, isHideInSearch=true -->
                    <!-- 品牌 , withBrand ,typeName=text_input, isHideInSearch=true -->
                    <!-- 盘点人员 , operId ,typeName=button, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('盘点人员')}" class="search-label operId-label">盘点人员</span><span class="search-colon">:</span></div>
                            <input lay-filter="operId" id="operId" name="operId"  type="hidden" class="layui-input"    lay-verify="|required"   />
                            <button id="operId-button" type="button" action-type="emp-dialog" class="layui-btn layui-btn-primary   " style="width: 180px"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择人员')}" th:default-label="${lang.translate('请选择人员')}">按钮文本</span></button>
                    </div>
                    <!-- 盘点备注 , inventoryNotes ,typeName=text_area, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('盘点备注')}" class="search-label inventoryNotes-label">盘点备注</span><span class="search-colon">:</span></div>
                        <input id="inventoryNotes" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>


                </div>
            </div>


            <!-- 按钮区域 -->
            <div id="search-area" class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索','','cmp:table.search')}">搜索</span></button>
            </div>
        </div>

        <div id="table-area" style="margin-top: 42px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
        <button th:if="${perm.checkAuth('eam_stock_inventory_asset:create')}" id="add-button" class="layui-btn icon-btn layui-btn-sm create-new-button " lay-event="create"><i class="layui-icon">&#xe654;</i><span th:text="${lang.translate('新建','','cmp:table.button')}">新建</span></button>
        <button th:if="${perm.checkAuth('eam_stock_inventory_asset:delete-by-ids')}" id="delete-button" class="layui-btn icon-btn layui-btn-danger layui-btn-sm batch-delete-button " lay-event="batch-del"><i class="layui-icon">&#xe67e;</i><span th:text="${lang.translate('删除','','cmp:table.button')}">删除</span></button>
    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">

    <button th:if="${perm.checkAuth('eam_stock_inventory_asset:view-form')}" class="layui-btn layui-btn-primary layui-btn-xs ops-view-button " lay-event="view"  data-id="{{d.id}}"> <span th:text="${lang.translate('查看','','cmp:table.ops')}">查看</span></button>
    <button th:if="${perm.checkAnyAuth('eam_stock_inventory_asset:update','eam_stock_inventory_asset:save')}" class="layui-btn layui-btn-primary layui-btn-xs ops-edit-button " lay-event="edit"data-id="{{d.id}}"><span th:text="${lang.translate('修改','','cmp:table.ops')}">修改</span></button>


    <button th:if="${perm.checkAuth('eam_stock_inventory_asset:delete')}" class="layui-btn layui-btn-xs layui-btn-danger ops-delete-button " lay-event="del" data-id="{{d.id}}"><span th:text="${lang.translate('删除','','cmp:table.ops')}">删除</span></button>


</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
    var SELECT_INVENTORYSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetInventoryDetailStatusEnum')}]];
    var AUTH_PREFIX="eam_stock_inventory_asset";

    // taskId
    var TASK_ID = [[${taskId}]] ;

</script>

<script th:src="'/business/eam/stock_inventory_asset/stock_inventory_asset_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/stock_inventory_asset/stock_inventory_asset_list.js?'+${cacheKey}"></script>

</body>
</html>