<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.sonatype.spice</groupId>
    <artifactId>spice-parent</artifactId>
    <version>17</version>
  </parent>

  <groupId>org.codehaus.plexus</groupId>
  <artifactId>plexus-archiver</artifactId>
  <version>2.1</version>

  <name>Plexus Archiver Component</name>

  <scm>
    <connection>scm:git:**************:sonatype/plexus-archiver.git</connection>
    <developerConnection>scm:git:**************:sonatype/plexus-archiver.git</developerConnection>
    <url>http://github.com/sonatype/plexus-archiver</url>
  </scm>
  <issueManagement>
    <system>jira</system>
    <url>http://jira.codehaus.org/browse/PLXCOMP/component/12540</url>
  </issueManagement>

  <distributionManagement>
    <repository>
      <id>plexus-releases</id>
      <name>Plexus Release Repository</name>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
    <snapshotRepository>
      <id>plexus-snapshots</id>
      <name>Plexus Snapshot Repository</name>
      <url>${plexusDistMgmtSnapshotsUrl}</url>
    </snapshotRepository>
    <site>
      <id>codehaus.org</id>
      <url>dav:https://dav.codehaus.org/plexus</url>
    </site>
  </distributionManagement>

  <properties>
    <useJvmChmod>false</useJvmChmod>
  </properties>

  <contributors>
    <contributor>
      <name>Dan Tran</name>
    </contributor>
    <contributor>
      <name>Richard van der Hoff</name>
    </contributor>
  </contributors>

  <dependencies>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-container-default</artifactId>
      <version>1.0-alpha-9-stable-1</version>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-utils</artifactId>
      <version>3.0</version>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-io</artifactId>
      <version>2.0.2</version>
    </dependency>
  </dependencies>
  
  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <systemPropertyVariables>
            <useJvmChmod>${useJvmChmod}</useJvmChmod>
          </systemPropertyVariables>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-release-plugin</artifactId>
        <version>2.2.1</version>
      </plugin>
    </plugins>
  </build>
  
</project>
