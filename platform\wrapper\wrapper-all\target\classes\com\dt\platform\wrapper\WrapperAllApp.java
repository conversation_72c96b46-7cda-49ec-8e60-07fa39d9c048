package com.dt.platform.wrapper;

import com.dt.platform.common.starter.BootApplication;
import com.dt.platform.framework.EamPlatformMeta;
import com.dt.platform.framework.OaPlatformMeta;
import com.dt.platform.framework.WebPlatformMeta;
import com.github.foxnic.commons.log.Logger;
import org.github.foxnic.web.framework.FoxnicWebMeta;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

@ComponentScan(basePackages = {
		//基础包
		FoxnicWebMeta.SERVICE_STORAGE_PACKAGE ,
		FoxnicWebMeta.SERVICE_SYSTEM_PACKAGE ,
		FoxnicWebMeta.SERVICE_OAUTH_PACKAGE,
		FoxnicWebMeta.SERVICE_HRM_PACKAGE,
		FoxnicWebMeta.SERVICE_PCM_PACKAGE,
		FoxnicWebMeta.SERVICE_CHANGES_PACKAGE,
		FoxnicWebMeta.SERVICE_BPM_PACKAGE,
		FoxnicWebMeta.SERVICE_DATA_PERM_PACKAGE,
		FoxnicWebMeta.SERVICE_JOB_PACKAGE,
		FoxnicWebMeta.SERVICE_DOCS_PACKAGE,

		// FRAMEWORK
		EamPlatformMeta.FRAMEWORK_PACKAGE+".datasource.eam",
		EamPlatformMeta.JOB_PACKAGE,
		//Web
		WebPlatformMeta.COMMON_PACKAGE,
		WebPlatformMeta.MOBILE_PACKAGE,
		//OA
		OaPlatformMeta.KNOWLEDGEBASE_PACKAGE,
		OaPlatformMeta.OA_PACKAGE,
		//EAM
		EamPlatformMeta.SERVICE_EAM_PACKAGE,
		//HR
//		EamPlatformMeta.SERVICE_HR_PACKAGE,
		//OPS
//		OpsPlatformMeta.DATACENTER_PACKAGE,
//		OpsPlatformMeta.OPS_PACKAGE,

		// 合同-Jar引用
//		ContractMeta.SERVICE_CONTRACT_PACKAGE,
		// 客户-Jar引用
//		CustomerMeta.SERVICE_CUSTOMER_PACKAGE,
		"com.dt.platform.eam.service.bpm",
		// 兼容性配置
		"com.dt.platform.wrapper.config"
}, excludeFilters = {
		@ComponentScan.Filter(type = org.springframework.context.annotation.FilterType.ASSIGNABLE_TYPE,
			classes = {
				com.dt.platform.common.config.UreportSpringContext.class,
				com.dt.platform.common.config.MagicApiCustomJsonValueProvider.class
			})
})
@SpringBootApplication
public class WrapperAllApp {
	public static void main(String[] args) {





		BootApplication.run(WrapperAllApp.class, args);
		Logger.info("系统启动完成!");

	}
}
