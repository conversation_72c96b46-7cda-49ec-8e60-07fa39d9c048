<!DOCTYPE html>
<html style="background-color: #FFFFFF">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <title th:text="${lang.translate('按组织查询')}">按组织查询</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/zTree/css/zTreeStyle/zTreeStyle.css" type="text/css">
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" th:href="@{/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css}" rel="stylesheet"/>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">

    <style>
        .top-container {
            display: -webkit-flex; /* Safari */
            display: flex;
            width: 100%;
            align-items:top;
        }
        .left-tree {
            width: 260px;
            background-color: #ffffff;
            border-right: 1px #e6e6e6 solid;
        }
        .right-tab {
            flex:1;
        }
        .ztree li span.button.icon_com_ico_docu{margin-right:2px; background: url(/assets/icons/gongsi.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle;background-size: 100% auto;}
        .ztree li span.button.icon_com_ico_close{margin-right:2px; background: url(/assets/icons/gongsi.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle;background-size: 100% auto;}
        .ztree li span.button.icon_com_ico_open{margin-right:2px; background: url(/assets/icons/gongsi.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle;background-size: 100% auto;}

        .ztree li span.button.icon_dept_ico_docu{margin-right:2px; background: url(/assets/icons/xuexiao.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle;background-size: 100% auto;}
        .ztree li span.button.icon_dept_ico_close{margin-right:2px; background: url(/assets/icons/xuexiao.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle;background-size: 100% auto;}
        .ztree li span.button.icon_dept_ico_open{margin-right:2px; background: url(/assets/icons/xuexiao.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle;background-size: 100% auto;}

        .ztree li span.button.icon_pos_ico_docu{margin-right:2px; background: url(/assets/icons/yuangongtongxinlu.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle;background-size: 100% auto;}
        .ztree li span.button.icon_pos_ico_close{margin-right:2px; background: url(/assets/icons/yuangongtongxinlu.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle;background-size: 100% auto;}
        .ztree li span.button.icon_pos_ico_open{margin-right:2px; background: url(/assets/icons/yuangongtongxinlu.png) no-repeat scroll 0 0 transparent; vertical-align:top; *vertical-align:middle;background-size: 100% auto;}


    </style>
</head>

<body>

<div class="top-container">
    <div class="left-tree">
        <div class="layui-form toolbar" style="padding:6px; border-bottom:1px #e6e6e6 solid; " id="toolbar">
            <table style="width: 100%"><tr>
                <td>
                    <input id="search-input" class="layui-input search-input" type="text" th:placeholder="${lang.translate('请输入关键字')}" style="width: 100%"/>
                </td>

            </tr></table>
        </div>
        <div id="tree-container" style="overflow:auto;height:800px">
            <ul id="menu-tree" class="ztree"></ul>
        </div>
    </div>
    <div class="right-tab" style="margin-left: 8px;margin-right: 8px">
        <div class="layui-tab-item layui-show" type="emp">
            <iframe id="employee-list-ifr" src="/business/eam/asset/asset_search/employee_list.html" frameborder=0 width="100%"></iframe>
        </div>
    </div>
</div>


<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/zTree/js/jquery.ztree.all-3.5.min.js" th:src="'/assets/libs/zTree/js/jquery.ztree.all-3.5.min.js?'+${cacheKey}"></script>

<script src="/business/eam/asset/asset_search/employee_tree.js" th:src="'/business/eam/asset/asset_search/employee_tree.js?'+${cacheKey}"></script>

</body>
</html>
