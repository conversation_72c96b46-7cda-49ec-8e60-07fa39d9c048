<!--
/**
 * 巡检计划 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2023-08-04 08:45:13
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
    <title th:text="${lang.translate('巡检计划')}">巡检计划</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 办理状态 , status ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('办理状态')}" class="search-label status-label">办理状态</span><span class="search-colon">:</span></div>
                        <div id="status" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.AssetHandleStatusEnum')}" style="width:180px" extraParam="{}"></div>
                    </div>
                    <!-- 负责人 , leaderId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 位置范围 , posDetail ,typeName=text_input, isHideInSearch=true -->
                    <!-- 开始日期 , startDate ,typeName=date_input, isHideInSearch=true -->
                    <!-- 截止日期 , endDate ,typeName=date_input, isHideInSearch=true -->
                    <!-- 计划周期 , actionCycleId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 预计用时(时) , completionTime ,typeName=number_input, isHideInSearch=true -->
                    <!-- 超时处理 , overtimeMethod ,typeName=select_box, isHideInSearch=true -->
                    <!-- 提醒时间(时) , remindTime ,typeName=number_input, isHideInSearch=true -->
                    <!-- 上次执行 , lastTime ,typeName=date_input, isHideInSearch=true -->
                    <!-- 下次执行 , nextTime ,typeName=date_input, isHideInSearch=true -->
                    <!-- 备注 , notes ,typeName=text_area, isHideInSearch=true -->
                    <!-- 巡检点数 , itemCount ,typeName=text_input, isHideInSearch=true -->
                    <!-- 巡检点数(未启用) , itemDisableCount ,typeName=text_input, isHideInSearch=true -->
                    <!-- 计划状态 , planStatus ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('计划状态')}" class="search-label planStatus-label">计划状态</span><span class="search-colon">:</span></div>
                        <div id="planStatus" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.EamPlanStatusEnum')}" style="width:180px" extraParam="{}"></div>
                    </div>
                    <!-- 计划名称 , name ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('计划名称')}" class="search-label name-label">计划名称</span><span class="search-colon">:</span></div>
                        <input id="name" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 计划单据 , planCode ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('计划单据')}" class="search-label planCode-label">计划单据</span><span class="search-colon">:</span></div>
                        <input id="planCode" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 巡检班组 , groupId ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('巡检班组')}" class="search-label groupId-label">巡检班组</span><span class="search-colon">:</span></div>
                        <div id="groupId" th:data="${'/service-eam/eam-inspection-group/query-paged-list'}" style="width:180px" extraParam="{}"></div>
                    </div>
                    <!-- 巡检顺序 , inspectionMethod ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('巡检顺序')}" class="search-label inspectionMethod-label">巡检顺序</span><span class="search-colon">:</span></div>
                        <div id="inspectionMethod" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.InspectionMethodEnum')}" style="width:180px" extraParam="{}"></div>
                    </div>
                    <!-- 计划类型 , planType ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('计划类型')}" class="search-label planType-label">计划类型</span><span class="search-colon">:</span></div>
                        <div id="planType" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.InspectionPlanTypeEnum')}" style="width:180px" extraParam="{}"></div>
                    </div>


                </div>
            </div>


            <!-- 按钮区域 -->
            <div id="search-area" class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索','','cmp:table.search')}">搜索</span></button>
                <button id="search-button-advance" class="layui-btn layui-btn-primary icon-btn search-button-advance"><i class="layui-icon">&#xe671;</i><span th:text="${lang.translate('更多','','cmp:table.search')}">更多</span></button>
            </div>
        </div>

        <div id="table-area" style="margin-top: 42px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
        <button th:if="${perm.checkAuth('eam_inspection_plan:create')}" id="add-button" class="layui-btn icon-btn layui-btn-sm create-new-button " lay-event="create"><i class="layui-icon">&#xe654;</i><span th:text="${lang.translate('新建','','cmp:table.button')}">新建</span></button>
    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">

    <button th:if="${perm.checkAuth('eam_inspection_plan:view-form')}" class="layui-btn layui-btn-primary layui-btn-xs ops-view-button " lay-event="view"  data-id="{{d.id}}"> <span th:text="${lang.translate('查看','','cmp:table.ops')}">查看</span></button>
    <button th:if="${perm.checkAnyAuth('eam_inspection_plan:update','eam_inspection_plan:save')}" class="layui-btn layui-btn-primary layui-btn-xs ops-edit-button " lay-event="edit"data-id="{{d.id}}"><span th:text="${lang.translate('修改','','cmp:table.ops')}">修改</span></button>


    <button th:if="${perm.checkAuth('eam_inspection_plan:delete')}" class="layui-btn layui-btn-xs layui-btn-danger ops-delete-button " lay-event="del" data-id="{{d.id}}"><span th:text="${lang.translate('删除','','cmp:table.ops')}">删除</span></button>

    <button th:if="${perm.checkAuth('eam_inspection_plan:start')}"class="layui-btn layui-btn-xs  start-button " lay-event="start" data-id="{{d.id}}"><span th:text="${lang.translate('启动','','cmp:table.ops')}">启动</span></button>
    <button th:if="${perm.checkAuth('eam_inspection_plan:stop')}"class="layui-btn layui-btn-xs  stop-button " lay-event="stop" data-id="{{d.id}}"><span th:text="${lang.translate('停用','','cmp:table.ops')}">停用</span></button>
    <button th:if="${perm.checkAuth('eam_inspection_plan:execute')}"class="layui-btn layui-btn-xs  execute-button " lay-event="execute" data-id="{{d.id}}"><span th:text="${lang.translate('创建任务','','cmp:table.ops')}">创建任务</span></button>

</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetHandleStatusEnum')}]];
    var SELECT_PLANSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.EamPlanStatusEnum')}]];
    var SELECT_PLANTYPE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.InspectionPlanTypeEnum')}]];
    var SELECT_INSPECTIONMETHOD_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.InspectionMethodEnum')}]];
    var SELECT_OVERTIMEMETHOD_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.InspectionTimeoutHandleEnum')}]];
    var AUTH_PREFIX="eam_inspection_plan";


</script>

<script th:src="'/business/eam/inspection_plan/inspection_plan_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/inspection_plan/inspection_plan_list.js?'+${cacheKey}"></script>

</body>
</html>