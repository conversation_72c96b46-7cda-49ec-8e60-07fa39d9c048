<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.camunda.commons</groupId>
    <artifactId>camunda-commons-bom</artifactId>
    <version>1.9.0</version>
    <relativePath>bom</relativePath>
  </parent>

  <artifactId>camunda-commons-root</artifactId>
  <name>camunda Commons - Root Pom</name>
  <inceptionYear>2014</inceptionYear>
  <packaging>pom</packaging>

  <modules>
    <module>bom</module>
    <module>logging</module>
    <module>utils</module>
    <module>testing</module>
  </modules>

  <properties>
    <license.includeTransitiveDependencies>false</license.includeTransitiveDependencies>
  </properties>

  <dependencyManagement>
    <dependencies>

      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>4.12</version>
      </dependency>

      <dependency>
        <groupId>org.assertj</groupId>
        <artifactId>assertj-core</artifactId>
        <version>2.9.1</version>
      </dependency>

      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>1.7.26</version>
      </dependency>

      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-classic</artifactId>
        <version>1.1.2</version>
      </dependency>

      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest-library</artifactId>
        <version>1.3</version>
      </dependency>

    </dependencies>
  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.felix</groupId>
          <artifactId>maven-bundle-plugin</artifactId>
          <configuration>
            <instructions>
              <Export-Package>org.camunda*</Export-Package>
            </instructions>
          </configuration>
          <executions>
            <execution>
              <id>bundle-manifest</id>
              <phase>process-classes</phase>
              <goals>
                <goal>manifest</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>clirr-maven-plugin</artifactId>
          <version>2.6.1</version>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>license-maven-plugin</artifactId>
        <version>1.14</version>
        <configuration>
          <acceptPomPackaging>true</acceptPomPackaging>
          <excludedScopes>test</excludedScopes>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>license-header-check</id>
      <build>
        <plugins>
          <plugin>
            <groupId>com.mycila</groupId>
            <artifactId>license-maven-plugin</artifactId>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

  <scm>
    <url>https://github.com/camunda/camunda-commons</url>
    <connection>scm:git:**************:camunda/camunda-commons.git</connection>
    <developerConnection>scm:git:**************:camunda/camunda-commons.git</developerConnection>
    <tag>1.9.0</tag>
  </scm>

</project>
