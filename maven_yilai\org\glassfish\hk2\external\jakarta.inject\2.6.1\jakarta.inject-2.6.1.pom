<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 2010, 2018 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Public License v. 2.0, which is available at
    http://www.eclipse.org/legal/epl-2.0.

    This Source Code may also be made available under the following Secondary
    Licenses when the conditions for such availability set forth in the
    Eclipse Public License v. 2.0 are satisfied: GNU General Public License,
    version 2 with the GNU Classpath Exception, which is available at
    https://www.gnu.org/software/classpath/license.html.

    SPDX-License-Identifier: EPL-2.0 OR GPL-2.0 WITH Classpath-exception-2.0

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>    
    <parent>
        <groupId>org.glassfish.hk2</groupId>
        <artifactId>external</artifactId>
        <version>2.6.1</version>
    </parent>
    <groupId>org.glassfish.hk2.external</groupId>
    <artifactId>jakarta.inject</artifactId>
    <name>javax.inject:${javax-inject.version} as OSGi bundle</name>
    <description>Injection API (JSR 330) version ${javax.inject.version} repackaged as OSGi bundle</description>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>step1-unpack-sources</id>
                        <phase>process-sources</phase>
                        <goals>
                            <goal>unpack</goal>
                        </goals>
                        <configuration>
                            <artifactItems>
                                <artifactItem>
                                    <groupId>javax.inject</groupId>
                                    <artifactId>javax.inject</artifactId>
                                    <version>${javax-inject.version}</version>
                                    <classifier>sources</classifier>
                                    <overWrite>false</overWrite>
                                    <outputDirectory>${project.build.directory}/alternateLocation</outputDirectory>
                                </artifactItem>
                            </artifactItems>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>step2-add-sources</id>
                        <phase>process-sources</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>${project.build.directory}/alternateLocation</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <configuration>
                    <instructions>
                        <Export-Package>
                            <!-- 
                                Actually there is no need to any of the implementation stuff, but 
                                because of a bug in Hibernate Validator, where in it uses thread's context
                                class loader to load implementation classes, we have to export implementation
                                packages so that our special context class loader can find them.
                                See http://opensource.atlassian.com/projects/hibernate/browse/HV-363
                            -->
                            javax.inject.*; version=${javax-inject.version} 
                        </Export-Package>
                        <Implementation-Version>${javax-inject.version}</Implementation-Version>
                    </instructions>
                </configuration>
                <executions>
                    <execution>
                        <id>osgi-manifest</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>manifest</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>javax.inject</groupId>
            <artifactId>javax.inject</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>
</project>
