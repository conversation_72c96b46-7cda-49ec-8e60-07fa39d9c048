/**
 * 保养方案 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2022-06-04 07:52:17
 */

layui.config({
    dir: layuiPath,
    base: '/module/'
}).extend({
    xmSelect: 'xm-select/xm-select',
    foxnicUpload: 'upload/foxnic-upload'
})
//
layui.define(['form', 'table', 'util', 'settings', 'admin', 'upload','foxnic','xmSelect','laydate','foxnicUpload','dropdown'],function () {

    var admin = layui.admin,settings = layui.settings,form = layui.form,upload = layui.upload,laydate= layui.laydate,dropdown=layui.dropdown;
    table = layui.table,layer = layui.layer,util = layui.util,fox = layui.foxnic,xmSelect = layui.xmSelect,foxup=layui.foxnicUpload;

    //模块基础路径
    const moduleURL="/service-eam/eam-maintain-plan";
    var timestamp = Date.parse(new Date());

    var actionCycleId="";
    var url="";
    var ps={};
    var formAction=admin.getTempData('eam-maintain-plan-form-data-form-action');
    var time= Date.parse(new Date());


    function openCronForm(data,queryString,url,ps){
        admin.putTempData('eam-action-crontab-form-data', data);
        var area=admin.getTempData('eam-action-crontab-form-area');
        var height= (area && area.height) ? area.height : ($(window).height()*0.6);
        var top= (area && area.top) ? area.top : (($(window).height()-height)/2);
        var title = fox.translate('周期');
        admin.popupCenter({
            title: title,
            resize: false,
            offset: [top,null],
            area: ["80%",height+"px"],
            type: 2,
            id:"eam-action-crontab-form-data-win",
            content: '/business/eam/action_crontab/action_crontab_form.html' +queryString,
            finish: function () {
                console.log("get",url,ps);
                admin.post(url, ps, function (r) {
                    if (r.success) {
                        var rcd=r.data;
                        actionCycleId=rcd.id;
                        $("#actionCycleId").val(rcd.crontab);
                    } else {
                        fox.showMessage(r);
                    }
                });

            }
        });
    }

    $("#actionCycleId").click(function(){
        var ownerId=""
        var queryString="";
        console.log("1111 method")
        if(formAction=="create"){
            console.log("create method")
            url="/service-eam/eam-action-crontab/get-by-owner-id"
            ownerId=time;
            ps.ownerId=ownerId;
            admin.putTempData('eam-action-crontab-form-data-form-action', "create",true);
            queryString="?ownerId="+ownerId;
            var tD={};
            openCronForm(tD,queryString,url,ps);
        }else if(formAction=="edit"){
            if(actionCycleId&&actionCycleId.length>5){
                console.log("edit method")
                queryString="?id="+actionCycleId
                url="/service-eam/eam-action-crontab/get-by-id"
                admin.putTempData('eam-action-crontab-form-data-form-action', "edit",true);
                ps.id=actionCycleId;
                admin.post(url, ps, function (r) {
                    if (r.success) {
                        openCronForm(r.data,queryString,url,ps);
                    } else {
                        fox.showMessage(r);
                    }
                });
            }else{
                //按照create 方式
                console.log("create method")
                url="/service-eam/eam-action-crontab/get-by-owner-id"
                ownerId=time;
                ps.ownerId=ownerId;
                admin.putTempData('eam-action-crontab-form-data-form-action', "create",true);
                queryString="?ownerId="+ownerId;
                var tD={};
                openCronForm(tD,queryString,url,ps);
            }


        }else if(formAction=="view"){
            console.log("view method")
            url="/service-eam/eam-action-crontab/get-by-id"
            admin.putTempData('eam-action-crontab-form-data-form-action', "view",true);
            queryString="?id="+actionCycleId;
            ps.id=actionCycleId;
            admin.post(url, ps, function (r) {
                if (r.success) {
                    openCronForm(r.data,queryString,url,ps);
                } else {
                    fox.showMessage(r);
                }
            });
        }
    });


    //列表页的扩展
    var list={
        /**
         * 列表页初始化前调用
         * */
        beforeInit:function () {
            console.log("list:beforeInit");
        },
        /**
         * 表格渲染前调用
         * @param cfg 表格配置参数
         * */
        beforeTableRender:function (cfg){
            cfg.cellMinWidth=160;;
        },
        /**
         * 表格渲染后调用
         * */
        afterTableRender :function (){

        },
        afterSearchInputReady: function() {
            console.log("list:afterSearchInputReady");
        },
        /**
         * 对话框打开之前调用，如果返回 null 则不打开对话框
         * */
        beforeDialog:function (param){
            //param.title="覆盖对话框标题";
            return param;
        },
        /**
         * 对话框回调，表单域以及按钮 会自动改变为选中的值，此处处理额外的逻辑即可
         * */
        afterDialog:function (param,result) {
            console.log('dialog',param,result);
        },
        /**
         * 当下拉框别选择后触发
         * */
        onSelectBoxChanged:function(id,selected,changes,isAdd) {
            console.log('onSelectBoxChanged',id,selected,changes,isAdd);
        },
        /**
         * 当日期选择组件选择后触发
         * */
        onDatePickerChanged:function(id,value, date, endDate) {
            console.log('onDatePickerChanged',id,value, date, endDate);
        },
        /**
         * 查询前调用
         * @param conditions 复合查询条件
         * @param param 请求参数
         * @param location 调用的代码位置
         * */
        beforeQuery:function (conditions,param,location) {
            console.log('beforeQuery',conditions,param,location);
            return true;
        },
        /**
         * 查询结果渲染后调用
         * */
        afterQuery : function (data) {
            for (var i = 0; i < data.length; i++) {
                //如果审批中或审批通过的不允许编辑
                console.log(data[i]);
                if(data[i].cycleMethod=="once") {
                    fox.disableButton($('.start-button').filter("[data-id='" + data[i].id + "']"), true);
                    fox.disableButton($('.stop-button').filter("[data-id='" + data[i].id + "']"), true);
                }else if(data[i].cycleMethod=="cycle"){
                    if(data[i].status=="acting"){
                        fox.disableButton($('.start-button').filter("[data-id='" + data[i].id + "']"), true);
                    }else if(data[i].status=="stop"){
                        fox.disableButton($('.stop-button').filter("[data-id='" + data[i].id + "']"), true);
                    }


                }
            }
        },
        /**
         * 进一步转换 list 数据
         * */
        templet:function (field,value,r) {

            if(field=="itemCount"){
                if(value){
                    return value
                }else{
                    return 0;
                }
            }


            if(value==null) return "";
            return value;
        },
        /**
         * 表单页面打开时，追加更多的参数信息
         * */
        makeFormQueryString:function(data,queryString,action) {
            return queryString;
        },
        /**
         * 在新建或编辑窗口打开前调用，若返回 false 则不继续执行后续操作
         * */
        beforeEdit:function (data) {
            console.log('beforeEdit',data);
            return true;
        },
        /**
         * 单行删除前调用，若返回false则不执行后续操作
         * */
        beforeSingleDelete:function (data) {
            console.log('beforeSingleDelete',data);
            return true;
        },
        afterSingleDelete:function (data){
            console.log('beforeSingleDelete',data);
            return true;
        },
        /**
         * 批量删除前调用，若返回false则不执行后续操作
         * */
        beforeBatchDelete:function (selected) {
            console.log('beforeBatchDelete',selected);
            return true;
        },
        /**
         * 批量删除后调用，若返回false则不执行后续操作
         * */
        afterBatchDelete:function (data) {
            console.log('afterBatchDelete',data);
            return true;
        },
        /**
         * 工具栏按钮事件前调用，如果返回 false 则不执行后续代码
         * */
        beforeToolBarButtonEvent:function (selected,obj) {
            console.log('beforeToolBarButtonEvent',selected,obj);
            return true;
        },
        /**
         * 列表操作栏按钮事件前调用，如果返回 false 则不执行后续代码
         * */
        beforeRowOperationEvent:function (data,obj) {
            console.log('beforeRowOperationEvent',data,obj);
            return true;
        },
        /**
         * 表格右侧操作列更多按钮事件
         * */
        moreAction:function (menu,data, it){
            console.log('moreAction',menu,data,it);
        },
        billOper:function(url,btnClass,ps,successMessage){
            var btn=$('.'+btnClass).filter("[data-id='" +ps.id + "']");
            var api=moduleURL+"/"+url;
            top.layer.confirm(fox.translate('确定进行该操作吗？'), function (i) {
                top.layer.close(i);
                admin.post(api, ps, function (r) {
                    if (r.success) {
                        top.layer.msg(successMessage, {time: 1000});
                        window.module.refreshTableData();
                    } else {
                        var errs = [];
                        if(r.errors&&r.errors.length>0){
                            for (var i = 0; i < r.errors.length; i++) {
                                if (errs.indexOf(r.errors[i].message) == -1) {
                                    errs.push(r.errors[i].message);
                                }
                            }
                            top.layer.msg(errs.join("<br>"), {time: 2000});
                        } else {
                            top.layer.msg(r.message, {time: 2000});
                        }
                    }
                }, {delayLoading: 1000, elms: [btn]});
            });
        },
        start:function (item){
            list.billOper("start","start-button",{id:item.id},"已启动");
        },
        stop:function (item){
             list.billOper("stop","stop-button",{id:item.id},"已停止");
        },
        execute:function (item){
             list.billOper("create-task","execute-button",{id:item.id},"操作成功");
        },


    /**
         * 末尾执行
         */
        ending:function() {

        }
    }

    //表单页的扩展
    var form={
        /**
         * 表单初始化前调用 , 并传入表单数据
         * */
        beforeInit:function (action,data) {
            //获取参数，并调整下拉框查询用的URL
            //var companyId=admin.getTempData("companyId");
            //fox.setSelectBoxUrl("employeeId","/service-hrm/hrm-employee/query-paged-list?companyId="+companyId);
            if(formAction=="create"){
                console.log("none")
            }else{
               //


            }

            console.log("form:beforeInit")
        },
        /**
         * 窗口调节前
         * */
        beforeAdjustPopup:function () {
            console.log('beforeAdjustPopup');
            return true;
        },
        /**
         * 表单数据填充前
         * */
        beforeDataFill:function (data) {
            console.log('beforeDataFill',data);
        },
        /**
         * 表单数据填充后
         * */
        afterDataFill:function (data) {
            console.log('afterDataFill',data);
            if(data.id){
                if(data.actionCycleId){
                    actionCycleId=data.actionCycleId;
                }
                if(data.actionCrontab){
                    $("#actionCycleId").val(data.actionCrontab.crontab);
                }
            }
            //
            // if(formAction=="create"){
            //     console.log("none");
            // }else{
            //     var cycleSelect=xmSelect.get("#cycleMethod",true);
            //     cycleSelect.update({disabled: true})
            //
            //     $("#cycleMethod").find("xm-select").css("background-color","#e6e6e6");
            // }

        },
        /**
         * 对话框打开之前调用，如果返回 null 则不打开对话框
         * */
        beforeDialog:function (param){
            //param.title="覆盖对话框标题";
            return param;
        },
        /**
         * 对话框回调，表单域以及按钮 会自动改变为选中的值，此处处理额外的逻辑即可
         * */
        afterDialog:function (param,result) {
            console.log('dialog',param,result);
        },
        /**
         * 当下拉框别选择后触发
         * */
        onSelectBoxChanged:function(id,selected,changes,isAdd) {
            console.log('onSelectBoxChanged',id,selected,changes,isAdd);
        },
        /**
         * 当日期选择组件选择后触发
         * */
        onDatePickerChanged:function(id,value, date, endDate) {
            console.log('onDatePickerChanged',id,value, date, endDate);
        },
        onRadioBoxChanged:function(id,data,checked) {
            console.log('onRadioChanged',id,data,checked);
        },
        onCheckBoxChanged:function(id,data,checked) {
            console.log('onCheckBoxChanged',id,data,checked);
        },

        /**
         * 在流程提交前处理表单数据
         * */
        processFormData4Bpm:function(processInstanceId,param,callback) {
            // 设置流程变量，并通过回调返回
            var variables={};
            // 此回调是必须的，否则流程提交会被中断
            callback(variables);
        },
        /**
         * 数据提交前，如果返回 false，停止后续步骤的执行
         * */
        beforeSubmit:function (data) {
            console.log("beforeSubmit",data);
            data.actionCycleId=actionCycleId;
            data.selectedCode=timestamp;
            return true;
        },
        /**
         * 数据提交后窗口关闭前，如果返回 false，停止后续步骤的执行
         * */
        betweenFormSubmitAndClose:function (param,result) {
            console.log("betweenFormSubmitAndClose",result);
            return true;
        },
        /**
         * 数据提交后执行
         * */
        afterSubmit:function (param,result) {
            console.log("afterSubmitt",param,result);
        },

        /**
         *  加载 设备范围
         */
        assetSelectList:function (ifr,win,data) {
            console.log("assetSelectList",ifr,data);
            //设置 iframe 高度
            ifr.height("450px");
            //设置地址

            var data={};
            data.searchContent={};
            data.assetSelectedCode=timestamp;
            data.assetBusinessType=BILL_TYPE
            data.action=formAction;
            data.ownerCode="asset";
            if(BILL_ID==null)BILL_ID="";
            data.assetOwnerId=BILL_ID;
            admin.putTempData('eam-asset-selected-data'+timestamp,data,true);
            admin.putTempData('eam-asset-selected-action'+timestamp,formAction,true);
            win.location="/business/eam/asset/asset_selected_list.html?pageType="+formAction+"&assetSelectedCode="+timestamp+"&pageType="+formAction;
        },
        /**
         *  加载 保养项目
         */
        maintainSelectList:function (ifr,win,data) {
            console.log("goodsSelectList",ifr,data);
            //设置 iframe 高度
            ifr.height("450px");
            var ownerId="";
            if(data&&data.id){
                ownerId=data.id;
            }else{
                ownerId=timestamp;
            }
            var ownerType="eam_asset_maintain_project"
            var queryString="?pageType="+formAction+"&selectedCode="+timestamp+"&ownerId="+ownerId+"&ownerType="+ownerType;
            //设置地址
            win.location="/business/eam/maintain_project/maintain_project_selected_list.html"+queryString

        },
        /**
         * 文件上传组件回调
         *  event 类型包括：
         *  afterPreview ：文件选择后，未上传前触发；
         *  afterUpload ：文件上传后触发
         *  beforeRemove ：文件删除前触发
         *  afterRemove ：文件删除后触发
         * */
        onUploadEvent: function(e) {
            console.log("onUploadEvent",e);
        },
        /**
         * 末尾执行
         */
        ending:function() {

        }
    }
    //
    window.pageExt={form:form,list:list};
});