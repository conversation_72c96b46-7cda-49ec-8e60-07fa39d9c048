<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.camunda.bpm</groupId>
    <artifactId>camunda-database-settings</artifactId>
    <relativePath>../database</relativePath>
    <version>7.17.0</version>
  </parent>

  <groupId>org.camunda.bpm.springboot.project</groupId>
  <artifactId>camunda-bpm-spring-boot-starter-root</artifactId>
  <name>Camunda Platform - Spring Boot Starter - Root Pom</name>
  <inceptionYear>2015</inceptionYear>

  <packaging>pom</packaging>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <!-- Import dependency management from Spring Boot -->
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>${version.spring-boot}</version>
        <scope>import</scope>
        <type>pom</type>
      </dependency>
    </dependencies>
  </dependencyManagement>
  
  <profiles>
    <profile>
      <id>distro</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <modules>
        <module>starter</module>
        <module>starter-rest</module>
        <module>starter-test</module>
        <module>starter-client/spring</module>
        <module>starter-client/spring-boot</module>
        <module>starter-qa</module>
      </modules>
    </profile>
    <profile>
      <id>distro-ce</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <modules>
        <module>starter-webapp-core</module>
        <module>starter-webapp</module>
        <module>starter-qa</module>
      </modules>
    </profile>
    <profile>
      <id>distro-starter</id>
      <!-- This profile is run in the release CI for building the enterprise artifacts
          It can be removed if the distro job is merged with the assembly and
          distro-ce is not used for building the spring boot starter -->
      <modules>
        <module>starter-webapp-core</module>
        <module>starter-webapp</module>
        <module>starter-qa</module>
      </modules>
    </profile>
    <profile>
      <id>integration-test-spring-boot-starter</id>
      <modules>
        <!-- All modules that contain *IT.java test classes need to be listed here -->

        <module>starter-qa</module>
        <module>starter</module>
        <module>starter-rest</module>
        <module>starter-client/spring-boot</module>
        <module>starter-webapp-core</module>
      </modules>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-failsafe-plugin</artifactId>
            <executions>
              <execution>
                <goals>
                  <goal>integration-test</goal>
                  <goal>verify</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <skipTests>true</skipTests>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

  <build>
    <defaultGoal>clean install</defaultGoal>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <configuration>
            <archive>
              <manifest>
                <addDefaultImplementationEntries />
              </manifest>
            </archive>
          </configuration>
        </plugin>
        <!-- set version for spring-boot-maven-plugin because we use the
          spring-boot-parent only in dependencyManagement section, not as parent. -->
        <plugin>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-maven-plugin</artifactId>
          <version>${version.spring-boot}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-failsafe-plugin</artifactId>
          <configuration>
            <redirectTestOutputToFile>true</redirectTestOutputToFile>
            <trimStackTrace>false</trimStackTrace>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>

    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <version>3.0.0-M1</version>
        <configuration>
          <rules>
            <requireJavaVersion>
              <version>[1.8,)</version>
            </requireJavaVersion>
          </rules>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>enforce</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

    </plugins>
  </build>

</project>
