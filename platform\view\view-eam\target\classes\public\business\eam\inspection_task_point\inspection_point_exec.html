<!--
/**
 * 巡检点 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2023-07-07 22:01:25
 */
 -->
<!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
    <title th:text="${lang.translate('巡检点检查项')}">巡检点检查项</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="opacity:0">

        <input name="id" id="id"  type="hidden"/>

        <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-7929-content">

            <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column" >

                <!-- radio_box : 巡检状态 ,  pointStatus  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('巡检状态')}">巡检状态</div></div>
                    <div class="layui-input-block ">
                        <input  readonly input-type="radio" type="radio" name="pointStatus" lay-filter="pointStatus" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.eam.InspectionTaskPointStatusEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==0)}">
                    </div>
                </div>
                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-7269-content">

            <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column"  style="padding-top: 0px" >

                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('处理动作')}">处理动作</div></div>
                    <div class="layui-input-block ">
                        <div id="actionLabel" input-type="select" th:data="${'/service-eam/eam-inspection-process-action/query-list'}" extraParam="{}"></div>
                    </div>
                </div>


                <!-- text_area : 巡检结果 ,  content  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('巡检结果')}">巡检结果</div></div>
                    <div class="layui-input-block ">
                        <textarea lay-filter="content" id="content" name="content" th:placeholder="${ lang.translate('请输入'+'巡检结果') }" class="layui-textarea" style="height: 150px" ></textarea>
                    </div>
                </div>


                <!--结束：栏次内字段循环-->

            </div>




            <!-- upload : 图片 ,  imageId  -->
            <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                <div class="layui-form-label "><div th:text="${lang.translate('图片')}">图片</div></div>
                <div class="layui-upload layui-input-block ">
                    <input input-type="upload" id="imageId"  name="imageId" lay-filter="imageId" style="display: none">
                    <button type="button" class="layui-btn" id="imageId-button" th:text="${lang.translate('选择图片')}">选择图片</button>
                    <div class="layui-upload-list" id="imageId-file-list"></div>
                </div>
            </div>

            <!--结束：栏次输入框循环-->
        </div>
        <!--开始：group 循环-->

        <fieldset class="layui-elem-field layui-field-title form-group-title" id="random-6263-fieldset">
            <legend>检查项</legend>
        </fieldset>

        <div class="layui-row form-row" id="defcontent">

        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 150px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消','','form.button')}" >取消</button>
    <button   class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存','','form.button')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var RADIO_POINTSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.InspectionTaskPointStatusEnum')}]];
    var VALIDATE_CONFIG={"operTime":{"date":true,"labelInForm":"操作时间","inputType":"date_input"}};
    var AUTH_PREFIX="eam_inspection_task_point_2";


</script>



<script th:src="'/business/eam/inspection_task_point/inspection_point_exec_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/inspection_task_point/inspection_point_exec.js?'+${cacheKey}"></script>

</body>
</html>