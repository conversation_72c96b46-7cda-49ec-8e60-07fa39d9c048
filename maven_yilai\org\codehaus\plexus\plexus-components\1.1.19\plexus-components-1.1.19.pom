<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.codehaus.plexus</groupId>
    <artifactId>plexus</artifactId>
    <version>3.0.1</version>
    <relativePath>../pom/pom.xml</relativePath>
  </parent>

  <artifactId>plexus-components</artifactId>
  <version>1.1.19</version>
  <packaging>pom</packaging>

  <name>Plexus Components</name>
  <url>http://plexus.codehaus.org/plexus-components</url>


  <modules>
    <module>plexus-cli</module>
    <module>plexus-digest</module>
    <module>plexus-i18n</module>
    <module>plexus-interactivity</module>
    <module>plexus-resources</module>
    <!--
    <module>plexus-swizzle</module>
    -->
    <module>plexus-velocity</module>
  </modules>

  <scm>
    <connection>scm:git:**************:sonatype/plexus-components.git</connection>
    <developerConnection>scm:git:**************:sonatype/plexus-components.git</developerConnection>
    <url>http://github.com/sonatype/plexus-components</url>
  </scm>
  <issueManagement>
    <system>JIRA</system>
    <url>http://jira.codehaus.org/browse/PLXCOMP</url>
  </issueManagement>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-container-default</artifactId>
        <version>1.0-alpha-9-stable-1</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-utils</artifactId>
        <version>3.0</version>
      </dependency>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>3.8.2</version>
        <scope>test</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-component-metadata</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>generate-metadata</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  
  <profiles>
    <profile>
      <id>parent-release</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-release-plugin</artifactId>
            <configuration>
              <arguments>-N -Pplexus-release</arguments>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
  
</project>
