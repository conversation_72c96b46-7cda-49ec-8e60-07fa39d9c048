<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.sonatype.forge</groupId>
    <artifactId>forge-parent</artifactId>
    <version>10</version>
    <relativePath />
  </parent>

  <groupId>org.sonatype.spice</groupId>
  <artifactId>spice-parent</artifactId>
  <version>17</version>
  <packaging>pom</packaging>

  <name>Sonatype Spice Components</name>

  <scm>
    <connection>scm:git:git://github.com/sonatype/oss-parents.git</connection>
    <developerConnection>scm:git:**************:sonatype/oss-parents.git</developerConnection>
    <url>https://github.com/sonatype/spice-parent</url>
  </scm>

  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <ciManagement>
    <system>Hudson</system>
    <url>https://grid.sonatype.org/ci/view/Spice/</url>
  </ciManagement>

  <issueManagement>
    <system>JIRA</system>
    <url>https://issues.sonatype.org/browse/SPICE</url>
  </issueManagement>

  <properties>
    <sisu-inject.version>2.1.1</sisu-inject.version>
    <slf4j.version>1.6.1</slf4j.version>
  </properties>

  <dependencyManagement>
    <dependencies>

      <!-- Inject Bean container -->
      <!-- Spice hosts SISU/Plexus-legacy libraries, and they expect they are embedded into apps having these provided -->
      <!-- That makes: shouldn't SISU be actually "provided" instead? -->
      <dependency>
        <groupId>org.sonatype.sisu</groupId>
        <artifactId>sisu-inject-bean</artifactId>
        <version>${sisu-inject.version}</version>
        <scope>runtime</scope>
      </dependency>
      <dependency>
        <groupId>org.sonatype.sisu</groupId>
        <artifactId>sisu-guice</artifactId>
        <version>2.9.4</version>
        <classifier>no_aop</classifier>
        <scope>runtime</scope>
      </dependency>
      <dependency>
        <groupId>javax.inject</groupId>
        <artifactId>javax.inject</artifactId>
        <version>1</version>
        <scope>compile</scope>
      </dependency>

      <!-- Plexus support (the version should be tied to SISU used ones!) -->
      <!-- Plexus needs to be compiled against, is specific (unlike JSR330-enabled SISU is) -->
      <dependency>
        <groupId>org.sonatype.sisu</groupId>
        <artifactId>sisu-inject-plexus</artifactId>
        <version>${sisu-inject.version}</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-component-annotations</artifactId>
        <version>1.5.5</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-classworlds</artifactId>
        <version>2.4</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-utils</artifactId>
        <version>2.0.5</version>
        <scope>compile</scope>
      </dependency>

      <!-- SLF4J Logging -->
      <!-- Only those needed during compile and runtime phases -->
      <!-- Spice is a library collection, so only the "simple" backend for testing and nothing more -->
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>${slf4j.version}</version>
        <type>jar</type>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>jcl-over-slf4j</artifactId>
        <version>${slf4j.version}</version>
        <type>jar</type>
        <scope>runtime</scope>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>jul-to-slf4j</artifactId>
        <version>${slf4j.version}</version>
        <type>jar</type>
        <scope>runtime</scope>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-simple</artifactId>
        <version>${slf4j.version}</version>
        <type>jar</type>
        <scope>test</scope>
      </dependency>

      <!-- Standard test -->
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>4.8.2</version>
        <scope>test</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.codehaus.plexus</groupId>
          <artifactId>plexus-component-metadata</artifactId>
          <version>1.5.5</version>
          <executions>
            <execution>
              <id>process-classes</id>
              <goals>
                <goal>generate-metadata</goal>
              </goals>
            </execution>
            <execution>
              <id>process-test-classes</id>
              <goals>
                <goal>generate-test-metadata</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>2.5</version>
        <configuration>
          <tagletArtifacts>
            <tagletArtifact>
              <groupId>org.apache.maven.plugin-tools</groupId>
              <artifactId>maven-plugin-tools-javadoc</artifactId>
              <version>2.5</version>
            </tagletArtifact>
            <tagletArtifact>
              <groupId>org.codehaus.plexus</groupId>
              <artifactId>plexus-javadoc</artifactId>
              <version>1.0</version>
            </tagletArtifact>
          </tagletArtifacts>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <version>2.1.1</version>
        <reportSets>
          <reportSet>
            <reports>
              <!-- Broken, don't know why. -->
              <report>dependencies</report>
              <report>project-team</report>
              <report>mailing-list</report>
              <report>cim</report>
              <report>issue-tracking</report>
              <report>license</report>
              <report>scm</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
    </plugins>
  </reporting>
</project>
