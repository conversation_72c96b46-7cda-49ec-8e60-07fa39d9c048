<!--
/**
 * 维修验收 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2024-03-25 10:15:01
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
    <title th:text="${lang.translate('维修验收')}">维修验收</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 申请单 , orderId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 维修单 , orderActId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 验收结果 , acceptResult ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 验收人员 , accepterId ,typeName=button, isHideInSearch=true -->
                    <!-- 实际故障 , categoryTplId ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('实际故障')}" class="search-label categoryTplId-label">实际故障</span><span class="search-colon">:</span></div>
                        <div id="categoryTplId" th:data="${'/service-eam/eam-repair-category-tpl/query-list'}" style="width:180px" extraParam="{}"></div>
                    </div>
                    <!-- 实际花费 , actualCost ,typeName=number_input, isHideInSearch=true -->
                    <!-- 图片 , pictureId ,typeName=upload, isHideInSearch=true -->
                    <!-- 制单人员 , originatorId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 修改人ID , updateBy ,typeName=text_input, isHideInSearch=true -->
                    <!-- 选择数据 , selectedCode ,typeName=text_input, isHideInSearch=true -->
                    <!-- orderBusinessCode , orderBusinessCode ,typeName=text_input, isHideInSearch=true -->
                    <!-- orderName , orderName ,typeName=text_input, isHideInSearch=true -->
                    <!-- 维修结果 , resultType ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('维修结果')}" class="search-label resultType-label">维修结果</span><span class="search-colon">:</span></div>
                        <div id="resultType" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_repair_result_type'}" style="width:180px" extraParam="{}"></div>
                    </div>
                    <!-- 验收编号 , businessCode ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('验收编号')}" class="search-label businessCode-label">验收编号</span><span class="search-colon">:</span></div>
                        <input id="businessCode" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 验收备注 , notes ,typeName=text_area, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('验收备注')}" class="search-label notes-label">验收备注</span><span class="search-colon">:</span></div>
                        <input id="notes" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 完成时间 , finishTime ,typeName=date_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('完成时间')}" class="search-label finishTime-label">完成时间</span><span class="search-colon">:</span></div>
                            <input type="text" id="finishTime-begin" style="width: 180px" lay-verify="date" th:placeholder="${lang.translate('开始日期')}" autocomplete="off" class="layui-input search-input search-date-input"  readonly >
                            <span class="search-dash">-</span>
                            <input type="text" id="finishTime-end"  style="width: 180px"  lay-verify="date" th:placeholder="${lang.translate('结束日期')}" autocomplete="off" class="layui-input search-input search-date-input" readonly>
                    </div>


                </div>
            </div>


            <!-- 按钮区域 -->
            <div id="search-area" class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索','','cmp:table.search')}">搜索</span></button>
            </div>
        </div>

        <div id="table-area" style="margin-top: 84px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
        <button th:if="${perm.checkAuth('eam_repair_order_acceptance:create')}" id="add-button" class="layui-btn icon-btn layui-btn-sm create-new-button " lay-event="create"><i class="layui-icon">&#xe654;</i><span th:text="${lang.translate('新建','','cmp:table.button')}">新建</span></button>
    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">

    <button th:if="${perm.checkAuth('eam_repair_order_acceptance:view-form')}" class="layui-btn layui-btn-primary layui-btn-xs ops-view-button " lay-event="view"  data-id="{{d.id}}"> <span th:text="${lang.translate('查看','','cmp:table.ops')}">查看</span></button>
    <button th:if="${perm.checkAnyAuth('eam_repair_order_acceptance:update','eam_repair_order_acceptance:save')}" class="layui-btn layui-btn-primary layui-btn-xs ops-edit-button " lay-event="edit"data-id="{{d.id}}"><span th:text="${lang.translate('修改','','cmp:table.ops')}">修改</span></button>



    <button th:if="${perm.checkAuth('eam_repair_order_acceptance:acceptance')}"class="layui-btn layui-btn-xs  acceptance-button " lay-event="acceptance" data-id="{{d.id}}"><span th:text="${lang.translate('确定验收','','cmp:table.ops')}">确定验收</span></button>

</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
    var RADIO_ACCEPTRESULT_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.RepairOrderActAcceptStatusEnum')}]];
    var AUTH_PREFIX="eam_repair_order_acceptance";


</script>

<script th:src="'/business/eam/repair_order_acceptance/repair_order_acceptance_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/repair_order_acceptance/repair_order_acceptance_list.js?'+${cacheKey}"></script>

</body>
</html>