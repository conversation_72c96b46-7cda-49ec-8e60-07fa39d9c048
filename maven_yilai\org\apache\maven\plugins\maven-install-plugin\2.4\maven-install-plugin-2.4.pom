<?xml version='1.0' encoding='UTF-8'?>

<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <artifactId>maven-plugins</artifactId>
    <groupId>org.apache.maven.plugins</groupId>
    <version>23</version>
    <relativePath>../maven-plugins/pom.xml</relativePath>
  </parent>

  <artifactId>maven-install-plugin</artifactId>
  <version>2.4</version>
  <packaging>maven-plugin</packaging>

  <name>Maven Install Plugin</name>
  <description>Copies the project artifacts to the user's local repository.</description>
  <inceptionYear>2004</inceptionYear>

  <prerequisites>
    <maven>${mavenVersion}</maven>
  </prerequisites>

  <scm>
    <connection>scm:svn:http://svn.apache.org/repos/asf/maven/plugins/tags/maven-install-plugin-2.4</connection>
    <developerConnection>scm:svn:https://svn.apache.org/repos/asf/maven/plugins/tags/maven-install-plugin-2.4</developerConnection>
    <url>http://svn.apache.org/viewvc/maven/plugins/tags/maven-install-plugin-2.4</url>
  </scm>
  <issueManagement>
    <system>jira</system>
    <url>http://jira.codehaus.org/browse/MINSTALL</url>
  </issueManagement>

  <properties>
    <mavenVersion>2.0.6</mavenVersion>
    <mavenPluginPluginVersion>3.1</mavenPluginPluginVersion>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-plugin-api</artifactId>
      <version>${mavenVersion}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-project</artifactId>
      <version>${mavenVersion}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-model</artifactId>
      <version>${mavenVersion}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-artifact-manager</artifactId>
      <version>${mavenVersion}</version>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-artifact</artifactId>
      <version>${mavenVersion}</version>
    </dependency>

    <!-- dependencies to annotations -->
    <dependency>
      <groupId>org.apache.maven.plugin-tools</groupId>
      <artifactId>maven-plugin-annotations</artifactId>
      <version>${mavenPluginPluginVersion}</version>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>org.apache.maven.plugin-testing</groupId>
      <artifactId>maven-plugin-testing-harness</artifactId>
      <version>1.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-utils</artifactId>
      <version>3.0.5</version>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-digest</artifactId>
      <version>1.0</version>
    </dependency>
  </dependencies>

  <contributors>
    <contributor>
      <name>Ludwig Magnusson</name>
    </contributor>
  </contributors>

  <build>

    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-plugin-plugin</artifactId>
          <version>${mavenPluginPluginVersion}</version>
          <configuration>
            <skipErrorNoDescriptorsFound>true</skipErrorNoDescriptorsFound>
          </configuration>
          <executions>
            <execution>
              <id>mojo-descriptor</id>
              <phase>process-classes</phase>
              <goals>
                <goal>descriptor</goal>
              </goals>
            </execution>
            <execution>
              <id>help-goal</id>
              <goals>
                <goal>helpmojo</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-plugin-plugin</artifactId>
        <version>${mavenPluginPluginVersion}</version>
      </plugin>
    </plugins>
  </reporting>

  <profiles>
    <profile>
      <id>run-its</id>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-invoker-plugin</artifactId>
              <version>1.7</version>
              <configuration>
                <debug>true</debug>
                <projectsDirectory>src/it</projectsDirectory>
                <cloneProjectsTo>${project.build.directory}/it</cloneProjectsTo>
                <pomIncludes>
                  <pomInclude>*/pom.xml</pomInclude>
                  <pomInclude>*/non-default-pom.xml</pomInclude>
                </pomIncludes>
                <preBuildHookScript>setup</preBuildHookScript>
                <postBuildHookScript>verify</postBuildHookScript>
                <localRepositoryPath>${project.build.directory}/local-repo</localRepositoryPath>
                <settingsFile>src/it/settings.xml</settingsFile>
                <addTestClassPath>true</addTestClassPath>
                <goals>
                  <goal>clean</goal>
                  <goal>install</goal>
                </goals>
              </configuration>
            </plugin>
          </plugins>
        </pluginManagement>
      </build>
    </profile>
  </profiles>
</project>
