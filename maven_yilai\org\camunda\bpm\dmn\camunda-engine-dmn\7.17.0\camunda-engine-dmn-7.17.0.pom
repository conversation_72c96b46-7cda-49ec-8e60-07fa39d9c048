<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.camunda.bpm.dmn</groupId>
    <artifactId>camunda-engine-dmn-root</artifactId>
    <version>7.17.0</version>
  </parent>

  <artifactId>camunda-engine-dmn</artifactId>
  <name>camunda DMN - engine</name>
  <properties>
    <camunda.artifact>org.camunda.bpm.dmn.engine</camunda.artifact>
    <camunda.osgi.import.additional>
      org.junit.*;resolution:=optional
    </camunda.osgi.import.additional>
  </properties>
  <dependencies>
    <dependency>
      <groupId>org.camunda.commons</groupId>
      <artifactId>camunda-commons-utils</artifactId>
    </dependency>

    <dependency>
      <groupId>org.camunda.commons</groupId>
      <artifactId>camunda-commons-typed-values</artifactId>
    </dependency>

    <dependency>
      <groupId>org.camunda.bpm.model</groupId>
      <artifactId>camunda-dmn-model</artifactId>
    </dependency>

    <dependency>
      <groupId>org.camunda.bpm.dmn</groupId>
      <artifactId>camunda-engine-feel-api</artifactId>
    </dependency>

    <dependency>
      <groupId>org.camunda.bpm.dmn</groupId>
      <artifactId>camunda-engine-feel-juel</artifactId>
    </dependency>

    <dependency>
      <groupId>org.camunda.bpm.dmn</groupId>
      <artifactId>camunda-engine-feel-scala</artifactId>
    </dependency>

    <dependency>
      <groupId>org.camunda.feel</groupId>
      <artifactId>feel-engine</artifactId>
      <classifier>scala-shaded</classifier>
    </dependency>

    <dependency>
      <groupId>de.odysseus.juel</groupId>
      <artifactId>juel-api</artifactId>
    </dependency>

    <dependency>
      <groupId>de.odysseus.juel</groupId>
      <artifactId>juel-spi</artifactId>
    </dependency>

    <dependency>
      <groupId>de.odysseus.juel</groupId>
      <artifactId>juel-impl</artifactId>
    </dependency>

    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.codehaus.groovy</groupId>
      <artifactId>groovy-all</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.graalvm.js</groupId>
      <artifactId>js</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.graalvm.js</groupId>
      <artifactId>js-scriptengine</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>joda-time</groupId>
      <artifactId>joda-time</artifactId>
      <scope>test</scope>
    </dependency>

  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <executions>
          <execution>
            <id>bundle-manifest</id>
            <phase>process-classes</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <!-- check for api differences between latest minor release -->
    <profile>
      <id>check-api-compatibility</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>clirr-maven-plugin</artifactId>
            <configuration>
              <comparisonVersion>${camunda.version.old}</comparisonVersion>
              <logResults>true</logResults>
              <excludes>
                <exclude>org/camunda/bpm/dmn/engine/impl/**</exclude>
              </excludes>
            </configuration>
            <executions>
              <execution>
                <id>all</id>
                <phase>verify</phase>
                <goals>
                  <goal>check-no-fork</goal>
                </goals>
                <configuration>
                  <textOutputFile>${project.build.directory}/clirr-all.txt</textOutputFile>
                  <failOnWarning>false</failOnWarning>
                  <failOnError>false</failOnError>
                </configuration>
              </execution>
              <execution>
                <id>restrictive</id>
                <phase>verify</phase>
                <goals>
                  <goal>check-no-fork</goal>
                </goals>
                <configuration>
                  <textOutputFile>${project.build.directory}/clirr-restrictive.txt</textOutputFile>
                  <failOnWarning>true</failOnWarning>
                  <ignoredDifferencesFile>../.clirr-jenkins-ignore.xml</ignoredDifferencesFile>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

</project>
