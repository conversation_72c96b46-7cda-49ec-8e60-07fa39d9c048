<!--
/**
 * 盘点明细 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2022-01-05 11:04:56
 */
 -->
<!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
	<title th:text="${lang.translate('盘点明细')}">盘点明细</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="display:none">

        <input name="id" id="id"  type="hidden"/>

         <!--开始：group 循环-->



        <div class="layui-row form-row" id="random-6910-content">

             <!--开始：group 循环-->
            <div class="layui-col-xs12 form-column" style="padding-top: 15px">

                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('盘点状态')}">盘点状态</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <div id="status" input-type="select" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.AssetInventoryDetailStatusEnum')}" extraParam="{}"></div>
                    </div>
                </div>

                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('照片')}">照片</div></div>
                    <div class="layui-upload layui-input-block layui-input-block-c1">
                        <input input-type="upload" id="pictureId"  name="pictureId" lay-filter="pictureId" style="display: none">
                        <button type="button" class="layui-btn" id="pictureId-button" th:text="${lang.translate('选择图片')}">选择图片</button>
                        <div class="layui-upload-list" id="pictureId-file-list"></div>
                    </div>
                </div>



                <div class="layui-form-item" >
                        <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('备注')}">备注</div></div>
                        <div class="layui-input-block layui-input-block-c1">
<!--                            <textarea style="height: 180px" lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('备注') }" type="text" class="layui-input"  /></textarea>-->
<!--                         -->
                            <textarea lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('备注') }" class="layui-textarea" style="height: 120px" ></textarea>
                        </div>
                    </div>

                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 250px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消')}" >取消</button>
    <button  class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>

<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetInventoryDetailStatusEnum')}]];

    var VALIDATE_CONFIG={"status":{"labelInForm":"盘点状态","inputType":"select_box","required":true}};
    var AUTH_PREFIX="eam_inventory_asset";





</script>



<script th:src="'/business/eam/inventory_asset/inventory_asset_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/inventory_asset/inventory_asset_form.js?'+${cacheKey}"></script>

</body>
</html>
