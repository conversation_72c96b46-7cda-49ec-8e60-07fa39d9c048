<!--
/**
 * 盘点明细 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2022-01-05 11:04:50
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <title th:text="${lang.translate('盘点明细')}">盘点明细</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 盘点 , inventoryId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 状态 , status ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('状态')}" class="search-label status-label">状态</span><span class="search-colon">:</span></div>
                        <div id="status" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.AssetInventoryDetailStatusEnum')}" style="width:140px" extraParam="{}"></div>
                    </div>
                    <!-- 资产 , assetId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 员工 , operEmplId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 操作时间 , operDate ,typeName=date_input, isHideInSearch=true -->
                    <!-- 备注 , notes ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('备注')}" class="search-label notes-label">备注</span><span class="search-colon">:</span></div>
                        <input id="notes" class="layui-input search-input" style="width: 140px" type="text" />
                    </div>


                </div>
            </div>


            <!-- 按钮区域 -->
            <div class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索')}">搜索</span></button>
            </div>
        </div>

        <div style="margin-top: 42px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>

<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
        <button th:if="${perm.checkAuth('eam_inventory_asset:rfid-data')}" id="rfid-data" class="layui-btn icon-btn layui-btn-sm rfid-data-button " lay-event="rfid-data"><span th:text="${lang.translate('RFID识别')}">RFID识别</span></button>
        <button th:if="${perm.checkAuth('eam_inventory_asset:asset-plus')}" id="plus-button" class="layui-btn icon-btn layui-btn-sm create-new-button " lay-event="asset-plus"><span th:text="${lang.translate('新增盘盈')}">新增盘盈</span></button>
        <button th:if="${perm.checkAuth('eam_inventory_asset:asset-add')}" id="add-button" class="layui-btn icon-btn layui-btn-sm add-new-button " lay-event="asset-add"><span th:text="${lang.translate('新增资产')}">新增资产</span></button>
        <button th:if="${perm.checkAuth('eam_inventory_asset:download-asset')}" id="download-bill" class="layui-btn icon-btn layui-btn-sm download-asset-button " lay-event="download-asset"><span th:text="${lang.translate('下载资产')}">下载资产</span></button>
        <button th:if="${perm.checkAuth('eam_inventory_asset:delete-by-ids')}" id="delete-button" class="layui-btn icon-btn layui-btn-danger layui-btn-sm batch-delete-button " lay-event="batch-del"><i class="layui-icon">&#xe67e;</i><span th:text="${lang.translate('删除')}">删除</span></button>

    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">
    <button class="layui-btn layui-btn-primary layui-btn-xs ops-view-button " lay-event="detail" th:text="${lang.translate('资产详情')}" data-id="{{d.id}}">资产详情</button>
    <button th:if="${perm.checkAuth('eam_inventory_asset:asset-modify')}"  class="layui-btn layui-btn-primary layui-btn-xs ops-view-button " lay-event="modify-asset" th:text="${lang.translate('修改资产')}" data-id="{{d.id}}">修改资产</button>
    <button class="layui-btn layui-btn-primary layui-btn-xs ops-edit-button " lay-event="edit" th:text="${lang.translate('执行盘点')}" data-id="{{d.id}}">执行盘点</button>
    <button th:if="${perm.checkAuth('eam_inventory_asset:delete')}" class="layui-btn layui-btn-xs layui-btn-danger ops-delete-button " lay-event="del" th:text="${lang.translate('删除')}" data-id="{{d.id}}">删除</button>
<!--    <button th:if="${perm.checkAuth('eam_inventory_asset:view-form')}" class="layui-btn layui-btn-primary layui-btn-xs ops-view-button " lay-event="view" th:text="${lang.translate('查看')}" data-id="{{d.id}}">查看</button>-->
</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetInventoryDetailStatusEnum')}]];
    var AUTH_PREFIX="eam_inventory_asset";
    var SELECT_ASSETSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetStatusEnum')}]];
    var INVENTORY_ID = [[${inventoryId}]] ;
    var INVENTORY = [[${inventory}]] ;

    var INVENTORY_MODE = [[${inventoryMode}]] ;

</script>

<script th:src="'/business/eam/inventory_asset/inventory_asset_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/inventory_asset/inventory_asset_list.js?'+${cacheKey}"></script>

</body>
</html>
