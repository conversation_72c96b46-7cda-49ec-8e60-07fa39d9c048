<!--
/**
 * 保养项目 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2023-07-10 16:07:47
 */
 -->
<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
    <title th:text="${lang.translate('保养项目')}">保养项目</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 项目 , projectId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 任务 , taskId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 状态 , status ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('状态')}" class="search-label status-label">状态</span><span class="search-colon">:</span></div>
                        <div id="status" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.MaintainTaskProjectStatusEnum')}" style="width:150px" extraParam="{}"></div>
                    </div>
                    <!-- 保养结果 , content ,typeName=text_input, isHideInSearch=true -->
                    <!-- 标准工时 , projectBaseCost ,typeName=number_input, isHideInSearch=true -->
                    <!-- 保养手册 , projectAttachId ,typeName=upload, isHideInSearch=true -->
                    <!-- 项目备注 , projectNotes ,typeName=text_input, isHideInSearch=true -->
                    <!-- 标准工时 , baseCost ,typeName=number_input, isHideInSearch=true -->
                    <!-- 开始时间 , startTime ,typeName=date_input, isHideInSearch=true -->
                    <!-- 结束时间 , endTime ,typeName=date_input, isHideInSearch=true -->
                    <!-- 选择 , selectedCode ,typeName=text_input, isHideInSearch=true -->
                    <!-- 保养类型 , projectMaintainType ,typeName=select_box, isHideInSearch=true -->
                    <!-- 项目编号 , projectCode ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('项目编号')}" class="search-label projectCode-label">项目编号</span><span class="search-colon">:</span></div>
                        <input id="projectCode" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>
                    <!-- 项目名称 , projectName ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('项目名称')}" class="search-label projectName-label">项目名称</span><span class="search-colon">:</span></div>
                        <input id="projectName" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>


                </div>
            </div>


            <!-- 按钮区域 -->
            <div id="search-area" class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索','','cmp:table.search')}">搜索</span></button>
            </div>
        </div>

        <div id="table-area" style="margin-top: 42px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">

    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">
    <button class="layui-btn layui-btn-primary layui-btn-xs ops-edit-button " lay-event="edit"data-id="{{d.id}}"><span th:text="${lang.translate('更新','','cmp:table.ops')}">更新</span></button>
</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.MaintainTaskProjectStatusEnum')}]];
    var AUTH_PREFIX="eam_maintain_task_project3";

    var SELECTED_CODE = [[${selectedCode}]] ;
    var OWNER_ID = [[${ownerId}]] ;
    var OWNER_TYPE = [[${ownerType}]] ;
    var PAGE_TYPE = [[${pageType}]] ;

</script>

<script th:src="'/business/eam/maintain_task_project/maintain_task_project_exec_e.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/maintain_task_project/maintain_task_project_exec_l.js?'+${cacheKey}"></script>

</body>
</html>