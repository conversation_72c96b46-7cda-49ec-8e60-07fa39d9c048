<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.foxnicweb.web</groupId>
    <artifactId>parent</artifactId>
    <version>1.8.0.RELEASE</version>
    <relativePath>../../../foxnic-web/pom.xml</relativePath>
  </parent>
  <groupId>com.foxnicweb.web</groupId>
  <artifactId>service-camunda</artifactId>
  <version>1.8.0.RELEASE</version>
  <name>service-camunda</name>
  <description>System Camunda</description>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <properties>
    <skipTests>true</skipTests>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <java.version>1.8</java.version>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
  </properties>
  <dependencies>
    <dependency>
      <groupId>com.foxnicweb</groupId>
      <artifactId>cangariza</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.foxnicweb.web</groupId>
      <artifactId>framework-boot</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.foxnicweb.web</groupId>
      <artifactId>domain</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.foxnicweb.web</groupId>
      <artifactId>proxy</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>org.camunda.bpm.springboot</groupId>
      <artifactId>camunda-bpm-spring-boot-starter-rest</artifactId>
      <version>${camunda.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-tomcat</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.camunda.bpm</groupId>
      <artifactId>camunda-engine-rest-openapi</artifactId>
      <version>${camunda.version}</version>
    </dependency>
    <dependency>
      <groupId>org.camunda.bpm.springboot</groupId>
      <artifactId>camunda-bpm-spring-boot-starter-webapp</artifactId>
      <version>${camunda.version}</version>
    </dependency>
    <dependency>
      <groupId>org.camunda.bpm</groupId>
      <artifactId>camunda-engine-plugin-spin</artifactId>
      <version>${camunda.version}</version>
    </dependency>
    <dependency>
      <groupId>org.camunda.spin</groupId>
      <artifactId>camunda-spin-dataformat-json-jackson</artifactId>
      <version>1.14.0</version>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.datatype</groupId>
      <artifactId>jackson-datatype-jdk8</artifactId>
    </dependency>
    <dependency>
      <groupId>org.quartz-scheduler</groupId>
      <artifactId>quartz</artifactId>
      <exclusions>
        <exclusion>
          <groupId>com.mchange</groupId>
          <artifactId>c3p0</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>
  <build>
    <defaultGoal>compile</defaultGoal>
    <resources>
      <resource>
        <directory>${basedir}/src/main/java</directory>
        <includes>
          <include>**/*.tql</include>
        </includes>
        <excludes>
          <exclude>**/*.java</exclude>
        </excludes>
      </resource>
      <resource>
        <directory>${basedir}/src/main/resources</directory>
        <includes>
          <include>**/**</include>
        </includes>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <artifactId>maven-source-plugin</artifactId>
        <version>2.2.1</version>
        <executions>
          <execution>
            <id>attach-sources</id>
            <goals>
              <goal>jar-no-fork</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <skipSource>true</skipSource>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
