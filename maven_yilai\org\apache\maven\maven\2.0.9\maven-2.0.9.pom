<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.maven</groupId>
    <artifactId>maven-parent</artifactId>
    <version>8</version>
    <relativePath>../pom/maven/pom.xml</relativePath>
  </parent>

  <artifactId>maven</artifactId>
  <version>2.0.9</version>
  <packaging>pom</packaging>

  <name>Maven</name>
  <description>Maven is a project development management and comprehension tool. Based on the concept of a project object model: builds, dependency management, documentation creation, site publication, and distribution publication are all controlled from the declarative file. Maven can be extended by plugins to utilise a number of other development tools for reporting or the build process.</description>
  <url>http://maven.apache.org</url>
  <inceptionYear>2001</inceptionYear>

  <issueManagement>
    <system>jira</system>
    <url>http://jira.codehaus.org/browse/MNG</url>
  </issueManagement>

  <mailingLists>
    <mailingList>
      <name>Maven User List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-users</archive>
      <otherArchives>
        <otherArchive>http://www.mail-archive.com/<EMAIL>/</otherArchive>
        <otherArchive>http://www.nabble.com/Maven---Users-f178.html</otherArchive>
        <otherArchive>http://maven.users.markmail.org/</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Maven Developer List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-dev</archive>
      <otherArchives>
        <otherArchive>http://www.mail-archive.com/<EMAIL>/</otherArchive>
        <otherArchive>http://www.nabble.com/Maven-Developers-f179.html</otherArchive>
        <otherArchive>http://maven.dev.markmail.org/</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Maven Issues List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-issues/</archive>
      <otherArchives>
        <otherArchive>http://www.mail-archive.com/<EMAIL></otherArchive>
        <otherArchive>http://www.nabble.com/Maven---Issues-f15573.html</otherArchive>
        <otherArchive>http://maven.issues.markmail.org/</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Maven Commits List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-commits</archive>
      <otherArchives>
        <otherArchive>http://www.mail-archive.com/<EMAIL></otherArchive>
        <otherArchive>http://www.nabble.com/Maven---Commits-f15575.html</otherArchive>
        <otherArchive>http://maven.commits.markmail.org/</otherArchive>
      </otherArchives>
    </mailingList>

    <!-- duplication from parent pom - temporary until they inherit properly -->
    <mailingList>
      <name>Maven Announcements List</name>
      <post><EMAIL></post>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-announce/</archive>
      <otherArchives>
        <otherArchive>http://www.mail-archive.com/<EMAIL></otherArchive>
        <otherArchive>http://www.nabble.com/Maven-Announcements-f15617.html</otherArchive>
        <otherArchive>http://maven.announce.markmail.org/</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Maven Notifications List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-notifications/</archive>
      <otherArchives>
        <otherArchive>http://www.mail-archive.com/<EMAIL></otherArchive>
        <otherArchive>http://www.nabble.com/Maven---Notifications-f15574.html</otherArchive>
        <otherArchive>http://maven.notifications.markmail.org/</otherArchive>
      </otherArchives>
    </mailingList>
  </mailingLists>

  <scm>
    <connection>scm:svn:http://svn.apache.org/repos/asf/maven/components/tags/maven-2.0.9</connection>
    <developerConnection>scm:svn:https://svn.apache.org/repos/asf/maven/components/tags/maven-2.0.9</developerConnection>
    <url>https://svn.apache.org/repos/asf/maven/components/tags/maven-2.0.9</url>
  </scm>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>2.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>2.0.2</version>
        </plugin>
        <plugin>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>2.4</version>
        </plugin>
        <plugin>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>2.2-beta-1</version>
        </plugin>
        <plugin>
          <artifactId>maven-shade-plugin</artifactId>
          <version>1.0</version>
        </plugin>
        <plugin>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.3</version>
        </plugin>
        <plugin>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>2.3</version>
        </plugin>
        <plugin>
          <artifactId>maven-install-plugin</artifactId>
          <version>2.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-site-plugin</artifactId>
          <version>2.0-beta-5</version>
        </plugin>
        <plugin>
          <artifactId>maven-resources-plugin</artifactId>
          <version>2.2</version>
        </plugin>
        <plugin>
          <artifactId>maven-remote-resources-plugin</artifactId>
          <version>1.0-beta-2</version>
        </plugin>
        <plugin>
          <artifactId>maven-clean-plugin</artifactId>
          <version>2.1.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-release-plugin</artifactId>
          <version>2.0-beta-7</version>
          <configuration>
            <tagBase>https://svn.apache.org/repos/asf/maven/components/tags</tagBase>
            <autoVersionSubmodules>true</autoVersionSubmodules>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.modello</groupId>
          <artifactId>modello-maven-plugin</artifactId>
          <version>1.0-alpha-13</version>
          <executions>
            <execution>
              <id>site-docs</id>
              <phase>pre-site</phase>
              <goals>
                <goal>xdoc</goal>
                <goal>xsd</goal>
              </goals>
            </execution>
            <execution>
              <id>standard</id>
              <goals>
                <goal>java</goal>
                <goal>xpp3-reader</goal>
                <goal>xpp3-writer</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

  <modules>
    <module>maven-artifact</module>
    <module>maven-artifact-manager</module>
    <module>maven-artifact-test</module>
    <module>maven-core</module>
    <module>maven-error-diagnostics</module>
    <module>maven-model</module>
    <module>maven-monitor</module>
    <module>maven-plugin-api</module>
    <module>maven-plugin-descriptor</module>
    <module>maven-plugin-parameter-documenter</module>
    <module>maven-plugin-registry</module>
    <module>maven-profile</module>
    <module>maven-project</module>
    <module>maven-reporting</module>
    <module>maven-repository-metadata</module>
    <module>maven-script</module>
    <module>maven-settings</module>
    <module>maven-toolchain</module>
    <module>apache-maven</module>
  </modules>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>3.8.1</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <properties>
    <mavenVersion>2.0.9</mavenVersion>
    <!-- Can't use. bootstrap-mini isn't smart enough: <wagonVersion>1.0-beta-2</wagonVersion> -->
  </properties>

  <dependencyManagement>
    <dependencies>
      <!-- Maven -->
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-plugin-descriptor</artifactId>
        <version>${mavenVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-error-diagnostics</artifactId>
        <version>${mavenVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-model</artifactId>
        <version>${mavenVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-project</artifactId>
        <version>${mavenVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.reporting</groupId>
        <artifactId>maven-reporting-api</artifactId>
        <version>${mavenVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-repository-metadata</artifactId>
        <version>${mavenVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-artifact</artifactId>
        <version>${mavenVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-artifact-manager</artifactId>
        <version>${mavenVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-artifact-test</artifactId>
        <version>${mavenVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-settings</artifactId>
        <version>${mavenVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-core</artifactId>
        <version>${mavenVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-toolchain</artifactId>
        <version>${mavenVersion}</version>
      </dependency>
      
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-plugin-parameter-documenter</artifactId>
        <version>${mavenVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-profile</artifactId>
        <version>${mavenVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-plugin-registry</artifactId>
        <version>${mavenVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-plugin-api</artifactId>
        <version>${mavenVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-monitor</artifactId>
        <version>${mavenVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-toolchain</artifactId>
        <version>${mavenVersion}</version>
      </dependency>
      <!-- Plexus -->
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-container-default</artifactId>
        <version>1.0-alpha-9-stable-1</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-utils</artifactId>
        <version>1.5.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.wagon</groupId>
        <artifactId>wagon-provider-api</artifactId>
        <version>1.0-beta-2</version> <!-- Can't use. bootstrap-mini isn't smart enough: <version>${wagonVersion}</version> -->
      </dependency>
      <dependency>
        <groupId>org.apache.maven.wagon</groupId>
        <artifactId>wagon-ssh</artifactId>
        <version>1.0-beta-2</version> <!-- Can't use. bootstrap-mini isn't smart enough: <version>${wagonVersion}</version> -->
      </dependency>
      <dependency>
        <groupId>org.apache.maven.wagon</groupId>
        <artifactId>wagon-ssh-external</artifactId>
        <version>1.0-beta-2</version> <!-- Can't use. bootstrap-mini isn't smart enough: <version>${wagonVersion}</version> -->
      </dependency>
      <dependency>
        <groupId>org.apache.maven.wagon</groupId>
        <artifactId>wagon-file</artifactId>
        <version>1.0-beta-2</version> <!-- Can't use. bootstrap-mini isn't smart enough: <version>${wagonVersion}</version> -->
      </dependency>
      <dependency>
        <groupId>org.apache.maven.wagon</groupId>
        <artifactId>wagon-webdav</artifactId>
        <version>1.0-beta-2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.wagon</groupId>
        <artifactId>wagon-http-lightweight</artifactId>
        <version>1.0-beta-2</version> <!-- Can't use. bootstrap-mini isn't smart enough: <version>${wagonVersion}</version> -->
      </dependency>
      <dependency>
        <groupId>easymock</groupId>
        <artifactId>easymock</artifactId>
        <version>1.2_Java1.3</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>classworlds</groupId>
        <artifactId>classworlds</artifactId>
        <version>1.1</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <distributionManagement>
    <site>
      <id>apache.website</id>
      <url>scp://people.apache.org/www/maven.apache.org/ref/${project.version}/</url>
    </site>
  </distributionManagement>

  <profiles>
    <profile>
      <id>release</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-assembly-plugin</artifactId>
            <inherited>false</inherited>
            <configuration>
              <descriptors>
                <descriptor>src/main/assembly/src.xml</descriptor>
              </descriptors>
              <tarLongFileMode>gnu</tarLongFileMode>
              <finalName>maven-${project.version}-src</finalName>
            </configuration>
            <executions>
              <execution>
                <id>make-assembly</id>
                <phase>package</phase>
                <goals>
                  <goal>single</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>run-its</id>
      <modules>
        <module>maven-core-it-runner</module>
      </modules>
    </profile>

    <profile>
      <!-- To generate aggregate reports, calling twice mvn site -Preporting,  mvn site -Preporting-aggregate -->
      <id>reporting-aggregate</id>
      <reporting>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-jxr-plugin</artifactId>
            <configuration>
              <aggregate>true</aggregate>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <configuration>
              <links>
                <link>http://java.sun.com/j2se/1.4.2/docs/api</link>
                <link>http://java.sun.com/j2ee/1.4/docs/api</link>
                <link>http://java.sun.com/j2se/1.5.0/docs/api</link>
                <link>http://commons.apache.org/collections/apidocs-COLLECTIONS_3_0/</link>
                <link>http://commons.apache.org/dbcp/apidocs/</link>
                <link>http://commons.apache.org/fileupload/apidocs/</link>
                <link>http://commons.apache.org/httpclient/apidocs/</link>
                <link>http://commons.apache.org/logging/apidocs/</link>
                <link>http://commons.apache.org/pool/apidocs/</link>
                <link>http://junit.sourceforge.net/javadoc/</link>
                <link>http://logging.apache.org/log4j/1.2/apidocs/</link>
                <link>http://jakarta.apache.org/regexp/apidocs/</link>
                <link>http://velocity.apache.org/engine/releases/velocity-1.5/apidocs/</link>
              </links>
              <aggregate>true</aggregate>
            </configuration>
          </plugin>
        </plugins>
      </reporting>
    </profile>
  </profiles>
</project>
