<!--
/**
 * 库存物品 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2022-07-30 08:42:46
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <title th:text="${lang.translate('库存物品')}">库存物品</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 所属 , ownerId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 所属 , ownerTmpId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 库存所属 , ownerCode ,typeName=text_input, isHideInSearch=true -->
                    <!-- 所属类型 , ownerType ,typeName=text_input, isHideInSearch=true -->
                    <!-- 业务编号 , businessCode ,typeName=text_input, isHideInSearch=true -->
                    <!-- 办理状态 , status ,typeName=select_box, isHideInSearch=true -->
                    <!-- 分类 , categoryId ,typeName=select_box, isHideInSearch=true -->
                    <!-- 厂商 , manufacturerId ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('厂商')}" class="search-label manufacturerId-label">厂商</span><span class="search-colon">:</span></div>
                        <div id="manufacturerId" th:data="${'/service-eam/eam-manufacturer/query-list'}" style="width:150px" extraParam="{}"></div>
                    </div>
                    <!-- 默认单价 , unitPrice ,typeName=number_input, isHideInSearch=true -->
                    <!-- 计量单位 , unit ,typeName=text_input, isHideInSearch=true -->
                    <!-- 安全库存下限 , stockMin ,typeName=number_input, isHideInSearch=true -->
                    <!-- 安全库存上限 , stockMax ,typeName=number_input, isHideInSearch=true -->
                    <!-- 安全库存 , stockSecurity ,typeName=number_input, isHideInSearch=true -->
                    <!-- 物品图片 , pictureId ,typeName=upload, isHideInSearch=true -->
                    <!-- 备注 , notes ,typeName=text_area, isHideInSearch=true -->
                    <!-- 批次号 , batchCode ,typeName=text_input, isHideInSearch=true -->
                    <!-- 所属公司 , ownCompanyId ,typeName=button, isHideInSearch=true -->
                    <!-- 使用组织 , useOrgId ,typeName=button, isHideInSearch=true -->
                    <!-- 供应商 , supplierName ,typeName=text_input, isHideInSearch=true -->
                    <!-- 来源 , sourceId ,typeName=select_box, isHideInSearch=true -->
                    <!-- 物品 , goodsId ,typeName=select_box, isHideInSearch=true -->
                    <!-- 入库数量 , stockInNumber ,typeName=number_input, isHideInSearch=true -->
                    <!-- 当前数量 , stockCurNumber ,typeName=number_input, isHideInSearch=true -->
                    <!-- 总金额 , amount ,typeName=number_input, isHideInSearch=true -->
                    <!-- 管理人 , managerId ,typeName=button, isHideInSearch=true -->
                    <!-- 库存数据 , realStockId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 制单人 , originatorId ,typeName=button, isHideInSearch=true -->
                    <!-- 选择 , selectedCode ,typeName=text_input, isHideInSearch=true -->
                    <!-- 物品型号 , goodsModel ,typeName=select_box, isHideInSearch=true -->
                    <!-- goodsName , goodsName ,typeName=text_input, isHideInSearch=true -->
                    <!-- goodsCategoryName , goodsCategoryName ,typeName=text_input, isHideInSearch=true -->
                    <!-- 计量单位 , goodsUnit ,typeName=select_box, isHideInSearch=true -->
                    <!-- 物品编码 , goodsCode ,typeName=select_box, isHideInSearch=true -->
                    <!-- 物品条码 , goodsBarCode ,typeName=select_box, isHideInSearch=true -->
                    <!-- goodsStockMax , goodsStockMax ,typeName=text_input, isHideInSearch=true -->
                    <!-- goodsStockMin , goodsStockMin ,typeName=text_input, isHideInSearch=true -->
                    <!-- goodsStockSecurity , goodsStockSecurity ,typeName=text_input, isHideInSearch=true -->
                    <!-- 品牌 , brandId ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('品牌')}" class="search-label brandId-label">品牌</span><span class="search-colon">:</span></div>
                        <div id="brandId" th:data="${'/service-eam/eam-brand/query-paged-list'}" style="width:150px" extraParam="{}"></div>
                    </div>
                    <!-- 规格型号 , model ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('规格型号')}" class="search-label model-label">规格型号</span><span class="search-colon">:</span></div>
                        <input id="model" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>
                    <!-- 物品名称 , name ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('物品名称')}" class="search-label name-label">物品名称</span><span class="search-colon">:</span></div>
                        <input id="name" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 状态 , goodsStatus ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('状态')}" class="search-label goodsStatus-label">状态</span><span class="search-colon">:</span></div>
                        <div id="goodsStatus" th:data="${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}" style="width:150px" extraParam="{}"></div>
                    </div>
                    <!-- 仓库 , warehouseId ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('资产仓库')}" class="search-label warehouseId-label">资产仓库</span><span class="search-colon">:</span></div>
                        <div id="warehouseId" th:data="${'/service-eam/eam-warehouse/query-paged-list'}" style="width:150px" extraParam="{}"></div>
                    </div>
                    <!-- 物品编码 , code ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('物品编码')}" class="search-label code-label">物品编码</span><span class="search-colon">:</span></div>
                        <input id="code" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>
                    <!-- 物品条码 , barCode ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('物品条码')}" class="search-label barCode-label">物品条码</span><span class="search-colon">:</span></div>
                        <input id="barCode" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 入库时间 , storageDate ,typeName=date_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('入库时间')}" class="search-label storageDate-label">入库时间</span><span class="search-colon">:</span></div>
                            <input type="text" id="storageDate-begin" style="width: 150px" lay-verify="date" th:placeholder="${lang.translate('开始日期')}" autocomplete="off" class="layui-input search-input search-date-input"  readonly >
                            <span class="search-dash">-</span>
                            <input type="text" id="storageDate-end"  style="width: 150px"  lay-verify="date" th:placeholder="${lang.translate('结束日期')}" autocomplete="off" class="layui-input search-input search-date-input" readonly>
                    </div>


                </div>
            </div>


            <!-- 按钮区域 -->
            <div id="search-area" class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索')}">搜索</span></button>
                <button id="search-button-advance" class="layui-btn layui-btn-primary icon-btn search-button-advance"><i class="layui-icon">&#xe671;</i><span th:text="${lang.translate('更多')}">更多</span></button>
            </div>
        </div>

        <div id="table-area" style="margin-top: 84px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
        <button th:if="${perm.checkAuth('eam_goods_stock:create')}" id="add-button" class="layui-btn icon-btn layui-btn-sm create-new-button " lay-event="create"><i class="layui-icon">&#xe654;</i><span th:text="${lang.translate('新建')}">新建</span></button>
        <button th:if="${perm.checkAuth('eam_goods_stock:delete-by-ids')}" id="delete-button" class="layui-btn icon-btn layui-btn-danger layui-btn-sm batch-delete-button " lay-event="batch-del"><i class="layui-icon">&#xe67e;</i><span th:text="${lang.translate('删除')}">删除</span></button>
    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">

    <button th:if="${perm.checkAuth('eam_goods_stock:view-form')}" class="layui-btn layui-btn-primary layui-btn-xs ops-view-button " lay-event="view"  data-id="{{d.id}}"> <span th:text="${lang.translate('查看')}">查看</span></button>
    <button th:if="${perm.checkAnyAuth('eam_goods_stock:update','eam_goods_stock:save')}" class="layui-btn layui-btn-primary layui-btn-xs ops-edit-button " lay-event="edit"data-id="{{d.id}}"><span th:text="${lang.translate('修改')}">修改</span></button>
    <button th:if="${perm.checkAuth('eam_goods_stock:delete')}" class="layui-btn layui-btn-xs layui-btn-danger ops-delete-button " lay-event="del" data-id="{{d.id}}"><span th:text="${lang.translate('删除')}">删除</span></button>

</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetHandleStatusEnum')}]];
    var SELECT_GOODSSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var AUTH_PREFIX="eam_goods_stock_2";

    // OWNER_CODE
    var OWNER_CODE = [[${ownerCode}]] ;
    // OWNER_TYPE
    var OWNER_TYPE = [[${ownerType}]] ;
    // CATEGORY_CODE
    var CATEGORY_CODE = [[${categoryCode}]] ;

</script>

<script th:src="'/business/eam/goods_stock/goods_stock_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/goods_stock/goods_stock_list.js?'+${cacheKey}"></script>

</body>
</html>
