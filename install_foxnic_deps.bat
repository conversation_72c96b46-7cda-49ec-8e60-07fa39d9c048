@echo off
chcp 65001 >nul
echo ================================
echo 安装Foxnic依赖到本地Maven仓库
echo ================================

set MAVEN_CMD="E:\eam-2.9.1\apache-maven-3.6.3\bin\mvn.cmd"
set LIB_DIR=temp_download\app\lib

echo 正在安装核心Foxnic依赖...

echo 安装 foxnic-sql...
%MAVEN_CMD% install:install-file -Dfile=%LIB_DIR%\foxnic-sql-1.8.0.RELEASE.jar -DgroupId=com.foxnicweb -DartifactId=foxnic-sql -Dversion=1.8.0.RELEASE -Dpackaging=jar

echo 安装 domain...
%MAVEN_CMD% install:install-file -Dfile=%LIB_DIR%\domain-1.8.0.RELEASE.jar -DgroupId=com.foxnicweb.web -DartifactId=domain -Dversion=1.8.0.RELEASE -Dpackaging=jar

echo 安装 contract-domain...
%MAVEN_CMD% install:install-file -Dfile=%LIB_DIR%\contract-domain-1.0.0.2.jar -DgroupId=com.foxnicweb.web -DartifactId=contract-domain -Dversion=1.0.0.2 -Dpackaging=jar

echo 安装 domain-eam...
%MAVEN_CMD% install:install-file -Dfile=%LIB_DIR%\domain-eam-2.9.1.2.jar -DgroupId=com.dt.platform -DartifactId=domain-eam -Dversion=2.9.1.2 -Dpackaging=jar

echo 安装 framework-eam...
%MAVEN_CMD% install:install-file -Dfile=%LIB_DIR%\framework-eam-2.9.1.2.jar -DgroupId=com.dt.platform -DartifactId=framework-eam -Dversion=2.9.1.2 -Dpackaging=jar

echo 安装 proxy-eam...
%MAVEN_CMD% install:install-file -Dfile=%LIB_DIR%\proxy-eam-2.9.1.2.jar -DgroupId=com.dt.platform -DartifactId=proxy-eam -Dversion=2.9.1.2 -Dpackaging=jar

echo 安装其他foxnic核心组件...
%MAVEN_CMD% install:install-file -Dfile=%LIB_DIR%\foxnic-api-1.8.0.RELEASE.jar -DgroupId=com.foxnicweb -DartifactId=foxnic-api -Dversion=1.8.0.RELEASE -Dpackaging=jar

%MAVEN_CMD% install:install-file -Dfile=%LIB_DIR%\foxnic-commons-1.8.0.RELEASE.jar -DgroupId=com.foxnicweb -DartifactId=foxnic-commons -Dversion=1.8.0.RELEASE -Dpackaging=jar

%MAVEN_CMD% install:install-file -Dfile=%LIB_DIR%\foxnic-dao-1.8.0.RELEASE.jar -DgroupId=com.foxnicweb -DartifactId=foxnic-dao -Dversion=1.8.0.RELEASE -Dpackaging=jar

%MAVEN_CMD% install:install-file -Dfile=%LIB_DIR%\foxnic-springboot-1.8.0.RELEASE.jar -DgroupId=com.foxnicweb -DartifactId=foxnic-springboot -Dversion=1.8.0.RELEASE -Dpackaging=jar

%MAVEN_CMD% install:install-file -Dfile=%LIB_DIR%\framework-boot-1.8.0.RELEASE.jar -DgroupId=com.foxnicweb -DartifactId=framework-boot -Dversion=1.8.0.RELEASE -Dpackaging=jar

%MAVEN_CMD% install:install-file -Dfile=%LIB_DIR%\framework-view-1.8.0.RELEASE.jar -DgroupId=com.foxnicweb -DartifactId=framework-view -Dversion=1.8.0.RELEASE -Dpackaging=jar

%MAVEN_CMD% install:install-file -Dfile=%LIB_DIR%\proxy-1.8.0.RELEASE.jar -DgroupId=com.foxnicweb -DartifactId=proxy -Dversion=1.8.0.RELEASE -Dpackaging=jar

echo ================================
echo Foxnic依赖安装完成！
echo 现在可以尝试编译项目了
echo ================================
pause
