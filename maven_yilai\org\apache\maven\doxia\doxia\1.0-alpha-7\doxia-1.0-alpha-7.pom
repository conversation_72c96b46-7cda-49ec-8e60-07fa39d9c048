<?xml version="1.0" encoding="UTF-8"?><project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.apache.maven.doxia</groupId>
  <artifactId>doxia</artifactId>
  <packaging>pom</packaging>
  <name>Doxia</name>
  <version>1.0-alpha-7</version>
  <url>http://maven.apache.org/doxia</url>
  <issueManagement>
    <system>jira</system>
    <url>http://jira.codehaus.org/browse/DOXIA</url>
  </issueManagement>
  <ciManagement>
    <system>continuum</system>
    <notifiers>
      <notifier>
        <address><EMAIL></address>
      </notifier>
    </notifiers>
  </ciManagement>
  <mailingLists>
    <mailingList>
      <name>Doxia Developer List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-doxia-dev/</archive>
    </mailingList>
    <mailingList>
      <name>Doxia User List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-doxia-users/</archive>
    </mailingList>
    <mailingList>
      <name>Doxia Commits List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-doxia-commits/</archive>
    </mailingList>
  </mailingLists>
  <developers>
    <developer>
      <name>Jason van Zyl</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
      <timezone>-5</timezone>
    </developer>
    <developer>
      <name>Brett Porter</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
      <timezone>+10</timezone>
    </developer>
    <developer>
      <name>Emmanuel Venisse</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
      <timezone>1</timezone>
    </developer>
  </developers>
  <scm>
    <connection>scm:svn:http://svn.apache.org/repos/asf/maven/doxia/tags/doxia-1.0-alpha-7</connection>
    <developerConnection>scm:svn:https://svn.apache.org/repos/asf/maven/doxia/tags/doxia-1.0-alpha-7</developerConnection>
    <url>http://svn.apache.org/viewcvs.cgi/maven/doxia/tags/doxia-1.0-alpha-7</url>
  </scm>
  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-maven-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>descriptor</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <modules>
    <module>doxia-core</module>
    <module>doxia-sink-api</module>
    <module>doxia-site-renderer</module>
    <module>doxia-decoration-model</module>
  </modules>
  <distributionManagement>
    <repository>
      <id>repo1</id>
      <name>Maven Central Repository</name>
      <url>scp://repo1.maven.org/home/<USER>/maven/repository-staging/to-ibiblio/maven2</url>
    </repository>
    <snapshotRepository>
      <id>snapshots</id>
      <name>Maven Central Development Repository</name>
      <url>scp://repo1.maven.org/home/<USER>/maven/repository-staging/snapshots/maven2</url>
    </snapshotRepository>
    <site>
      <id>website</id>
      <url>scp://minotaur.apache.org/www/maven.apache.org/doxia/</url>
    </site>
  </distributionManagement>
</project>