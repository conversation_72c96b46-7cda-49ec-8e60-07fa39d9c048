/**
 * 采购申请 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2022-04-16 06:46:55
 */

layui.config({
    dir: layuiPath,
    base: '/module/'
}).extend({
    xmSelect: 'xm-select/xm-select',
    foxnicUpload: 'upload/foxnic-upload'
})
//
layui.define(['form', 'table', 'util', 'settings', 'admin', 'upload','foxnic','xmSelect','laydate','foxnicUpload','dropdown','bpm'],function () {

    var admin = layui.admin,settings = layui.settings,form = layui.form,upload = layui.upload,laydate= layui.laydate,dropdown=layui.dropdown;
    table = layui.table,layer = layui.layer,util = layui.util,fox = layui.foxnic,xmSelect = layui.xmSelect,foxup=layui.foxnicUpload,bpm=layui.bpm;
    var bpmFunction=layui.bpmFunction;


    var formAction=admin.getTempData('eam-purchase-apply-form-data-form-action');
    //模块基础路径
    const moduleURL="/service-eam/eam-purchase-apply";

    var bpmFunction=layui.bpmFunction;
    var processId=QueryString.get("processId");
    var processInstance=null;

    //列表页的扩展
    var list={
        getNowFormatDate:function() {
            var date = new Date();
            var seperator1 = "";
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var strDate = date.getDate();
            if (month >= 1 && month <= 9) {
                month = "0" + month;
            }
            if (strDate >= 0 && strDate <= 9) {
                strDate = "0" + strDate;
            }
            var currentdate = year + seperator1 + month + seperator1 + strDate;
            return currentdate;
        },
        getBpmViewConfig:function (act) {
            return {
                title:"资产采购申请-"+  window.pageExt.list.getNowFormatDate(),
                priority:"normal", // priority 的可选值 urgency，normal
                labelWidth:77, // 标签宽度，用于对齐
                displayTitle:true,  // 是否显示标题与优先级
                displayPriority:true, // 是否显示优先级
                displayDraftComment:true, // 是否显示起草节点的流程说明
                displayApprovalComment:true, // 是否显示签字意见
                displayDraftAttachment:true, // 是否使用起草附件
                displayApprovalAttachment:true // 是否使用审批附件
            }
        },



        bpmOpenWithoutProcess:function(pkdata) {
            top.layer.msg('当前业务单据尚未关联流程', {icon: 2, time: 1500});
        },
        /**
         * 列表页初始化前调用
         * */

        beforeInit:function () {
            var toolbarHtml=document.getElementById("toolbarTemplate").innerHTML;
            var operHtml=document.getElementById("tableOperationTemplate").innerHTML;
            if(APPROVAL_REQUIRED){
                operHtml=operHtml.replace(/lay-event="download-bill"/i, "style=\"display:none\"")
                operHtml=operHtml.replace(/lay-event="confirm-data"/i, "style=\"display:none\"")
                // operHtml=operHtml.replace(/lay-event="confirm-data"/i, "style=\"display:none\"")
                // document.getElementById("tableOperationTemplate").innerHTML=operHtml;
            }else{
                operHtml=operHtml.replace(/lay-event="download-bill"/i, "style=\"display:none\"")
                operHtml=operHtml.replace(/lay-event="revoke-data"/i, "style=\"display:none\"")
                operHtml=operHtml.replace(/lay-event="for-approval"/i, "style=\"display:none\"")
                // operHtml=operHtml.replace(/lay-event="revoke-data"/i, "style=\"display:none\"")
                // operHtml=operHtml.replace(/lay-event="for-approval"/i, "style=\"display:none\"")
                // document.getElementById("tableOperationTemplate").innerHTML=operHtml;
            }


            // if(PAGE_TYPE&&PAGE_TYPE=="approval"){
            //     $("#status-search-unit").hide();
            //     toolbarHtml=toolbarHtml.replace(/lay-event="create"/i, "style=\"display:none\"")
            //     operHtml=operHtml.replace(/lay-event="confirm-data"/i, "style=\"display:none\"")
            //     operHtml=operHtml.replace(/lay-event="revoke-data"/i, "style=\"display:none\"")
            //     operHtml=operHtml.replace(/lay-event="for-approval"/i, "style=\"display:none\"")
            //     operHtml=operHtml.replace(/lay-event="edit"/i, "style=\"display:none\"")
            //     operHtml=operHtml.replace(/lay-event="del"/i, "style=\"display:none\"")
            //
            //     operHtml=operHtml.replace(/lay-event="clean-out"/i, "style=\"display:none\"")
            //     operHtml=operHtml.replace(/lay-event="download-bill"/i, "style=\"display:none\"")
            //
            // }else if( PAGE_TYPE&&PAGE_TYPE=="asset_scrap"){
            //     operHtml=operHtml.replace(/lay-event="agree"/i, "style=\"display:none\"")
            //     operHtml=operHtml.replace(/lay-event="deny"/i, "style=\"display:none\"")
            // }

            document.getElementById("toolbarTemplate").innerHTML=toolbarHtml;
            document.getElementById("tableOperationTemplate").innerHTML=operHtml;
            console.log("list:beforeInit");
            if(PAGE_TAB_STATUS){
                if("wait"==PAGE_TAB_STATUS){
                    $("#add-button").hide();
                    $("#add-button").remove();
                    setTimeout(function () {
                        $("#add-button").hide();
                        $("#add-button").remove();
                    },100)
                }
            }
        },
        /**
         * 表格渲染前调用
         * @param cfg 表格配置参数
         * */
        beforeTableRender:function (cfg){
            cfg.cellMinWidth=160;;
        },
        /**
         * 表格渲染后调用
         * */
        afterTableRender :function (){

        },
        afterSearchInputReady: function() {
            console.log("list:afterSearchInputReady");
        },
        /**
         * 对话框打开之前调用，如果返回 null 则不打开对话框
         * */
        beforeDialog:function (param){
            //param.title="覆盖对话框标题";
            return param;
        },
        /**
         * 对话框回调，表单域以及按钮 会自动改变为选中的值，此处处理额外的逻辑即可
         * */
        afterDialog:function (param,result) {
            console.log('dialog',param,result);
        },
        /**
         * 当下拉框别选择后触发
         * */
        onSelectBoxChanged:function(id,selected,changes,isAdd) {
            console.log('onSelectBoxChanged',id,selected,changes,isAdd);
        },
        /**
         * 当日期选择组件选择后触发
         * */
        onDatePickerChanged:function(id,value, date, endDate) {
            console.log('onDatePickerChanged',id,value, date, endDate);
        },
        /**
         * 查询前调用
         * @param conditions 复合查询条件
         * @param param 请求参数
         * @param location 调用的代码位置
         * */
        beforeQuery:function (conditions,param,location) {
            console.log('beforeQuery',conditions,param,location);
            if(PAGE_TAB_STATUS){
                if("wait"==PAGE_TAB_STATUS){
                    param.status="complete";
                    param.assetCheck="not_check";
                }
            }

            return true;
        },
        /**
         * 查询结果渲染后调用
         * */
        afterQuery : function (data) {



            for (var i = 0; i < data.length; i++) {

                bpmFunction.columnBpmOpenButtonStatus(data[i]);

                //如果审批中或审批通过的不允许编辑
                if(data[i].status=="complete") {
                    // fox.disableButton($('.ops-delete-button').filter("[data-id='" + data[i].id + "']"), true);
                    // fox.disableButton($('.ops-edit-button').filter("[data-id='" + data[i].id + "']"), true);
                    // fox.disableButton($('.for-approval-button').filter("[data-id='" + data[i].id + "']"), true);
                    // fox.disableButton($('.confirm-data-button').filter("[data-id='" + data[i].id + "']"), true);
                    // fox.disableButton($('.revoke-data-button').filter("[data-id='" + data[i].id + "']"), true);
                    if(data[i].assetCheck=="not_check"){
                        console.log("enable")
                    }else{
                        fox.disableButton($('.check-bill-button').filter("[data-id='" + data[i].id + "']"), true);
                    }

                }else if(data[i].status=="incomplete"){
                    fox.disableButton($('.check-bill-button').filter("[data-id='" + data[i].id + "']"), true);

                    // fox.disableButton($('.ops-delete-button').filter("[data-id='" + data[i].id + "']"), true);
                    // fox.disableButton($('.ops-edit-button').filter("[data-id='" + data[i].id + "']"), true);
                    // fox.disableButton($('.for-approval-button').filter("[data-id='" + data[i].id + "']"), true);
                    // fox.disableButton($('.confirm-data-button').filter("[data-id='" + data[i].id + "']"), true);


                  //  fox.disableButton($('.revoke-data-button').filter("[data-id='" + data[i].id + "']"), true);
                }else if(data[i].status=="deny"){
                    fox.disableButton($('.check-bill-button').filter("[data-id='" + data[i].id + "']"), true);
                    // fox.disableButton($('.check-bill-button').filter("[data-id='" + data[i].id + "']"), true);
                    // fox.disableButton($('.ops-delete-button').filter("[data-id='" + data[i].id + "']"), true);
                    // fox.disableButton($('.ops-edit-button').filter("[data-id='" + data[i].id + "']"), true);
                    // fox.disableButton($('.for-approval-button').filter("[data-id='" + data[i].id + "']"), true);
                    // fox.disableButton($('.confirm-data-button').filter("[data-id='" + data[i].id + "']"), true);
                    // fox.disableButton($('.revoke-data-button').filter("[data-id='" + data[i].id + "']"), true);
                }else if(data[i].status=="approval"){
                    fox.disableButton($('.check-bill-button').filter("[data-id='" + data[i].id + "']"), true);
                    // fox.disableButton($('.check-bill-button').filter("[data-id='" + data[i].id + "']"), true);
                    // fox.disableButton($('.ops-delete-button').filter("[data-id='" + data[i].id + "']"), true);
                    // fox.disableButton($('.ops-edit-button').filter("[data-id='" + data[i].id + "']"), true);
                    // fox.disableButton($('.for-approval-button').filter("[data-id='" + data[i].id + "']"), true);
                    // fox.disableButton($('.confirm-data-button').filter("[data-id='" + data[i].id + "']"), true);
                    // fox.disableButton($('.revoke-data-button').filter("[data-id='" + data[i].id + "']"), true);
                }else if(data[i].status=="cancel"){

                //    fox.disableButton($('.ops-delete-button').filter("[data-id='" + data[i].id + "']"), true);
                    fox.disableButton($('.ops-edit-button').filter("[data-id='" + data[i].id + "']"), true);
                    fox.disableButton($('.for-approval-button').filter("[data-id='" + data[i].id + "']"), true);
                    fox.disableButton($('.confirm-data-button').filter("[data-id='" + data[i].id + "']"), true);
                    fox.disableButton($('.revoke-data-button').filter("[data-id='" + data[i].id + "']"), true);
                    fox.disableButton($('.check-bill-button').filter("[data-id='" + data[i].id + "']"), true);
                }

                if(data[i].cleanStatus=="complete"){
                    fox.disableButton($('.clean-out-button').filter("[data-id='" + data[i].id + "']"), true);
                }

            }

        },
        /**
         * 进一步转换 list 数据
         * */
        templet:function (field,value,r) {
            if(value==null) return "";
            return value;
        },
        /**
         * 表单页面打开时，追加更多的参数信息
         * */
        makeFormQueryString:function(data,queryString,action) {
            return queryString;
        },
        /**
         * 在新建或编辑窗口打开前调用，若返回 false 则不继续执行后续操作
         * */
        beforeEdit:function (data) {
            console.log('beforeEdit',data);
            return true;
        },
        /**
         * 单行删除前调用，若返回false则不执行后续操作
         * */
        beforeSingleDelete:function (data) {
            console.log('beforeSingleDelete',data);
            return true;
        },
        afterSingleDelete:function (data){
            console.log('beforeSingleDelete',data);
            return true;
        },
        /**
         * 批量删除前调用，若返回false则不执行后续操作
         * */
        beforeBatchDelete:function (selected) {
            console.log('beforeBatchDelete',selected);
            return true;
        },
        /**
         * 批量删除后调用，若返回false则不执行后续操作
         * */
        afterBatchDelete:function (data) {
            console.log('afterBatchDelete',data);
            return true;
        },
        /**
         * 工具栏按钮事件前调用，如果返回 false 则不执行后续代码
         * */
        beforeToolBarButtonEvent:function (selected,obj) {
            console.log('beforeToolBarButtonEvent',selected,obj);
            return true;
        },
        /**
         * 列表操作栏按钮事件前调用，如果返回 false 则不执行后续代码
         * */
        beforeRowOperationEvent:function (data,obj) {
            console.log('beforeRowOperationEvent',data,obj);
            return true;
        },
        billOper:function(url,btnClass,ps,successMessage){
            var btn=$('.'+btnClass).filter("[data-id='" +ps.id + "']");
            var api=moduleURL+"/"+url;

            top.layer.confirm(fox.translate('确定进行该操作吗？'), function (i) {
                top.layer.close(i);
                admin.post(api, ps, function (r) {
                    if (r.success) {
                        top.layer.msg(successMessage, {time: 1000});
                        window.module.refreshTableData();
                    } else {
                        var errs = [];
                        if(r.errors&&r.errors.length>0){
                            for (var i = 0; i < r.errors.length; i++) {
                                if (errs.indexOf(r.errors[i].message) == -1) {
                                    errs.push(r.errors[i].message);
                                }
                            }
                            top.layer.msg(errs.join("<br>"), {time: 2000});
                        } else {
                            top.layer.msg(r.message, {time: 2000});
                        }
                    }
                }, {delayLoading: 1000, elms: [btn]});
            });
        },
        /**
         * 表格右侧操作列更多按钮事件
         * */
        moreAction:function (menu,data, it){
            console.log('moreAction',menu,data,it);
        },
        forApproval:function (data){
            console.log('forApproval',data);
        },
        confirmData:function (data){
            list.billOper("confirm-operation","confirm-data-button",{id:data.id},"已确认");
            console.log('confirmData',data);
        },
        revokeData:function (data){
            console.log('revokeData',data);
        },
        downloadBill:function (data){
            console.log('downloadBill',data);
        },
        check:function (applyData){
            //设置新增是初始化数据
            var data={};
            admin.putTempData('eam-purchase-check-form-data-form-action', "create",true);
            var action="create";
            var queryString="applyId="+applyData.id;
            admin.putTempData('eam-purchase-check-form-data', data);
            var area=admin.getTempData('eam-purchase-check-form-area');
            var height= (area && area.height) ? area.height : ($(window).height()*0.6);
            var top= (area && area.top) ? area.top : (($(window).height()-height)/2);
            var title = fox.translate('采购验收');
            if(action=="create") title=fox.translate('添加')+title;
            else if(action=="edit") title=fox.translate('修改')+title;
            else if(action=="view") title=fox.translate('查看')+title;
            admin.popupCenter({
                title: title,
                resize: false,
                offset: [top,null],
                area: ["80%",height+"px"],
                type: 2,
                id:"eam-purchase-check-form-data-win",
                content: '/business/eam/purchase_check/purchase_check_form.html' + (queryString?("?"+queryString):""),
                finish: function () {
                    setTimeout(function(){
                        window.module.refreshTableData();
                    },500);
                    if(action=="create") {

                    }
                }
            });
        },
        /**
         * 末尾执行
         */
        ending:function() {

        }
    }
    var timestamp = Date.parse(new Date());

    //表单页的扩展
    var form={
            /**
             * 在流程提交前处理表单数据
             * */
            processFormData4Bpm:function(processInstanceId,param,callback) {
                // 设置流程变量，并通过回调返回
                var variables={};
                // 此回调是必须的，否则流程提交会被中断
                callback(variables);
            },
            onProcessInstanceReady:function (result) {
            // 可根据流程状态、当前审批节点判断和控制表单页面
            processInstance=result.data;
            console.log("processInstance",processInstance);
            // 获得所有待办节点
            var todoNodes=bpm.getTodoNodes(processInstance);
            console.log("todoNodes",todoNodes);
            // 判断是否为待办节点
            var isTodoNode=bpm.isTodoNodes(processInstance,"N1");
            console.log("isTodoNode:N1",isTodoNode);
            // 判断是否为当前账户的待办节点
            var isMyTodoNode=bpm.isCurrentUserTodoNode(processInstance,"N1");
            console.log("isMyTodoNode:N1",isMyTodoNode);
        },
        /**
         * 表单初始化前调用 , 并传入表单数据
         * */
        beforeInit:function (action,data) {
            //获取参数，并调整下拉框查询用的URL
            //var companyId=admin.getTempData("companyId");
            //fox.setSelectBoxUrl("employeeId","/service-hrm/hrm-employee/query-paged-list?companyId="+companyId);
            console.log("form:beforeInit")
            if(processId==null){
                formAction="create";
            }

            if(formAction=="create"){
                //默认填充报修人员
                if($("#purchaseUserId")){
                //    $("#purchaseUserId").val(CUR_EMP_ID);
                }
                if($("#purchaseUserId-button")){
                    var html="<i class=\"layui-icon layui-icon-search\"></i><span default-label=\"采购人员\">自动填充</span>"
                    $("#purchaseUserId-button").html(html);
                    $("#purchaseUserId-button").attr("disabled","disabled");
                    $("#purchaseUserId-button").css("background-color", "#D2D2D2");
                }
            }

        },
        /**
         * 窗口调节前
         * */
        beforeAdjustPopup:function () {
            console.log('beforeAdjustPopup');
            return true;
        },
        /**
         * 表单数据填充前
         * */
        beforeDataFill:function (data) {
            console.log('beforeDataFill',data);
        },
        /**
         * 表单数据填充后
         * */
        afterDataFill:function (data) {
            console.log('afterDataFill',data);

            if(data.status){
                if(data.status=="incomplete"){
                    console.log("can edit");
                }else{
                    fox.lockForm($("#data-form"),true);
                }
            }

        },
        /**
         * 对话框打开之前调用，如果返回 null 则不打开对话框
         * */
        beforeDialog:function (param){
            //param.title="覆盖对话框标题";
            return param;
        },
        /**
         * 对话框回调，表单域以及按钮 会自动改变为选中的值，此处处理额外的逻辑即可
         * */
        afterDialog:function (param,result) {
            console.log('dialog',param,result);
        },
        /**
         * 当下拉框别选择后触发
         * */
        onSelectBoxChanged:function(id,selected,changes,isAdd) {
            console.log('onSelectBoxChanged',id,selected,changes,isAdd);
        },
        /**
         * 当日期选择组件选择后触发
         * */
        onDatePickerChanged:function(id,value, date, endDate) {
            console.log('onDatePickerChanged',id,value, date, endDate);
        },
        /**
         * 数据提交前，如果返回 false，停止后续步骤的执行
         * */
        beforeSubmit:function (data) {
            data.selectedCode=timestamp;
            console.log("beforeSubmit",data);
            return true;
        },
        /**
         * 数据提交后窗口关闭前，如果返回 false，停止后续步骤的执行
         * */
        betweenFormSubmitAndClose:function (param,result) {
            console.log("betweenFormSubmitAndClose",result);
            return true;
        },
        /**
         * 数据提交后执行
         * */
        afterSubmit:function (param,result) {
            console.log("afterSubmitt",param,result);
        },

        /**
         *  加载 订单列表
         */
        assetSelectOrderList:function (ifr,win,data) {

            var pageType="view";
            var ownerId=timestamp;


            console.log("ownerId:######1",ownerId,formAction,pageType);
            if(formAction=="create"){
                pageType="create"
                ownerId=timestamp;
            }else if(formAction=="view"){
                ownerId=data.id;
                pageType="view"
            }else if(formAction=="edit"){
                ownerId=data.id;
                if(data.status=="incomplete"){
                    pageType="modify";
                }else{
                    pageType="view";
                }
            }else{
                pageType="view";
                ownerId=data.id;
            }

            console.log("ownerId:######2",ownerId,formAction,pageType);
            //设置 iframe 高度
            ifr.height("400px");
            //设置地址
            win.location="/business/eam/purchase_order/purchase_order_selected_list.html?selectedCode="+timestamp+"&pageType="+pageType+"&ownerId="+ownerId+"&ownerType=buy";
        },
        /**
         * 文件上传组件回调
         *  event 类型包括：
         *  afterPreview ：文件选择后，未上传前触发；
         *  afterUpload ：文件上传后触发
         *  beforeRemove ：文件删除前触发
         *  afterRemove ：文件删除后触发
         * */
        onUploadEvent: function(e) {
            console.log("onUploadEvent",e);
        },
        /**
         * 末尾执行
         */
        ending:function() {

        }
    }
    //
    window.pageExt={form:form,list:list};
});