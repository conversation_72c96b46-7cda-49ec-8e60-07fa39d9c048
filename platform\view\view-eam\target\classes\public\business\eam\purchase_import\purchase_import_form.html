<!--
/**
 * 采购单导入资产 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2023-12-01 20:35:15
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
	<title th:text="${lang.translate('采购单导入资产')}">采购单导入资产</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="opacity:0">

        <input name="id" id="id"  type="hidden"/>

         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-0447-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >

                <!-- text_input : 名称 ,  name -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('名称')}">名称</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="name" id="name" name="name" th:placeholder="${ lang.translate('请输入'+'名称') }" type="text" class="layui-input"    lay-verify="|required"  />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >

                <!-- button : 采购单 ,  orderId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('采购单')}">采购单</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="orderId" id="orderId" name="orderId"  type="hidden" class="layui-input"    lay-verify="|required"   />
                        <button id="orderId-button" type="button" action-type="" class="layui-btn " style="width: 100%" default-width="100%" auto-width="false"><span th:text="${lang.translate('选择')}" th:default-label="${lang.translate('选择')}">按钮文本</span></button>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->

        <fieldset class="layui-elem-field layui-field-title form-group-title" id="random-1727-fieldset">
            <legend>数据覆盖</legend>
        </fieldset>

        <div class="layui-row form-row" id="random-1727-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >

                <!-- button : 所属公司 ,  ownCompanyId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('所属公司')}">所属公司</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="ownCompanyId" id="ownCompanyId" name="ownCompanyId"  type="hidden" class="layui-input"   />
                        <button id="ownCompanyId-button" type="button" action-type="org-dialog" class="layui-btn   " style="width: 100%" default-width="100%" auto-width="false"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择公司')}" th:default-label="${lang.translate('请选择公司')}">按钮文本</span></button>
                    </div>
                </div>
            
                <!-- button : 管理人 ,  managerId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('管理人')}">管理人</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="managerId" id="managerId" name="managerId"  type="hidden" class="layui-input"   />
                        <button id="managerId-button" type="button" action-type="emp-dialog" class="layui-btn   " style="width: 100%" default-width="100%" auto-width="false"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择人员')}" th:default-label="${lang.translate('请选择人员')}">按钮文本</span></button>
                    </div>
                </div>
            
                <!-- select_box : 位置 ,  positionId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('位置')}">位置</div></div>
                    <div class="layui-input-block ">
                        <div id="positionId" input-type="select" th:data="${'/service-eam/eam-position/query-paged-list'}" extraParam="{}"></div>
                    </div>
                </div>
            
                <!-- text_input : 资产备注 ,  assetNotes -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('资产备注')}">资产备注</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="assetNotes" id="assetNotes" name="assetNotes" th:placeholder="${ lang.translate('请输入'+'资产备注') }" type="text" class="layui-input"  />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >

                <!-- button : 使用部门 ,  useOrgId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('使用部门')}">使用部门</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="useOrgId" id="useOrgId" name="useOrgId"  type="hidden" class="layui-input"   />
                        <button id="useOrgId-button" type="button" action-type="org-dialog" class="layui-btn   " style="width: 100%" default-width="100%" auto-width="false"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择组织节点')}" th:default-label="${lang.translate('请选择组织节点')}">按钮文本</span></button>
                    </div>
                </div>
            
                <!-- button : 使用人 ,  useUserId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('使用人')}">使用人</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="useUserId" id="useUserId" name="useUserId"  type="hidden" class="layui-input"   />
                        <button id="useUserId-button" type="button" action-type="emp-dialog" class="layui-btn   " style="width: 100%" default-width="100%" auto-width="false"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择人员')}" th:default-label="${lang.translate('请选择人员')}">按钮文本</span></button>
                    </div>
                </div>
            
                <!-- date_input : 采购日期 ,  purchaseDate  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('采购日期')}">采购日期</div></div>
                    <div class="layui-input-block ">
                        <input input-type="date" lay-filter="purchaseDate" id="purchaseDate" name="purchaseDate"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择'+'采购日期') }" type="text" class="layui-input"    lay-verify=""   />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-4139-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column"  style="padding-top: 0px" >

                <!-- button : 填充数据 ,  fill  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('填充数据')}">填充数据</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="fill" id="fill" name="fill"  type="hidden" class="layui-input"   />
                        <button id="fill-button" type="button" action-type="" class="layui-btn " style="width: 100%" default-width="100%" auto-width="false"><span th:text="${lang.translate('填充数据')}" th:default-label="${lang.translate('填充数据')}">按钮文本</span></button>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column"  style="padding-top: 0px" >
<!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->
        <fieldset class="layui-elem-field layui-field-title form-group-title" id="assetListEl-fieldset">
        <legend>资产信息</legend>
        </fieldset>
        <div class="layui-row form-row" style="text-align: center;" id="assetListEl-content">
            <div style="display: inline-block;padding-right: 8px;padding-left: 8px" class="layui-col-xs12">
            <iframe id="assetListEl-iframe" js-fn="assetList" class="form-iframe" frameborder="0" style="width: 100%"></iframe>
            </div>
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 120px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消','','form.button')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('eam_purchase_import:create','eam_purchase_import:update','eam_purchase_import:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存','','form.button')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var RADIO_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetImportStatusEnum')}]];
    var RADIO_IMPORTTOTYPE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetImportSourceTypeEnum')}]];
    var VALIDATE_CONFIG={"purchaseDate":{"date":true,"labelInForm":"采购日期","inputType":"date_input"},"orderId":{"labelInForm":"采购单","inputType":"button","required":true},"name":{"labelInForm":"名称","inputType":"text_input","required":true}};
    var AUTH_PREFIX="eam_purchase_import";

    // importType
    var IMPORT_TYPE = [[${importType}]] ;

</script>



<script th:src="'/business/eam/purchase_import/purchase_import_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/purchase_import/purchase_import_form.js?'+${cacheKey}"></script>

</body>
</html>