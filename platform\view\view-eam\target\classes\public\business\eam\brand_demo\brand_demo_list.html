<!--
/**
 * 品牌 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2021-11-01 19:16:31
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <title th:text="${lang.translate('品牌')}">品牌</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" th:href="@{/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css}" rel="stylesheet"/>
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon"> <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
    <style>
    </style>
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 名称 , brandName ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('名称')}" class="search-label brandName-label">名称</span><span class="search-colon">:</span></div>
                        <input id="brandName" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>
                    <!-- 排序 , sort ,typeName=number_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('排序')}" class="search-label sort-label">排序</span><span class="search-colon">:</span></div>
                            <input id="sort" class="layui-input search-input" style="width: 150px" type="text" autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0" min-value="" max-value="" scale="0"/>
                    </div>


                </div>
            </div>


            <!-- 按钮区域 -->
            <div class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索')}">搜索</span></button>
            </div>
        </div>

        <div style="margin-top: 42px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>

<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
        <button th:if="${perm.checkAuth('eam_brand_demo:create')}" id="add-button" class="layui-btn icon-btn layui-btn-sm create-new-button" lay-event="create"><i class="layui-icon">&#xe654;</i><span th:text="${lang.translate('新建')}">新建</span></button>
        <button th:if="${perm.checkAuth('eam_brand_demo:delete-by-ids')}" id="delete-button" class="layui-btn icon-btn layui-btn-danger layui-btn-sm batch-delete-button" lay-event="batch-del"><i class="layui-icon">&#xe67e;</i><span th:text="${lang.translate('删除')}">删除</span></button>
    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">

    <button th:if="${perm.checkAuth('eam_brand_demo:view-form')}" class="layui-btn layui-btn-primary layui-btn-xs ops-view-button" lay-event="view" th:text="${lang.translate('查看')}" data-id="{{d.id}}">查看</button>
    <button th:if="${perm.checkAnyAuth('eam_brand_demo:update','eam_brand_demo:save')}" class="layui-btn layui-btn-primary layui-btn-xs ops-edit-button" lay-event="edit" th:text="${lang.translate('修改')}" data-id="{{d.id}}">修改</button>


    <button th:if="${perm.checkAuth('eam_brand_demo:delete')}" class="layui-btn layui-btn-xs layui-btn-danger ops-delete-button" lay-event="del" th:text="${lang.translate('删除')}" data-id="{{d.id}}">删除</button>


</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
    var AUTH_PREFIX="eam_brand_demo";


</script>

<script th:src="'/business/eam/brand_demo/brand_demo_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/brand_demo/brand_demo_list.js?'+${cacheKey}"></script>

</body>
</html>
