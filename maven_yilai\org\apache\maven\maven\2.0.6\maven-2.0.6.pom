<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.apache.maven</groupId>
    <artifactId>maven-parent</artifactId>
    <version>5</version>
    <relativePath>../pom/maven/pom.xml</relativePath>
  </parent>
  <artifactId>maven</artifactId>
  <packaging>pom</packaging>
  <name>Maven</name>
  <version>2.0.6</version>
  <description>Maven is a project development management and comprehension tool. Based on the concept of a project object model: builds, dependency management, documentation creation, site publication, and distribution publication are all controlled from the declarative file. Maven can be extended by plugins to utilise a number of other development tools for reporting or the build process.</description>
  <url>http://maven.apache.org</url>
  <issueManagement>
    <system>jira</system>
    <url>http://jira.codehaus.org/browse/MNG</url>
  </issueManagement>
  <inceptionYear>2001</inceptionYear>
  <mailingLists>
    <mailingList>
      <name>Maven User List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-users</archive>
      <otherArchives>
        <otherArchive>http://www.mail-archive.com/<EMAIL>/</otherArchive>
        <otherArchive>http://www.nabble.com/Maven---Users-f178.html</otherArchive>
      </otherArchives>
    </mailingList>
  </mailingLists>
  <scm>
    <connection>scm:svn:https://svn.apache.org/repos/asf/maven/components/tags/maven-2.0.6</connection>
    <developerConnection>scm:svn:https://svn.apache.org/repos/asf/maven/components/tags/maven-2.0.6</developerConnection>
    <url>https://svn.apache.org/repos/asf/maven/components/tags/maven-2.0.6</url>
  </scm>
  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-release-plugin</artifactId>
          <configuration>
            <tagBase>https://svn.apache.org/repos/asf/maven/components/tags</tagBase>
          </configuration>
        </plugin>
       <plugin>
          <groupId>org.codehaus.modello</groupId>
          <artifactId>modello-maven-plugin</artifactId>
          <version>1.0-alpha-13</version>
          <executions>
            <execution>
              <id>site-docs</id>
              <phase>pre-site</phase>
              <goals>
                <goal>xdoc</goal>
                <goal>xsd</goal>
              </goals>
            </execution>
            <execution>

              <id>standard</id>
              <goals>
                <goal>java</goal>
                <goal>xpp3-reader</goal>
                <goal>xpp3-writer</goal>
              </goals>
            </execution>
          </executions>
        </plugin>        
      </plugins>
    </pluginManagement>
  </build>
  <modules>
    <module>maven-artifact</module>
    <module>maven-artifact-manager</module>
    <module>maven-artifact-test</module>
    <module>maven-core</module>
    <module>maven-error-diagnostics</module>
    <module>maven-model</module>
    <module>maven-monitor</module>
    <module>maven-plugin-api</module>
    <module>maven-plugin-descriptor</module>
    <module>maven-plugin-parameter-documenter</module>
    <module>maven-plugin-registry</module>
    <module>maven-profile</module>
    <module>maven-project</module>
    <module>maven-reporting</module>
    <module>maven-repository-metadata</module>
    <module>maven-script</module>
    <module>maven-settings</module>
  </modules>
  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>3.8.1</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <properties>
    <mavenVersion>2.0.6</mavenVersion>
  </properties>
  <dependencyManagement>
    <dependencies>
      <!-- Maven -->
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-model</artifactId>
        <version>${mavenVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-project</artifactId>
        <version>${mavenVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.reporting</groupId>
        <artifactId>maven-reporting-api</artifactId>
        <version>${mavenVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-repository-metadata</artifactId>
        <version>${mavenVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-artifact</artifactId>
        <version>${mavenVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-artifact-manager</artifactId>
        <version>${mavenVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-artifact-test</artifactId>
        <version>${mavenVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-settings</artifactId>
        <version>${mavenVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-plugin-parameter-documenter</artifactId>
        <version>${mavenVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-profile</artifactId>
        <version>${mavenVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-plugin-registry</artifactId>
        <version>${mavenVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-plugin-api</artifactId>
        <version>${mavenVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-monitor</artifactId>
        <version>${mavenVersion}</version>
      </dependency>      
      <!-- Plexus -->
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-container-default</artifactId>
        <version>1.0-alpha-9-stable-1</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-utils</artifactId>
        <version>1.4.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.wagon</groupId>
        <artifactId>wagon-provider-api</artifactId>
        <version>1.0-beta-2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.wagon</groupId>
        <artifactId>wagon-ssh</artifactId>
        <version>1.0-beta-2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.wagon</groupId>
        <artifactId>wagon-ssh-external</artifactId>
        <version>1.0-beta-2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.wagon</groupId>
        <artifactId>wagon-file</artifactId>
        <version>1.0-beta-2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.wagon</groupId>
        <artifactId>wagon-http-lightweight</artifactId>
        <version>1.0-beta-2</version>
      </dependency>
      <dependency>
        <groupId>easymock</groupId>
        <artifactId>easymock</artifactId>
        <version>1.2_Java1.3</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>classworlds</groupId>
        <artifactId>classworlds</artifactId>
        <version>1.1</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <distributionManagement>
    <site>
      <id>apache.website</id>
      <url>scp://people.apache.org/www/maven.apache.org/ref/${project.version}/</url>
    </site>
  </distributionManagement>
</project>
