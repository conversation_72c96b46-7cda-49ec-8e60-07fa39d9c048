<!-- 
/**
 * 编码分配属性 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2021-08-07 21:18:50
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <title th:text="${lang.translate('编码分配属性')}">编码分配属性</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/assets/css/admin.css"/>
    <link rel="stylesheet" href="/assets/css/foxnic-web.css">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" th:href="@{/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css}" rel="stylesheet"/>
</head>

<body style="overflow-y: hidden">

<div class="layui-card">
 
    <div class="layui-card-body">
        <div class="layui-form toolbar search-bar">

            <div class="search-unit">
                <span th:text="${lang.translate('占位符')}" class="search-label">占位符</span><span class="search-colon">:</span>
                <input id="code" class="layui-input search-input" type="text" th:placeholder="${lang.translate('请输入占位符')}"/>
            </div>
            <div class="search-unit">
                <span th:text="${lang.translate('编码名称')}" class="search-label">编码名称</span><span class="search-colon">:</span>
                <input id="name" class="layui-input search-input" type="text" th:placeholder="${lang.translate('请输入编码名称')}"/>
            </div>
            <div class="search-unit">
                <span th:text="${lang.translate('属性分类')}" class="search-label">属性分类</span><span class="search-colon">:</span>


                <div id="type" th:data="${enum.toArray('com.dt.platform.constants.enums.common.CodeAttrTypeEnum')}" style="width:140px"></div>
            </div>
            <div class="search-unit">
                <span th:text="${lang.translate('备注')}" class="search-label">备注</span><span class="search-colon">:</span>
                <input id="notes" class="layui-input search-input" type="text" th:placeholder="${lang.translate('请输入备注')}"/>
            </div>

            <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i><span th:text="${lang.translate('搜索')}">搜索</span></button>
            <button th:if="${perm.checkAuth('eam_code_attr:create')}" id="add-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe654;</i><span th:text="${lang.translate('添加')}">添加</span></button>
            <button th:if="${perm.checkAuth('eam_code_attr:delete-by-ids')}" id="delete-button" class="layui-btn icon-btn layui-btn-danger"><i class="layui-icon">&#xe67e;</i><span th:text="${lang.translate('删除')}">删除</span></button>
            
        </div>

        <table class="layui-table" id="data-table" lay-filter="data-table"></table>
    </div>
</div>

<script type="text/javascript" src="/module/global.js"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js"></script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">
    <a th:if="${perm.checkAuth('eam_code_attr:view-form')}" class="layui-btn layui-btn-primary layui-btn-xs" lay-event="edit" th:text="${lang.translate('修改')}">修改</a>
    <a th:if="${perm.checkAuth('eam_code_attr:delete')}" class="layui-btn layui-btn-xs" lay-event="del" th:text="${lang.translate('删除')}">删除</a>
</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var RADIO_TYPE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.CodeAttrTypeEnum')}]];
    var AUTH_PREFIX="eam_code_attr";
</script>

<script src="/business/eam/code_attr/code_attr_list.js"></script>

</body>
</html>