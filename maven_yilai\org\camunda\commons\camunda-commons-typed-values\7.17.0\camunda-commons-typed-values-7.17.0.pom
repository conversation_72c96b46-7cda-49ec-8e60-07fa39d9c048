<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>camunda-database-settings</artifactId>
    <groupId>org.camunda.bpm</groupId>
    <version>7.17.0</version>
    <relativePath>../database/pom.xml</relativePath>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.camunda.commons</groupId>
  <artifactId>camunda-commons-typed-values</artifactId>
  <name>camunda Commons - Typed Values</name>
  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>clirr-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>all</id>
            <phase>verify</phase>
            <goals>
              <goal>check-no-fork</goal>
            </goals>
            <configuration>
              <textOutputFile>${project.build.directory}/clirr-all.txt</textOutputFile>
              <failOnWarning>false</failOnWarning>
              <failOnError>false</failOnError>
              <ignored>
                <difference>
                  <differenceType>8001</differenceType>
                  <className>camundajar/com/sun/activation/**/*</className>
                </difference>
                <difference>
                  <differenceType>8001</differenceType>
                  <className>camundajar/javax/activation/**/*</className>
                </difference>
              </ignored>
            </configuration>
          </execution>
          <execution>
            <id>restrictive</id>
            <phase>verify</phase>
            <goals>
              <goal>check-no-fork</goal>
            </goals>
            <configuration>
              <textOutputFile>${project.build.directory}/clirr-restrictive.txt</textOutputFile>
              <failOnWarning>true</failOnWarning>
              <ignoredDifferencesFile>.clirr-jenkins-ignore.xml</ignoredDifferencesFile>
            </configuration>
          </execution>
        </executions>
        <configuration>
          <comparisonVersion>${camunda.version.old}</comparisonVersion>
          <logResults>true</logResults>
          <excludes>
            <exclude>org/camunda/bpm/engine/impl/**</exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <promoteTransitiveDependencies>true</promoteTransitiveDependencies>
              <artifactSet>
                <includes>
                  <include>com.sun.activation:javax.activation</include>
                </includes>
              </artifactSet>
              <relocations>
                <relocation>
                  <pattern>com.sun.activation</pattern>
                  <shadedPattern>camundajar.com.sun.activation</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>javax.activation</pattern>
                  <shadedPattern>camundajar.javax.activation</shadedPattern>
                </relocation>
              </relocations>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <executions>
          <execution>
            <phase>generate-sources</phase>
            <goals>
              <goal>cleanVersions</goal>
            </goals>
          </execution>
          <execution>
            <id>bundle-manifest</id>
            <phase>process-classes</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <dependencies>
    <dependency>
      <groupId>org.camunda.commons</groupId>
      <artifactId>camunda-commons-utils</artifactId>
      <version>1.10.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.camunda.commons</groupId>
      <artifactId>camunda-commons-logging</artifactId>
      <version>1.10.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.26</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.camunda.bpm</groupId>
        <artifactId>camunda-core-internal-dependencies</artifactId>
        <version>${project.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <properties>
    <camunda.artifact>org.camunda.bpm.engine.variable</camunda.artifact>
    <camunda.osgi.import.additional>!javax.activation</camunda.osgi.import.additional>
  </properties>
</project>
