<!--
/**
 * 巡检点 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2024-03-18 09:23:56
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
    <title th:text="${lang.translate('巡检点')}">巡检点</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 任务 , taskId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 操作时间 , operTime ,typeName=date_input, isHideInSearch=true -->
                    <!-- 巡检结果 , content ,typeName=text_area, isHideInSearch=true -->
                    <!-- 图片 , imageId ,typeName=upload, isHideInSearch=true -->
                    <!-- 巡检点 , pointId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 巡检内容 , pointContent ,typeName=text_input, isHideInSearch=true -->
                    <!-- 巡检路线 , pointRouteId ,typeName=select_box, isHideInSearch=true -->
                    <!-- RFID , pointRfid ,typeName=text_input, isHideInSearch=true -->
                    <!-- 位置 , pointPosId ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('位置')}" class="search-label pointPosId-label">位置</span><span class="search-colon">:</span></div>
                        <div id="pointPosId" th:data="${'/service-eam/eam-inspection-point-pos/query-paged-list'}" style="width:180px" extraParam="{}"></div>
                    </div>
                    <!-- 位置经度 , pointPosLongitude ,typeName=number_input, isHideInSearch=true -->
                    <!-- 位置纬度 , pointPosLatitude ,typeName=number_input, isHideInSearch=true -->
                    <!-- 备注 , pointNotes ,typeName=text_input, isHideInSearch=true -->
                    <!-- 排序 , sort ,typeName=number_input, isHideInSearch=true -->
                    <!-- 操作人 , operId ,typeName=button, isHideInSearch=true -->
                    <!-- 备注 , notes ,typeName=text_input, isHideInSearch=true -->
                    <!-- 巡检方式 , inspMethod ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 修改人ID , updateBy ,typeName=text_input, isHideInSearch=true -->
                    <!-- 选择 , selectedCode ,typeName=text_input, isHideInSearch=true -->
                    <!-- 编码 , pointCode ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('编码')}" class="search-label pointCode-label">编码</span><span class="search-colon">:</span></div>
                        <input id="pointCode" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 巡检点 , pointName ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('巡检点')}" class="search-label pointName-label">巡检点</span><span class="search-colon">:</span></div>
                        <input id="pointName" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 位置详情 , pointPos ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('位置详情')}" class="search-label pointPos-label">位置详情</span><span class="search-colon">:</span></div>
                        <input id="pointPos" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 巡检状态 , pointStatus ,typeName=radio_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('巡检状态')}" class="search-label pointStatus-label">巡检状态</span><span class="search-colon">:</span></div>


                        <div id="pointStatus" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.InspectionTaskPointStatusEnum')}" style="width:180px"></div>
                    </div>
                    <!-- 处理动作 , actionLabel ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('处理动作')}" class="search-label actionLabel-label">处理动作</span><span class="search-colon">:</span></div>
                        <div id="actionLabel" th:data="${'/service-eam/eam-inspection-process-action/query-list'}" style="width:180px" extraParam="{}"></div>
                    </div>


                </div>
            </div>


            <!-- 按钮区域 -->
            <div id="search-area" class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索','','cmp:table.search')}">搜索</span></button>
            </div>
        </div>

        <div id="table-area" style="margin-top: 84px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
        <button th:if="${perm.checkAuth('eam_inspection_task_point:create')}" id="add-button" class="layui-btn icon-btn layui-btn-sm create-new-button " lay-event="create"><i class="layui-icon">&#xe654;</i><span th:text="${lang.translate('新建','','cmp:table.button')}">新建</span></button>
        <button id="import-data"  class="layui-btn icon-btn layui-btn-sm  import-data " lay-event="tool-import-data"><span th:text="${lang.translate('导入','','cmp:table.button')}">导入</span></button>
        <button id="export-data"  class="layui-btn icon-btn layui-btn-sm  export-data " lay-event="tool-export-data"><span th:text="${lang.translate('导出','','cmp:table.button')}">导出</span></button>
    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">

    <button th:if="${perm.checkAuth('eam_inspection_task_point:view-form')}" class="layui-btn layui-btn-primary layui-btn-xs ops-view-button " lay-event="view"  data-id="{{d.id}}"> <span th:text="${lang.translate('查看','','cmp:table.ops')}">查看</span></button>
    <button th:if="${perm.checkAnyAuth('eam_inspection_task_point:update','eam_inspection_task_point:save')}" class="layui-btn layui-btn-primary layui-btn-xs ops-edit-button " lay-event="edit"data-id="{{d.id}}"><span th:text="${lang.translate('修改','','cmp:table.ops')}">修改</span></button>


    <button th:if="${perm.checkAuth('eam_inspection_task_point:delete')}" class="layui-btn layui-btn-xs layui-btn-danger ops-delete-button " lay-event="del" data-id="{{d.id}}"><span th:text="${lang.translate('删除','','cmp:table.ops')}">删除</span></button>


</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
    var RADIO_POINTSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.InspectionTaskPointStatusEnum')}]];
    var RADIO_INSPMETHOD_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.PointInspectMethodEnum')}]];
    var AUTH_PREFIX="eam_inspection_task_point";


</script>

<script th:src="'/business/eam/inspection_task_point/inspection_task_point_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/inspection_task_point/inspection_task_point_list.js?'+${cacheKey}"></script>

</body>
</html>