<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 2011, 2018 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Public License v. 2.0, which is available at
    http://www.eclipse.org/legal/epl-2.0.

    This Source Code may also be made available under the following Secondary
    Licenses when the conditions for such availability set forth in the
    Eclipse Public License v. 2.0 are satisfied: GNU General Public License,
    version 2 with the GNU Classpath Exception, which is available at
    https://www.gnu.org/software/classpath/license.html.

    SPDX-License-Identifier: EPL-2.0 OR GPL-2.0 WITH Classpath-exception-2.0

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.glassfish.jersey</groupId>
        <artifactId>project</artifactId>
        <version>2.30.1</version>
    </parent>

    <groupId>org.glassfish.jersey.media</groupId>
    <artifactId>project</artifactId>
    <packaging>pom</packaging>
    <name>jersey-media</name>

    <description>
        Contains entity media type provider modules.
    </description>

    <modules>
        <module>jaxb</module>
        <module>json-binding</module>
        <module>json-jackson</module>
        <module>json-jackson1</module>
        <module>json-jettison</module>
        <module>json-processing</module>
        <module>moxy</module>
        <module>multipart</module>
        <module>sse</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>org.glassfish.jersey.inject</groupId>
            <artifactId>jersey-hk2</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
