<?xml version="1.0" encoding="UTF-8"?>

<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.apache</groupId>
    <artifactId>apache</artifactId>
    <version>3</version>
    <relativePath>../asf/pom.xml</relativePath>
  </parent>
  <groupId>org.apache.maven</groupId>
  <artifactId>maven-parent</artifactId>
  <version>5</version>
  <packaging>pom</packaging>
  <name>Apache Maven</name>
  <description>
    Maven is a software project management and comprehension tool. Based on the concept of a project object model
    (POM), Maven can manage a project's build, reporting and documentation from a central piece of information.
  </description>
  <url>http://maven.apache.org/</url>
  <issueManagement>
    <system>jira</system>
    <url>http://jira.codehaus.org/browse/MPA</url>
  </issueManagement>
  <ciManagement>
    <system>continuum</system>
    <url>http://maven.zones.apache.org/continuum</url>
    <notifiers>
      <notifier>
        <type>mail</type>
        <configuration>
          <address><EMAIL></address>
        </configuration>
      </notifier>
    </notifiers>
  </ciManagement>
  <inceptionYear>2002</inceptionYear>
  <mailingLists>
    <mailingList>
      <name>Maven Announcements List</name>
      <post><EMAIL></post>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-announce/</archive>
    </mailingList>
    <mailingList>
      <name>Maven Issues List</name>
      <post><EMAIL></post>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-issues/</archive>
    </mailingList>
    <mailingList>
      <name>Maven Notifications List</name>
      <post><EMAIL></post>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-notifications/</archive>
    </mailingList>
  </mailingLists>

  <developers>
    <developer>
      <id>jvanzyl</id>
      <name>Jason van Zyl</name>
      <email><EMAIL></email>
      <organization>ASF</organization>
      <roles>
        <role>PMC Chair</role>
      </roles>
      <timezone>-5</timezone>
    </developer>
    <developer>
      <id>brett</id>
      <name>Brett Porter</name>
      <email><EMAIL></email>
      <organization>ASF</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>+10</timezone>
    </developer>
    <developer>
      <id>evenisse</id>
      <name>Emmanuel Venisse</name>
      <email><EMAIL></email>
      <organization>ASF</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <id>kenney</id>
      <name>Kenney Westerhof</name>
      <email><EMAIL></email>
      <organization>Neonics</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <id>snicoll</id>
      <name>Stephane Nicoll</name>
      <email><EMAIL></email>
      <organization>ASF</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <id>vmassol</id>
      <name>Vincent Massol</name>
      <email><EMAIL></email>
      <organization>ASF</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <id>fgiust</id>
      <name>Fabrizio Giustina</name>
      <email><EMAIL></email>
      <organization>openmind</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <id>epunzalan</id>
      <name>Edwin Punzalan</name>
      <email><EMAIL></email>
      <organization>Mergere</organization>
      <roles>
        <role>Committer</role>
      </roles>
      <timezone>+8</timezone>
    </developer>
    <developer>
      <id>mperham</id>
      <name>Mike Perham</name>
      <email><EMAIL></email>
      <organization>IBM</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>-6</timezone>
    </developer>
    <developer>
      <id>jdcasey</id>
      <name>John Casey</name>
      <email><EMAIL></email>
      <organization>ASF</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>-5</timezone>
    </developer>
    <developer>
      <id>trygvis</id>
      <name>Trygve Laugstol</name>
      <email><EMAIL></email>
      <organization>ASF</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <id>vsiveton</id>
      <name>Vincent Siveton</name>
      <email><EMAIL></email>
      <organization>ASF</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>-5</timezone>
    </developer>
    <developer>
      <id>carlos</id>
      <name>Carlos Sanchez</name>
      <email><EMAIL></email>
      <organization>ASF</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <id>dennisl</id>
      <name>Dennis Lundberg</name>
      <email><EMAIL></email>
      <organization>ASF</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <id>aheritier</id>
      <name>Arnaud Heritier</name>
      <email><EMAIL></email>
      <organization>ASF</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <id>handyande</id>
      <name>Andrew Williams</name>
      <email><EMAIL></email>
      <roles>
        <role>Committer</role>
      </roles>
      <timezone>0</timezone>
    </developer>
    <developer>
      <id>jtolentino</id>
      <name>Ernesto Tolentino Jr.</name>
      <email><EMAIL></email>
      <organization>ASF</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>+8</timezone>
    </developer>
    <developer>
      <id>joakime</id>
      <name>Joakim Erdfelt</name>
      <email><EMAIL></email>
      <organization>ASF</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>-5</timezone>
    </developer>
    <developer>
      <id>jmcconnell</id>
      <name>Jesse McConnell</name>
      <email><EMAIL></email>
      <organization>ASF</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>-6</timezone>
    </developer>
    <developer>
      <id>wsmoak</id>
      <name>Wendy Smoak</name>
      <email><EMAIL></email>
      <roles>
        <role>Committer</role>
      </roles>
      <timezone>-7</timezone>
    </developer>

  </developers>

  <distributionManagement>
    <site>
      <id>apache.website</id>
      <url>scp://people.apache.org/www/maven.apache.org</url>
    </site>
  </distributionManagement>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>        
          <artifactId>maven-release-plugin</artifactId>
          <version>2.0-beta-4</version>          
          <configuration>
            <!-- This element will be overriden by children -->
            <tagBase>https://svn.apache.org/repos/asf/maven/pom/tags</tagBase>
            <useReleaseProfile>false</useReleaseProfile>
            <goals>deploy</goals>
            <arguments>-Prelease</arguments>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

  <profiles>
    <profile>
      <id>ci</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-pmd-plugin</artifactId>
            <executions>
              <execution>
                <goals>
                  <goal>cpd-check</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>reporting</id>
      <reporting>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-report-plugin</artifactId>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-checkstyle-plugin</artifactId>
            <configuration>
              <configLocation>http://svn.apache.org/repos/asf/maven/plugins/trunk/maven-checkstyle-plugin/src/main/resources/config/maven_checks.xml</configLocation>
              <headerLocation>http://svn.apache.org/repos/asf/maven/plugins/trunk/maven-checkstyle-plugin/src/main/resources/config/maven-header.txt</headerLocation>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-pmd-plugin</artifactId>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>cobertura-maven-plugin</artifactId>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>taglist-maven-plugin</artifactId>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-jxr-plugin</artifactId>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <configuration>
              <links>
                <link>http://java.sun.com/j2ee/1.4/docs/api</link>
                <link>http://java.sun.com/j2se/1.5.0/docs/api</link>
                <link>http://jakarta.apache.org/commons/collections/apidocs-COLLECTIONS_3_0/</link>
                <link>http://jakarta.apache.org/commons/dbcp/apidocs/</link>
                <link>http://jakarta.apache.org/commons/fileupload/apidocs/</link>
                <link>http://jakarta.apache.org/commons/httpclient/apidocs/</link>
                <link>http://jakarta.apache.org/commons/logging/apidocs/</link>
                <link>http://jakarta.apache.org/commons/pool/apidocs/</link>
                <link>http://www.junit.org/junit/javadoc/</link>
                <link>http://logging.apache.org/log4j/docs/api/</link>
                <link>http://jakarta.apache.org/regexp/apidocs/</link>
                <link>http://jakarta.apache.org/velocity/api/</link>
              </links>
            </configuration>
          </plugin>
        </plugins>
      </reporting>
    </profile>    
    <profile>
      <id>release</id>
      <build>
        <plugins>
          <!-- We want to sign the artifact, the POM, and all attached artifacts -->
          <plugin>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>1.0-alpha-1</version>
            <configuration>
              <passphrase>${gpg.passphrase}</passphrase>
            </configuration>
            <executions>
              <execution>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <!-- We want to deploy the artifact to a staging location for perusal -->
          <plugin>
            <inherited>true</inherited>
            <artifactId>maven-deploy-plugin</artifactId>
            <version>2.3</version>
            <configuration>
              <altDeploymentRepository>${deploy.altRepository}</altDeploymentRepository>
              <updateReleaseInfo>true</updateReleaseInfo>
            </configuration>
          </plugin>
          <!-- We want to package up license resources in the JARs produced -->
          <plugin>
            <artifactId>maven-remote-resources-plugin</artifactId>
            <version>1.0-alpha-1</version>
            <executions>
              <execution>
                <goals>
                  <goal>process</goal>
                </goals>
                <configuration>
                  <resourceBundles>
                    <resourceBundle>org.apache:apache-jar-resource-bundle:1.0</resourceBundle>
                  </resourceBundles>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <version>2.0.2</version>
            <executions>
              <execution>
                <id>attach-sources</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>2.2</version>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>          
        </plugins>
      </build>
    </profile>                     
  </profiles>

  <scm>
    <connection>scm:svn:https://svn.apache.org/repos/asf/maven/pom/tags/maven-parent-5</connection>
    <developerConnection>scm:svn:https://svn.apache.org/repos/asf/maven/pom/tags/maven-parent-5</developerConnection>
    <url>https://svn.apache.org/repos/asf/maven/pom/tags/maven-parent-5</url>
  </scm>
</project>

