<!--
/**
 * 产品管理 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2024-04-08 07:38:40
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
	<title th:text="${lang.translate('产品管理')}">产品管理</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="opacity:0">

        <input name="id" id="id"  type="hidden"/>

         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-0268-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >

                <!-- text_input : 产品Key ,  productKey -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('产品Key')}">产品Key</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="productKey" id="productKey" name="productKey" th:placeholder="${ lang.translate('请输入'+'产品Key') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- text_input : 产品名称 ,  name -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('产品名称')}">产品名称</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="name" id="name" name="name" th:placeholder="${ lang.translate('请输入'+'产品名称') }" type="text" class="layui-input"    lay-verify="|required"  />
                    </div>
                </div>
            
                <!-- radio_box : 设备定位 ,  locationStatus  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('设备定位')}">设备定位</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input input-type="radio" type="radio" name="locationStatus" lay-filter="locationStatus" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==0)}">
                    </div>
                </div>
            
                <!-- radio_box : 定位方式 ,  locationMethod  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('定位方式')}">定位方式</div></div>
                    <div class="layui-input-block ">
                        <input input-type="radio" type="radio" name="locationMethod" lay-filter="locationMethod" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.eam.IotLocationMethodEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==0)}">
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >

                <!-- select_box : 品类 ,  categoryId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('品类')}">品类</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="categoryId" input-type="select" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_iot_product_category'}" extraParam="{}"></div>
                    </div>
                </div>
            
                <!-- radio_box : 节点类型 ,  productType  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('节点类型')}">节点类型</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input input-type="radio" type="radio" name="productType" lay-filter="productType" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.eam.IotNodeTypeEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==0)}">
                    </div>
                </div>
            
                <!-- radio_box : 透传设备 ,  productTransparent  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('透传设备')}">透传设备</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input input-type="radio" type="radio" name="productTransparent" lay-filter="productTransparent" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.common.YesNoEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==0)}">
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-4050-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column"  style="padding-top: 0px" >
                <!-- upload : 图片 ,  imageId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('图片')}">图片</div></div>
                    <div class="layui-upload layui-input-block ">
                        <input input-type="upload" id="imageId"  name="imageId" lay-filter="imageId" style="display: none">
                        <button type="button" class="layui-btn" id="imageId-button" th:text="${lang.translate('选择附件')}">选择附件</button>
                        <div class="layui-upload-list" id="imageId-file-list"></div>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 75px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消','','form.button')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('iot_product:create','iot_product:update','iot_product:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存','','form.button')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var RADIO_PRODUCTTYPE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.IotNodeTypeEnum')}]];
    var RADIO_PRODUCTTRANSPARENT_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.YesNoEnum')}]];
    var RADIO_LOCATIONSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var RADIO_LOCATIONMETHOD_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.IotLocationMethodEnum')}]];
    var VALIDATE_CONFIG={"locationStatus":{"labelInForm":"设备定位","inputType":"radio_box","required":true},"name":{"labelInForm":"产品名称","inputType":"text_input","required":true},"productTransparent":{"labelInForm":"透传设备","inputType":"radio_box","required":true},"categoryId":{"labelInForm":"品类","inputType":"select_box","required":true},"productType":{"labelInForm":"节点类型","inputType":"radio_box","required":true}};
    var AUTH_PREFIX="iot_product";


</script>



<script th:src="'/business/eam/product/product_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/product/product_form.js?'+${cacheKey}"></script>

</body>
</html>