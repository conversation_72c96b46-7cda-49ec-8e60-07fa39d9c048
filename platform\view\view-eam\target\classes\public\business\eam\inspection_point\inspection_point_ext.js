/**
 * 巡检点 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2023-07-05 14:19:29
 */

layui.config({
    dir: layuiPath,
    base: '/module/'
}).extend({
    xmSelect: 'xm-select/xm-select',
    foxnicUpload: 'upload/foxnic-upload'
})
//
layui.define(['form', 'table', 'util', 'settings', 'admin', 'upload','foxnic','xmSelect','laydate','foxnicUpload','dropdown'],function () {

    var admin = layui.admin,settings = layui.settings,form = layui.form,upload = layui.upload,laydate= layui.laydate,dropdown=layui.dropdown;
    table = layui.table,layer = layui.layer,util = layui.util,fox = layui.foxnic,xmSelect = layui.xmSelect,foxup=layui.foxnicUpload;

    //模块基础路径
    const moduleURL="/service-eam/eam-inspection-point";

    var timestamp = Date.parse(new Date());
    //列表页的扩展
    var list={
        /**
         * 列表页初始化前调用
         * */
        beforeInit:function () {
            console.log("list:beforeInit");
        },
        /**
         * 按事件名称移除表格按钮栏的按钮
         * */
        removeOperationButtonByEvent(event) {
            var template=$("#tableOperationTemplate");
            var content=template.text();
            content=content.split("\n");
            var buttons=[]
            for (let i = 0; i < content.length ; i++) {
                if(content[i] && content[i].indexOf("lay-event=\""+event+"\"")==-1) {
                    buttons.push(content[i]);
                }
            }
            template.text(buttons.join("\n"))
        },
        /**
         * 表格渲染前调用
         * @param cfg 表格配置参数
         * */
        beforeTableRender:function (cfg){
            cfg.cellMinWidth=160;;
        },
        /**
         * 表格渲染后调用
         * */
        afterTableRender :function (){

        },
        afterSearchInputReady: function() {
            console.log("list:afterSearchInputReady");
        },
        /**
         * 对话框打开之前调用，如果返回 null 则不打开对话框
         * */
        beforeDialog:function (param){
            //param.title="覆盖对话框标题";
            return param;
        },
        /**
         * 对话框回调，表单域以及按钮 会自动改变为选中的值，此处处理额外的逻辑即可
         * */
        afterDialog:function (param,result) {
            console.log('dialog',param,result);
        },
        /**
         * 当下拉框别选择后触发
         * */
        onSelectBoxChanged:function(id,selected,changes,isAdd) {
            console.log('onSelectBoxChanged',id,selected,changes,isAdd);
        },
        /**
         * 当日期选择组件选择后触发
         * */
        onDatePickerChanged:function(id,value, date, endDate) {
            console.log('onDatePickerChanged',id,value, date, endDate);
        },
        /**
         * 查询前调用
         * @param conditions 复合查询条件
         * @param param 请求参数
         * @param location 调用的代码位置
         * */
        beforeQuery:function (conditions,param,location) {
            console.log('beforeQuery',conditions,param,location);
            return true;
        },
        /**
         * 查询结果渲染后调用
         * */
        afterQuery : function (data) {

        },
        /**
         * 单行数据刷新后调用
         * */
        afterRefreshRowData: function (data,remote,context) {

        },
        /**
         * 进一步转换 list 数据
         * */
        templet:function (field,value,r) {

            if(field=="itemCount"){
                if(value){
                    return value
                }else{
                    return 0;
                }
            }

            if(field=="assetId"){
                console.log("value",value);
                console.log("r",r)
                var res="";
                if(r.asset){
                    res="设备:"+r.asset.name+",型号:"+r.asset.model+",编号:"+r.asset.assetCode;
                }
                return res;
            }


            if(field=="itemDisableCount"){
                if(value){
                    return value
                }else{
                    return 0;
                }
            }

            if(value==null) return "";
            return value;
        },
        /**
         * 表单页面打开时，追加更多的参数信息
         * */
        makeFormQueryString:function(data,queryString,action) {
            return queryString;
        },
        /**
         * 在新建或编辑窗口打开前调用，若返回 false 则不继续执行后续操作
         * */
        beforeEdit:function (data) {
            console.log('beforeEdit',data);
            return true;
        },
        /**
         * 单行删除前调用，若返回false则不执行后续操作
         * */
        beforeSingleDelete:function (data) {
            console.log('beforeSingleDelete',data);
            return true;
        },
        afterSingleDelete:function (data){
            console.log('beforeSingleDelete',data);
            return true;
        },
        /**
         * 批量删除前调用，若返回false则不执行后续操作
         * */
        beforeBatchDelete:function (selected) {
            console.log('beforeBatchDelete',selected);
            return true;
        },
        /**
         * 批量删除后调用，若返回false则不执行后续操作
         * */
        afterBatchDelete:function (data) {
            console.log('afterBatchDelete',data);
            return true;
        },
        /**
         * 工具栏按钮事件前调用，如果返回 false 则不执行后续代码
         * */
        beforeToolBarButtonEvent:function (selected,obj) {
            console.log('beforeToolBarButtonEvent',selected,obj);
            return true;
        },
        /**
         * 列表操作栏按钮事件前调用，如果返回 false 则不执行后续代码
         * */
        beforeRowOperationEvent:function (data,obj) {
            console.log('beforeRowOperationEvent',data,obj);
            return true;
        },
        /**
         * 表格右侧操作列更多按钮事件
         * */
        moreAction:function (menu,data, it){
            console.log('moreAction',menu,data,it);
        },
        exportData:function(selected,obj){
            console.log(selected,obj);
            //   var ps={searchField: "$composite", searchValue: JSON.stringify(value)};
            function getSelectedValue(id,prop) { var xm=xmSelect.get(id,true); return xm==null ? null : xm.getValue(prop);}
            var value = {};
            value.code={ inputType:"button",value: $("#code").val() ,fuzzy: true,splitValue:false,valuePrefix:"",valueSuffix:"" };
            value.name={ inputType:"button",value: $("#name").val()};
            value.status={ inputType:"select_box", value: getSelectedValue("#status","value"), label:getSelectedValue("#status","nameStr") };
            value.posId={ inputType:"select_box", value: getSelectedValue("#posId","value") ,fillBy:["inspectionPointPos"]  , label:getSelectedValue("#posId","nameStr") };
            var ps={searchField:"$composite"};
            ps.searchValue=JSON.stringify(value);
            var downloadUrl=moduleURL+"/export-excel";
            fox.submit(downloadUrl,ps,"post",function(){
                console.log("execute finish");
            });
        },
        showRwm:function(data,it){

            var textValue = data.code;
            var type = "auto";
            var text = data.code
            layer.open({
                type: 1
                , offset: type
                ,title:"二维码"
                , area: ["60%", "60%"]
                , id: 'layerAsset_' + type //防止重复弹出
                , content: '<div style="padding: 20px 100px;">' + text + '</div> ' +
                    '<div style="margin-left:20px;margin-right: 20px;margin-top:20px;margin-bottom: 20px;" id="assetCodePic"></div>'
                , btn: '关闭'
                , btnAlign: 'c' //按钮居中
                , shade: 0 //不显示遮罩
                , success: function (layero) {
                    console.log("open success",layero);
                    $("#assetCodePic").qrcode({
                        render: "canvas",
                        wihth: 250,
                        height: 250,
                        text: textValue
                    });
                }
                ,yes: function(){
                    layer.closeAll();
                }
            });
        },
        importData:function(selected,obj){
            var q="?code=eam_asset_insepect_point&importApi="+moduleURL+"/import-excel";
            var index = admin.popupCenter({
                title: "数据导入",
                resize: false,
                id: 'assetDataImport',
                area: ["60%", "50%"],
                type: 2,
                content: '/business/common/tpl_file/import_form.html'+q,
            });
        },
        modifyItem:function(selected,obj){
            if(selected.length==0){
                alert("请勾选检查点")
                return 1
            }
            var ids=""
            for(var i=0;i<selected.length;i++){
                if(i==0){
                    ids=selected[i];
                }else{
                    ids=ids+","+selected[i]
                }
            }
            var index = admin.popupCenter({
                title: "批量修改检查项",
                resize: false,
                id: 'eam-inspection-point-modify',
                area: ["85%", "85%"],
                type: 2,
                content: '/business/eam/inspection_point/modify_check_item.html?pointIds='+ids,
            });
        },
        /**
         * 末尾执行
         */
        ending:function() {

        }
    }

    //表单页的扩展
    var form={
        /**
         * 表单初始化前调用 , 并传入表单数据
         * */
        beforeInit:function (action,data) {
            //获取参数，并调整下拉框查询用的URL
            //var companyId=admin.getTempData("companyId");
            //fox.setSelectBoxUrl("employeeId","/service-hrm/hrm-employee/query-paged-list?companyId="+companyId);
            console.log("form:beforeInit")
          //  $("#code").attr("disabled","disabled").css("background-color","#e6e6e6");
          //  $("#code").attr("placeholder","自动填充")
        },
        /**
         * 窗口调节前
         * */
        beforeAdjustPopup:function (arg) {
            console.log('beforeAdjustPopup');
            return true;
        },
        /**
         * 表单数据填充前
         * */
        beforeDataFill:function (data) {
            console.log('beforeDataFill',data);
        },
        /**
         * 表单数据填充后
         * */
        afterDataFill:function (data) {
            console.log('afterDataFill',data);
        },
        /**
         * 对话框打开之前调用，如果返回 null 则不打开对话框
         * */
        beforeDialog:function (param){
            //param.title="覆盖对话框标题";
            return param;
        },
        /**
         * 对话框回调，表单域以及按钮 会自动改变为选中的值，此处处理额外的逻辑即可
         * */
        afterDialog:function (param,result) {
            console.log('dialog',param,result);
        },
        /**
         * 当下拉框别选择后触发
         * */
        onSelectBoxChanged:function(id,selected,changes,isAdd) {
            console.log('onSelectBoxChanged',id,selected,changes,isAdd);
        },
        /**
         * 当日期选择组件选择后触发
         * */
        onDatePickerChanged:function(id,value, date, endDate) {
            console.log('onDatePickerChanged',id,value, date, endDate);
        },
        onRadioBoxChanged:function(id,data,checked) {
            console.log('onRadioChanged',id,data,checked);
        },
        onCheckBoxChanged:function(id,data,checked) {
            console.log('onCheckBoxChanged',id,data,checked);
        },

        /**
         * 在流程提交前处理表单数据
         * */
        processFormData4Bpm:function(processInstanceId,param,callback) {
            // 设置流程变量，并通过回调返回
            var variables={};
            // 此回调是必须的，否则流程提交会被中断
            callback(variables);
        },
        /**
         * 数据提交前，如果返回 false，停止后续步骤的执行
         * */
        beforeSubmit:function (data) {
            data.selectedCode=timestamp;
            console.log("beforeSubmit",data);
            return true;
        },
        /**
         * 数据提交后窗口关闭前，如果返回 false，停止后续步骤的执行
         * */
        betweenFormSubmitAndClose:function (param,result) {
            console.log("betweenFormSubmitAndClose",result);
            return true;
        },
        /**
         * 数据提交后执行
         * */
        afterSubmit:function (param,result) {
            console.log("afterSubmitt",param,result);
        },

        /**
         *  加载 巡检项
         */
        checkSelectList:function (ifr,win,data) {
            // debugger
            console.log("checkSelectList",ifr,data);
            //设置 iframe 高度
            ifr.height("400px");
            var selectCode=timestamp;
            var formAction=admin.getTempData('eam-inspection-point-form-data-form-action');
            var pageType=formAction;
            var ownerId="";
            if(formAction=="create"){
                ownerId=timestamp;
            }else{
                ownerId=data.id;
            }
            //设置地址
            win.location="/business/eam/check_item/check_item_selected_list.html?ownerId="+ownerId+"&pageType="+pageType+"&selectCode="+selectCode
        },
        /**
         * 文件上传组件回调
         *  event 类型包括：
         *  afterPreview ：文件选择后，未上传前触发；
         *  afterUpload ：文件上传后触发
         *  beforeRemove ：文件删除前触发
         *  afterRemove ：文件删除后触发
         * */
        onUploadEvent: function(e) {
            console.log("onUploadEvent",e);
        },
         selectBoxDataTransform:function(label,curItem,item,data,i){
            if(label&&label=="assetId"){
                var name=data[i].assetCode
                if(data[i].name){
                    name=name+"-"+data[i].name
                }
                curItem.name=name;
                return curItem;
            }
            return curItem;

        },
        /**
         * 末尾执行
         */
        ending:function() {

        }
    }
    //
    window.pageExt={form:form,list:list};
});