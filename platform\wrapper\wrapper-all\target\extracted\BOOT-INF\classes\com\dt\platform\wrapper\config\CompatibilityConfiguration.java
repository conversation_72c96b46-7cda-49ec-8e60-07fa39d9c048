package com.dt.platform.wrapper.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.support.PropertySourcesPlaceholderConfigurer;
import org.ssssssss.magicapi.model.RequestEntity;

/**
 * 兼容性配置类
 * 用于解决依赖缺失问题
 */
@Configuration
public class CompatibilityConfiguration {

    /**
     * 创建UReport PropertyPlaceholderConfigurer的替代品
     * 当UReport相关类不存在时使用标准的PropertySourcesPlaceholderConfigurer
     */
    @Bean(name = "UReportPropertyPlaceholderConfigurer")
    @ConditionalOnMissingClass("com.bstek.ureport.UReportPropertyPlaceholderConfigurer")
    @Primary
    public PropertySourcesPlaceholderConfigurer ureportPropertyPlaceholderConfigurer() {
        return new PropertySourcesPlaceholderConfigurer();
    }

    /**
     * 创建MagicApi ResultProvider的替代品
     * 当原始ResultProvider类不存在时使用这个实现
     */
    @Bean(name = "magicApiCustomJsonValueProvider")
    @ConditionalOnMissingClass("org.ssssssss.magicapi.core.interceptor.ResultProvider")
    @Primary
    public Object magicApiCustomJsonValueProvider() {
        return new Object() {
            public Object buildResult(RequestEntity requestEntity, int code, String message, Object data) {
                // 简单的结果构建实现
                java.util.Map<String, Object> result = new java.util.HashMap<>();
                result.put("code", code);
                result.put("message", message);
                result.put("data", data);
                return result;
            }
        };
    }
}
