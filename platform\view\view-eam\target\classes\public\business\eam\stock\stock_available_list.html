<!--
/**
 * 资产库存 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2021-11-22 13:52:29
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <title th:text="${lang.translate('资产库存')}">资产库存</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" th:href="@{/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css}" rel="stylesheet"/>
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon"> <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
    <style>
    </style>
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 库存所属 , ownerCode ,typeName=text_input, isHideInSearch=true -->
                    <!-- 流程 , procId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 办理状态 , status ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('办理状态')}" class="search-label status-label">办理状态</span><span class="search-colon">:</span></div>
                        <div id="status" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.AssetHandleStatusEnum')}" style="width:140px" extraParam="{}"></div>
                    </div>
                    <!-- 所属公司 , ownCompanyId ,typeName=button, isHideInSearch=true -->
                    <!-- 仓库 , warehouseId ,typeName=select_box, isHideInSearch=true -->
                    <!-- 总金额 , amount ,typeName=number_input, isHideInSearch=true -->
                    <!-- 管理人 , managerId ,typeName=button, isHideInSearch=true -->
                    <!-- 附件 , attachId ,typeName=upload, isHideInSearch=true -->
                    <!-- 制单人 , originatorId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 选择数据 , selectedCode ,typeName=text_input, isHideInSearch=true -->
                    <!-- 变更类型 , chsType ,typeName=text_input, isHideInSearch=true -->
                    <!-- 变更状态 , chsStatus ,typeName=text_input, isHideInSearch=true -->
                    <!-- 变更版本号 , chsVersion ,typeName=text_input, isHideInSearch=true -->
                    <!-- 变更ID , changeInstanceId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 流程概要 , summary ,typeName=text_input, isHideInSearch=true -->
                    <!-- 最后审批人账户ID , latestApproverId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 最后审批人姓名 , latestApproverName ,typeName=text_input, isHideInSearch=true -->
                    <!-- 下一节点审批人 , nextApproverIds ,typeName=text_input, isHideInSearch=true -->
                    <!-- 下一个审批节点审批人姓名 , nextApproverNames ,typeName=text_input, isHideInSearch=true -->
                    <!-- 审批意见 , approvalOpinion ,typeName=text_input, isHideInSearch=true -->
                    <!-- 单据名称 , stockName ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('单据名称')}" class="search-label stockName-label">单据名称</span><span class="search-colon">:</span></div>
                        <input id="stockName" class="layui-input search-input" style="width: 140px" type="text" />
                    </div>
                    <!-- 业务编号 , businessCode ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('业务编号')}" class="search-label businessCode-label">业务编号</span><span class="search-colon">:</span></div>
                        <input id="businessCode" class="layui-input search-input" style="width: 140px" type="text" />
                    </div>
                    <!-- 批次号 , stockBatchCode ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('批次号')}" class="search-label stockBatchCode-label">批次号</span><span class="search-colon">:</span></div>
                        <input id="stockBatchCode" class="layui-input search-input" style="width: 140px" type="text" />
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 供应商 , supplierId ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('供应商')}" class="search-label supplierId-label">供应商</span><span class="search-colon">:</span></div>
                        <div id="supplierId" th:data="${'/service-eam/eam-supplier/query-list'}" style="width:140px" extraParam="{}"></div>
                    </div>
                    <!-- 来源 , sourceId ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('来源')}" class="search-label sourceId-label">来源</span><span class="search-colon">:</span></div>
                        <div id="sourceId" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_source'}" style="width:140px" extraParam="{}"></div>
                    </div>
                    <!-- 备注 , stockNotes ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('备注')}" class="search-label stockNotes-label">备注</span><span class="search-colon">:</span></div>
                        <input id="stockNotes" class="layui-input search-input" style="width: 140px" type="text" />
                    </div>
                    <!-- 接收人 , receiverUserName ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('接收人')}" class="search-label receiverUserName-label">接收人</span><span class="search-colon">:</span></div>
                        <input id="receiverUserName" class="layui-input search-input" style="width: 140px" type="text" />
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 购置日期 , purchaseDate ,typeName=date_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('购置日期')}" class="search-label purchaseDate-label">购置日期</span><span class="search-colon">:</span></div>
                            <input type="text" id="purchaseDate-begin" style="width: 140px" lay-verify="date" th:placeholder="${lang.translate('开始日期')}" autocomplete="off" class="layui-input search-input search-date-input"  readonly >
                            <span class="search-dash">-</span>
                            <input type="text" id="purchaseDate-end"  style="width: 140px"  lay-verify="date" th:placeholder="${lang.translate('结束日期')}" autocomplete="off" class="layui-input search-input search-date-input" readonly>
                    </div>
                    <!-- 业务时间 , businessDate ,typeName=date_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('业务时间')}" class="search-label businessDate-label">业务时间</span><span class="search-colon">:</span></div>
                            <input type="text" id="businessDate-begin" style="width: 140px" lay-verify="date" th:placeholder="${lang.translate('开始日期')}" autocomplete="off" class="layui-input search-input search-date-input"  readonly >
                            <span class="search-dash">-</span>
                            <input type="text" id="businessDate-end"  style="width: 140px"  lay-verify="date" th:placeholder="${lang.translate('结束日期')}" autocomplete="off" class="layui-input search-input search-date-input" readonly>
                    </div>


                </div>
            </div>


            <!-- 按钮区域 -->
            <div class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索')}">搜索</span></button>
                <button id="search-button-advance" class="layui-btn layui-btn-primary icon-btn search-button-advance"><i class="layui-icon">&#xe671;</i><span th:text="${lang.translate('更多')}">更多</span></button>
            </div>
        </div>

        <div style="margin-top: 84px ">
            <table class="layui-table" th:id="(${tableId})"  lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>

<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
        <button th:if="${stockCreateBtn}" id="add-button" class="layui-btn icon-btn layui-btn-sm create-new-button " lay-event="create"><i class="layui-icon">&#xe654;</i><span th:text="${lang.translate('新建')}">新建</span></button>
    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">

    <button th:if="${stockViewFormBtn}" class="layui-btn layui-btn-primary layui-btn-xs ops-view-button " lay-event="view" th:text="${lang.translate('查看')}" data-id="{{d.id}}">查看</button>
    <button th:if="${stockModifyBtn}"   class="layui-btn layui-btn-primary layui-btn-xs ops-edit-button " lay-event="edit" th:text="${lang.translate('修改')}" data-id="{{d.id}}">修改</button>
    <button th:if="${stockDeleteBtn}"   class="layui-btn layui-btn-xs layui-btn-danger ops-delete-button " lay-event="del" th:text="${lang.translate('删除')}" data-id="{{d.id}}">删除</button>

    <button th:if="${forApprovalBtn}"   class="layui-btn layui-btn-xs for-approval-button " lay-event="for-approval" th:text="${lang.translate('送审')}" data-id="{{d.id}}">送审</button>
    <button th:if="${confirmDataBtn}"   class="layui-btn layui-btn-xs confirm-data-button " lay-event="confirm-data" th:text="${lang.translate('确认')}" data-id="{{d.id}}">确认</button>
    <button th:if="${revokeDataBtn}"    class="layui-btn layui-btn-xs revoke-data-button " lay-event="revoke-data" th:text="${lang.translate('撤销')}"  data-id="{{d.id}}">撤销</button>

</script>


<script th:inline="javascript">

    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];

    var AUTH_PREFIX= [[${authPrefix}]];

    var TABLE_ID=[[${tableId}]];

    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetHandleStatusEnum')}]];
    // 是否需要审批
    var APPROVAL_REQUIRED = [[${approvalRequired}]] ;
    // 页面类型
    var OWNER_CODE = [[${ownerCode}]] ;

    var STOCK_TYPE = [[${stockType}]] ;

    var OPER_TYPE = [[${operType}]] ;

</script>

<script th:src="'/business/eam/stock/stock_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/stock/stock_list.js?'+${cacheKey}"></script>

</body>
</html>
