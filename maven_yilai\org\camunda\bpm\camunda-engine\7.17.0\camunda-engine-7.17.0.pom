<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>camunda-database-settings</artifactId>
    <groupId>org.camunda.bpm</groupId>
    <version>7.17.0</version>
    <relativePath>../database/pom.xml</relativePath>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>camunda-engine</artifactId>
  <name>Camunda Platform - engine</name>
  <build>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
      </resource>
      <resource>
        <filtering>true</filtering>
        <directory>src/main/resources</directory>
        <includes>
          <include>org/camunda/bpm/engine/product-info.properties</include>
        </includes>
      </resource>
    </resources>
    <testResources>
      <testResource>
        <directory>src/test/resources</directory>
      </testResource>
      <testResource>
        <filtering>true</filtering>
        <directory>src/test/resources</directory>
        <includes>
          <include>**/camunda.cfg.xml</include>
          <include>**/*camunda.cfg.xml</include>
          <include>**/camunda.cfg.*.xml</include>
          <include>testconfig.properties</include>
          <include>**/testcontainers.properties</include>
        </includes>
      </testResource>
    </testResources>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.eclipse.m2e</groupId>
          <artifactId>lifecycle-mapping</artifactId>
          <version>1.0.0</version>
          <configuration>
            <lifecycleMappingMetadata>
              <pluginExecutions>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <versionRange>[2.1.0,)</versionRange>
                    <goals>
                      <goal>cleanVersions</goal>
                      <goal>manifest</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore />
                  </action>
                </pluginExecution>
              </pluginExecutions>
            </lifecycleMappingMetadata>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <failIfNoTests>false</failIfNoTests>
          <trimStackTrace>false</trimStackTrace>
          <redirectTestOutputToFile>true</redirectTestOutputToFile>
          <includes>
            <include>%regex[.*(${test.includes}).*Test.*.class]</include>
          </includes>
          <excludes>
            <exclude>**/*TestCase.java</exclude>
            <exclude>%regex[.*(${test.excludes}).*Test.*.class]</exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <executions>
          <execution>
            <phase>generate-sources</phase>
            <goals>
              <goal>cleanVersions</goal>
            </goals>
          </execution>
          <execution>
            <id>bundle-manifest</id>
            <phase>process-classes</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>test-jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <configuration>
          <promoteTransitiveDependencies>false</promoteTransitiveDependencies>
          <createSourcesJar>true</createSourcesJar>
          <createDependencyReducedPom>true</createDependencyReducedPom>
          <keepDependenciesWithProvidedScope>true</keepDependenciesWithProvidedScope>
          <artifactSet>
            <excludes>
              <exclude>org.camunda.bpm.model:*</exclude>
              <exclude>org.camunda.bpm.dmn:*</exclude>
              <exclude>org.camunda.commons:*</exclude>
              <exclude>org.camunda.connect:*</exclude>
              <exclude>org.camunda.feel:*</exclude>
              <exclude>org.springframework:*</exclude>
              <exclude>org.slf4j:*</exclude>
              <exclude>org.apache.commons:*</exclude>
              <exclude>com.sun.mail:*</exclude>
              <exclude>javax.activation:*</exclude>
              <exclude>org.mybatis:mybatis:*</exclude>
              <exclude>commons-logging:*</exclude>
              <exclude>joda-time:*</exclude>
            </excludes>
          </artifactSet>
          <relocations>
            <relocation>
              <pattern>com.google.gson</pattern>
              <shadedPattern>camundajar.impl.com.google.gson</shadedPattern>
            </relocation>
          </relocations>
          <transformers>
            <transformer />
          </transformers>
          <shadeTestJar>true</shadeTestJar>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>distro</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-source-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-sources</id>
                <phase>package</phase>
                <goals>
                  <goal>jar-no-fork</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>database</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>exec-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>database-test-drop-schema-before-test</id>
                <phase>process-test-classes</phase>
                <goals>
                  <goal>java</goal>
                </goals>
              </execution>
              <execution>
                <id>database-test-drop-schema-after-test</id>
                <phase>prepare-package</phase>
                <goals>
                  <goal>java</goal>
                </goals>
              </execution>
            </executions>
            <configuration>
              <mainClass>org.camunda.bpm.engine.impl.db.DbSchemaDrop</mainClass>
              <classpathScope>test</classpathScope>
              <cleanupDaemonThreads>false</cleanupDaemonThreads>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>cfghistoryaudit</id>
      <properties>
        <history.level>audit</history.level>
      </properties>
    </profile>
    <profile>
      <id>cfghistoryactivity</id>
      <properties>
        <history.level>activity</history.level>
      </properties>
    </profile>
    <profile>
      <id>cfghistorynone</id>
      <properties>
        <history.level>none</history.level>
      </properties>
    </profile>
    <profile>
      <id>cfgAuthorizationCheckRevokesAlways</id>
      <properties>
        <authorizationCheckRevokes>always</authorizationCheckRevokes>
      </properties>
    </profile>
    <profile>
      <id>cfgJdbcBatchProcessingOff</id>
      <properties>
        <jdbcBatchProcessing>false</jdbcBatchProcessing>
      </properties>
    </profile>
    <profile>
      <id>h2-in-memory</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <failIfNoTests>false</failIfNoTests>
              <trimStackTrace>false</trimStackTrace>
              <redirectTestOutputToFile>true</redirectTestOutputToFile>
              <reuseForks>true</reuseForks>
              <systemPropertyVariables>
                <jdbcUrl>jdbc:h2:mem:camunda_${surefire.forkNumber};DB_CLOSE_DELAY=1000</jdbcUrl>
              </systemPropertyVariables>
            </configuration>
          </plugin>
        </plugins>
      </build>
      <properties>
        <forkCount>0.5C</forkCount>
      </properties>
    </profile>
    <profile>
      <id>mariadb-galera</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <failIfNoTests>false</failIfNoTests>
              <trimStackTrace>false</trimStackTrace>
              <redirectTestOutputToFile>true</redirectTestOutputToFile>
              <excludes>
                <exclude>**/JdbcStatementTimeoutTest.java</exclude>
                <exclude>**/CompetingMessageCorrelationTest.java</exclude>
                <exclude>**/ConcurrentDeploymentTest.java</exclude>
                <exclude>**/DatabaseFlushTest.java</exclude>
                <exclude>**/*TestCase.java</exclude>
              </excludes>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>mysql</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <failIfNoTests>false</failIfNoTests>
              <trimStackTrace>false</trimStackTrace>
              <redirectTestOutputToFile>true</redirectTestOutputToFile>
              <excludes>
                <exclude>**/CompetingMessageCorrelationTest.java</exclude>
              </excludes>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>mariadb</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <failIfNoTests>false</failIfNoTests>
              <trimStackTrace>false</trimStackTrace>
              <redirectTestOutputToFile>true</redirectTestOutputToFile>
              <excludes>
                <exclude>**/CompetingMessageCorrelationTest.java</exclude>
              </excludes>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>cockroachdb</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <failIfNoTests>false</failIfNoTests>
              <trimStackTrace>false</trimStackTrace>
              <redirectTestOutputToFile>true</redirectTestOutputToFile>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>check-api-compatibility</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <skipTests>true</skipTests>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>clirr-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>all</id>
                <phase>verify</phase>
                <goals>
                  <goal>check-no-fork</goal>
                </goals>
                <configuration>
                  <textOutputFile>${project.build.directory}/clirr-all.txt</textOutputFile>
                  <failOnWarning>false</failOnWarning>
                  <failOnError>false</failOnError>
                </configuration>
              </execution>
              <execution>
                <id>restrictive</id>
                <phase>verify</phase>
                <goals>
                  <goal>check-no-fork</goal>
                </goals>
                <configuration>
                  <textOutputFile>${project.build.directory}/clirr-restrictive.txt</textOutputFile>
                  <failOnWarning>true</failOnWarning>
                  <ignoredDifferencesFile>.clirr-jenkins-ignore.xml</ignoredDifferencesFile>
                </configuration>
              </execution>
            </executions>
            <configuration>
              <comparisonVersion>${camunda.version.old}</comparisonVersion>
              <logResults>true</logResults>
              <excludes>
                <exclude>org/camunda/bpm/*/impl/**</exclude>
                <exclude>org/camunda/bpm/engine/test/ProcessEngineRule</exclude>
                <exclude>camundajar/**/*</exclude>
              </excludes>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>wls-compatibility</id>
      <dependencies>
        <dependency>
          <groupId>joda-time</groupId>
          <artifactId>joda-time</artifactId>
          <version>1.2.1</version>
        </dependency>
      </dependencies>
    </profile>
    <profile>
      <id>check-plugins</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-antrun-plugin</artifactId>
            <executions>
              <execution>
                <id>copy-camunda-cfg</id>
                <phase>process-test-classes</phase>
                <goals>
                  <goal>run</goal>
                </goals>
                <configuration>
                  <target>
                    <echo />
                    <copy />
                  </target>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <argLine>-Xmx968m</argLine>
            </configuration>
          </plugin>
        </plugins>
      </build>
      <dependencies>
        <dependency>
          <groupId>org.camunda.bpm</groupId>
          <artifactId>camunda-engine-plugin-spin</artifactId>
        </dependency>
        <dependency>
          <groupId>org.camunda.spin</groupId>
          <artifactId>camunda-spin-dataformat-all</artifactId>
        </dependency>
        <dependency>
          <groupId>org.camunda.bpm</groupId>
          <artifactId>camunda-engine-plugin-connect</artifactId>
        </dependency>
        <dependency>
          <groupId>org.camunda.connect</groupId>
          <artifactId>camunda-connect-connectors-all</artifactId>
        </dependency>
      </dependencies>
    </profile>
    <profile>
      <id>db-table-prefix</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-antrun-plugin</artifactId>
            <executions>
              <execution>
                <id>database-test-create-schema</id>
                <phase>process-test-classes</phase>
                <goals>
                  <goal>run</goal>
                </goals>
                <configuration>
                  <target>
                    <copy />
                  </target>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <excludes>
                <exclude>**/*TestCase.java</exclude>
                <exclude>**/DeploymentAwareJobExecutorTest.java</exclude>
                <exclude>**/DatabaseTablePrefixTest.java</exclude>
                <exclude>**/DeploymentTest.java</exclude>
                <exclude>**/DeploymentAutoHistoryTest.java</exclude>
                <exclude>**/CustomHistoryLevelUserOperationLogTest.java</exclude>
                <exclude>**/CustomHistoryLevelWithoutUserOperationLogTest.java</exclude>
                <exclude>**/CustomHistoryLevelIncidentTest.java</exclude>
                <exclude>**/CustomHistoryLevelIdentityLinkTest.java</exclude>
                <exclude>**/removaltime/cleanup/HistoryCleanupScheduler*Test.java</exclude>
                <exclude>**/HistoryCleanupDisabledOnBootstrapTest.java</exclude>
                <exclude>**/LoginAttemptsTest.java</exclude>
                <exclude>**/ConcurrentHistoryCleanupUpdateOfFailingJobTest.java</exclude>
                <exclude>**/TelemetryTaskWorkerMetricsTest.java</exclude>
              </excludes>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>java15</id>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <artifactId>maven-surefire-plugin</artifactId>
              <configuration>
                <excludes>
                  <exclude>**/*NashornTest.java</exclude>
                </excludes>
              </configuration>
            </plugin>
          </plugins>
        </pluginManagement>
      </build>
    </profile>
    <profile>
      <id>testBpmn</id>
      <properties>
        <test.includes>bpmn</test.includes>
      </properties>
    </profile>
    <profile>
      <id>testCmmn</id>
      <properties>
        <test.includes>cmmn</test.includes>
      </properties>
    </profile>
    <profile>
      <id>testDmn</id>
      <properties>
        <test.includes>dmn</test.includes>
      </properties>
    </profile>
    <profile>
      <id>testBpmnCmmn</id>
      <properties>
        <test.includes>bpmn|cmmn</test.includes>
      </properties>
    </profile>
    <profile>
      <id>testExceptBpmn</id>
      <properties>
        <test.excludes>bpmn</test.excludes>
      </properties>
    </profile>
    <profile>
      <id>testExceptCmmn</id>
      <properties>
        <test.excludes>cmmn</test.excludes>
      </properties>
    </profile>
    <profile>
      <id>testExceptDmn</id>
      <properties>
        <test.excludes>dmn</test.excludes>
      </properties>
    </profile>
    <profile>
      <id>testExceptBpmnCmmn</id>
      <properties>
        <test.excludes>bpmn|cmmn</test.excludes>
      </properties>
    </profile>
  </profiles>
  <dependencies>
    <dependency>
      <groupId>org.camunda.bpm.model</groupId>
      <artifactId>camunda-bpmn-model</artifactId>
      <version>7.17.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.camunda.bpm.model</groupId>
      <artifactId>camunda-cmmn-model</artifactId>
      <version>7.17.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.camunda.bpm.dmn</groupId>
      <artifactId>camunda-engine-dmn</artifactId>
      <version>7.17.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>juel-api</artifactId>
          <groupId>de.odysseus.juel</groupId>
        </exclusion>
        <exclusion>
          <artifactId>juel-spi</artifactId>
          <groupId>de.odysseus.juel</groupId>
        </exclusion>
        <exclusion>
          <artifactId>juel-impl</artifactId>
          <groupId>de.odysseus.juel</groupId>
        </exclusion>
        <exclusion>
          <artifactId>fastparse_2.13</artifactId>
          <groupId>com.lihaoyi</groupId>
        </exclusion>
        <exclusion>
          <artifactId>sourcecode_2.13</artifactId>
          <groupId>com.lihaoyi</groupId>
        </exclusion>
        <exclusion>
          <artifactId>geny_2.13</artifactId>
          <groupId>com.lihaoyi</groupId>
        </exclusion>
        <exclusion>
          <artifactId>scala-library</artifactId>
          <groupId>org.scala-lang</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.camunda.commons</groupId>
      <artifactId>camunda-commons-logging</artifactId>
      <version>1.10.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.camunda.commons</groupId>
      <artifactId>camunda-commons-typed-values</artifactId>
      <version>7.17.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.mybatis</groupId>
      <artifactId>mybatis</artifactId>
      <version>3.5.6</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-beans</artifactId>
      <version>5.2.19.RELEASE</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>joda-time</groupId>
      <artifactId>joda-time</artifactId>
      <version>2.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
      <version>2.8.9</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.camunda.connect</groupId>
      <artifactId>camunda-connect-core</artifactId>
      <version>1.5.2</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.camunda.connect</groupId>
      <artifactId>camunda-connect-connectors-all</artifactId>
      <version>1.5.2</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.ant</groupId>
      <artifactId>ant</artifactId>
      <version>1.7.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.hibernate.javax.persistence</groupId>
      <artifactId>hibernate-jpa-2.0-api</artifactId>
      <version>1.0.1.Final</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.geronimo.specs</groupId>
      <artifactId>geronimo-jta_1.1_spec</artifactId>
      <version>1.1.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.uuid</groupId>
      <artifactId>java-uuid-generator</artifactId>
      <version>3.2.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec</groupId>
      <artifactId>jboss-javaee-web-6.0</artifactId>
      <version>3.0.2.Final</version>
      <type>pom</type>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.tomcat</groupId>
      <artifactId>catalina</artifactId>
      <version>6.0.37</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.jboss</groupId>
      <artifactId>jboss-vfs</artifactId>
      <version>3.1.0.Final</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-email</artifactId>
      <version>1.5</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.13.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>4.3.1</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>byte-buddy</artifactId>
          <groupId>net.bytebuddy</groupId>
        </exclusion>
        <exclusion>
          <artifactId>byte-buddy-agent</artifactId>
          <groupId>net.bytebuddy</groupId>
        </exclusion>
        <exclusion>
          <artifactId>objenesis</artifactId>
          <groupId>org.objenesis</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.h2database</groupId>
      <artifactId>h2</artifactId>
      <version>2.0.206</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.openjpa</groupId>
      <artifactId>openjpa</artifactId>
      <version>2.4.3</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>commons-lang</artifactId>
          <groupId>commons-lang</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-collections</artifactId>
          <groupId>commons-collections</groupId>
        </exclusion>
        <exclusion>
          <artifactId>serp</artifactId>
          <groupId>net.sourceforge.serp</groupId>
        </exclusion>
        <exclusion>
          <artifactId>geronimo-jms_1.1_spec</artifactId>
          <groupId>org.apache.geronimo.specs</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-pool</artifactId>
          <groupId>commons-pool</groupId>
        </exclusion>
        <exclusion>
          <artifactId>xbean-asm6-shaded</artifactId>
          <groupId>org.apache.xbean</groupId>
        </exclusion>
        <exclusion>
          <artifactId>geronimo-jpa_2.0_spec</artifactId>
          <groupId>org.apache.geronimo.specs</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.subethamail</groupId>
      <artifactId>subethasmtp-wiser</artifactId>
      <version>1.2</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>subethasmtp-smtp</artifactId>
          <groupId>org.subethamail</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-logging</artifactId>
          <groupId>commons-logging</groupId>
        </exclusion>
        <exclusion>
          <artifactId>mail</artifactId>
          <groupId>javax.mail</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.8.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-test</artifactId>
      <version>5.2.19.RELEASE</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.8.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.camunda.commons</groupId>
      <artifactId>camunda-commons-testing</artifactId>
      <version>1.10.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.camunda.bpm</groupId>
      <artifactId>camunda-test-utils-testcontainers</artifactId>
      <version>7.17.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>testcontainers</artifactId>
          <groupId>org.testcontainers</groupId>
        </exclusion>
        <exclusion>
          <artifactId>mysql</artifactId>
          <groupId>org.testcontainers</groupId>
        </exclusion>
        <exclusion>
          <artifactId>mariadb</artifactId>
          <groupId>org.testcontainers</groupId>
        </exclusion>
        <exclusion>
          <artifactId>postgresql</artifactId>
          <groupId>org.testcontainers</groupId>
        </exclusion>
        <exclusion>
          <artifactId>mssqlserver</artifactId>
          <groupId>org.testcontainers</groupId>
        </exclusion>
        <exclusion>
          <artifactId>cockroachdb</artifactId>
          <groupId>org.testcontainers</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.hamcrest</groupId>
      <artifactId>hamcrest-library</artifactId>
      <version>1.3</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <version>2.9.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
      <version>1.2.10</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>logback-core</artifactId>
          <groupId>ch.qos.logback</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>jcl-over-slf4j</artifactId>
      <version>1.7.26</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>jul-to-slf4j</artifactId>
      <version>1.7.26</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.github.tomakehurst</groupId>
      <artifactId>wiremock</artifactId>
      <version>1.58</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>httpclient</artifactId>
          <groupId>org.apache.httpcomponents</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jackson-core</artifactId>
          <groupId>com.fasterxml.jackson.core</groupId>
        </exclusion>
        <exclusion>
          <artifactId>xmlunit</artifactId>
          <groupId>xmlunit</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jetty</artifactId>
          <groupId>org.mortbay.jetty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jackson-databind</artifactId>
          <groupId>com.fasterxml.jackson.core</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jsonassert</artifactId>
          <groupId>org.skyscreamer</groupId>
        </exclusion>
        <exclusion>
          <artifactId>json-path</artifactId>
          <groupId>com.jayway.jsonpath</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jackson-annotations</artifactId>
          <groupId>com.fasterxml.jackson.core</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jopt-simple</artifactId>
          <groupId>net.sf.jopt-simple</groupId>
        </exclusion>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.codehaus.groovy</groupId>
      <artifactId>groovy-all</artifactId>
      <version>2.4.13</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.python</groupId>
      <artifactId>jython</artifactId>
      <version>2.5.3</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.jruby</groupId>
      <artifactId>jruby-complete</artifactId>
      <version>9.1.17.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.graalvm.js</groupId>
      <artifactId>js</artifactId>
      <version>21.1.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>regex</artifactId>
          <groupId>org.graalvm.regex</groupId>
        </exclusion>
        <exclusion>
          <artifactId>truffle-api</artifactId>
          <groupId>org.graalvm.truffle</groupId>
        </exclusion>
        <exclusion>
          <artifactId>graal-sdk</artifactId>
          <groupId>org.graalvm.sdk</groupId>
        </exclusion>
        <exclusion>
          <artifactId>icu4j</artifactId>
          <groupId>com.ibm.icu</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.graalvm.js</groupId>
      <artifactId>js-scriptengine</artifactId>
      <version>21.1.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>graal-sdk</artifactId>
          <groupId>org.graalvm.sdk</groupId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.camunda.bpm</groupId>
        <artifactId>camunda-core-internal-dependencies</artifactId>
        <version>${project.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-framework-bom</artifactId>
        <version>${version.spring.framework}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <properties>
    <mail.server.port>5025</mail.server.port>
    <skip-third-party-bom>false</skip-third-party-bom>
    <test.excludes>$.</test.excludes>
    <camunda.artifact>org.camunda.bpm</camunda.artifact>
    <camunda.osgi.import.additional>!com.google.gson.*,
      junit*;resolution:=optional,
      org.junit*;resolution:=optional,
      com.sun*;resolution:=optional,
      javax.persistence*;resolution:=optional,
      javax.servlet*;resolution:=optional,
      javax.transaction*;resolution:=optional,
      javax.ejb*;resolution:=optional,
      javax.xml*;resolution:=optional,
      javax.mail*;resolution:=optional,
      org.apache.catalina*;resolution:=optional,
      org.apache.commons.mail;resolution:=optional,
      org.apache.tools.ant*;resolution:=optional,
      org.apache.xerces*;resolution:=optional,
      org.springframework*;resolution:=optional,
      com.fasterxml*;resolution:=optional,
      org.jboss.vfs*;resolution:=optional</camunda.osgi.import.additional>
    <history.level>full</history.level>
    <authorizationCheckRevokes>auto</authorizationCheckRevokes>
    <jdbcBatchProcessing>true</jdbcBatchProcessing>
    <camunda.osgi.export.pkg>!${camunda.artifact}.engine.variable.*,
      ${camunda.artifact};${camunda.osgi.version};-noimport:=true,
      ${camunda.artifact}.application.*;${camunda.osgi.version};-noimport:=true,
      ${camunda.artifact}.container.*;${camunda.osgi.version};-noimport:=true,
      ${camunda.artifact}.engine.*;${camunda.osgi.version};-noimport:=true</camunda.osgi.export.pkg>
  </properties>
</project>
