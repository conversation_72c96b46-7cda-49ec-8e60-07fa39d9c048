<?xml version='1.0' encoding='UTF-8'?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.maven</groupId>
    <artifactId>maven-parent</artifactId>
    <version>21</version>
    <relativePath>../../pom/maven/pom.xml</relativePath>
  </parent>

  <groupId>org.apache.maven.plugins</groupId>
  <artifactId>maven-plugins</artifactId>
  <version>22</version>
  <packaging>pom</packaging>

  <name>Maven Plugins</name>
  <description>Maven Plugins</description>
  <url>http://maven.apache.org/plugins/</url>

  <mailingLists>
    <!-- duplication from components - temporary until the site layout is clearer -->
    <mailingList>
      <name>Maven User List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-users</archive>
      <otherArchives>
        <otherArchive>http://www.mail-archive.com/<EMAIL>/</otherArchive>
        <otherArchive>http://old.nabble.com/Maven---Users-f178.html</otherArchive>
        <otherArchive>http://maven.users.markmail.org/</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Maven Developer List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-dev</archive>
      <otherArchives>
        <otherArchive>http://www.mail-archive.com/<EMAIL>/</otherArchive>
        <otherArchive>http://old.nabble.com/Maven-Developers-f179.html</otherArchive>
        <otherArchive>http://maven.dev.markmail.org/</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Maven Issues List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-issues/</archive>
      <otherArchives>
        <otherArchive>http://www.mail-archive.com/<EMAIL></otherArchive>
        <otherArchive>http://old.nabble.com/Maven---Issues-f15573.html</otherArchive>
        <otherArchive>http://maven.issues.markmail.org/</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Maven Commits List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-dev</archive>
      <otherArchives>
        <otherArchive>http://www.mail-archive.com/<EMAIL></otherArchive>
        <otherArchive>http://old.nabble.com/Maven---Commits-f15575.html</otherArchive>
        <otherArchive>http://maven.commits.markmail.org/</otherArchive>
      </otherArchives>
    </mailingList>
    <!-- duplication from parent pom - temporary until they inherit properly -->
    <mailingList>
      <name>Maven Announcements List</name>
      <post><EMAIL></post>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-announce/</archive>
      <otherArchives>
        <otherArchive>http://www.mail-archive.com/<EMAIL></otherArchive>
        <otherArchive>http://old.nabble.com/Maven-Announcements-f15617.html</otherArchive>
        <otherArchive>http://maven.announce.markmail.org/</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Maven Notifications List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-notifications/</archive>
      <otherArchives>
        <otherArchive>http://www.mail-archive.com/<EMAIL></otherArchive>
        <otherArchive>http://old.nabble.com/Maven---Notifications-f15574.html</otherArchive>
        <otherArchive>http://maven.notifications.markmail.org/</otherArchive>
      </otherArchives>
    </mailingList>
  </mailingLists>

  <scm>
    <connection>scm:svn:http://svn.apache.org/repos/asf/maven/plugins/tags/maven-plugins-22</connection>
    <developerConnection>scm:svn:https://svn.apache.org/repos/asf/maven/plugins/tags/maven-plugins-22</developerConnection>
    <url>http://svn.apache.org/viewvc/maven/plugins/tags/maven-plugins-22</url>
  </scm>
  <ciManagement>
    <system>Jenkins</system>
    <url>https://builds.apache.org/hudson/job/maven-plugins/</url>
  </ciManagement>

  <distributionManagement>
    <site>
      <id>apache.website</id>
      <url>scp://people.apache.org/www/maven.apache.org/plugins/</url>
    </site>
  </distributionManagement>

  <repositories>
    <repository>
      <id>apache.snapshots</id>
      <name>Apache Snapshot Repository</name>
      <url>http://repository.apache.org/snapshots</url>
      <releases>
        <enabled>false</enabled>
      </releases>
    </repository>
  </repositories>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-changes-plugin</artifactId>
          <version>2.6</version>
          <configuration>
            <issueManagementSystems>
              <issueManagementSystem>JIRA</issueManagementSystem>
            </issueManagementSystems>
            <maxEntries>1000</maxEntries>
            <runOnlyAtExecutionRoot>true</runOnlyAtExecutionRoot>
            <!-- Used by announcement-generate goal -->
            <templateDirectory>org/apache/maven/plugins</templateDirectory>
            <!-- Used by announcement-mail goal -->
            <subject>[ANN] ${project.name} ${project.version} Released</subject>
            <toAddresses>
              <toAddress implementation="java.lang.String"><EMAIL></toAddress>
              <toAddress implementation="java.lang.String"><EMAIL></toAddress>
            </toAddresses>
            <ccAddresses>
              <ccAddress implementation="java.lang.String"><EMAIL></ccAddress>
            </ccAddresses>
            <!-- These values need to be specified as properties in the profile apache-release in your settings.xml -->
            <fromDeveloperId>${apache.availid}</fromDeveloperId>
            <smtpHost>${smtp.host}</smtpHost>
          </configuration>
          <dependencies>
            <!-- Used by announcement-generate goal -->
            <dependency>
              <groupId>org.apache.maven.shared</groupId>
              <artifactId>maven-shared-resources</artifactId>
              <version>1</version>
            </dependency>
          </dependencies>
        </plugin>
        <plugin>
          <artifactId>maven-release-plugin</artifactId>
          <configuration>
            <tagBase>https://svn.apache.org/repos/asf/maven/plugins/tags</tagBase>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-site-plugin</artifactId>
          <configuration>
            <stagingSiteURL>scp://people.apache.org/www/maven.apache.org/plugins/${project.artifactId}-${project.version}</stagingSiteURL>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>enforce</goal>
            </goals>
            <id>ensure-no-container-api</id>
            <configuration>
              <rules>
                <bannedDependencies>
                  <excludes>
                    <exclude>org.codehaus.plexus:plexus-component-api</exclude>
                  </excludes>
                  <message>The new containers are not supported. You probably added a dependency that is missing the exclusions.</message>
                </bannedDependencies>
              </rules>
              <fail>true</fail>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-plugin-plugin</artifactId>
        <executions>
          <execution>
            <id>generated-helpmojo</id>
            <goals>
              <goal>helpmojo</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-plugin-plugin</artifactId>
        <version>2.8</version>
      </plugin>
    </plugins>
  </reporting>

  <profiles>
    <profile>
      <id>quality-checks</id>
      <activation>
        <property>
          <name>quality-checks</name>
          <value>true</value>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-docck-plugin</artifactId>
            <executions>
              <execution>
                <id>docck-check</id>
                <phase>verify</phase>
                <goals>
                  <goal>check</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>run-its</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-invoker-plugin</artifactId>
            <configuration>
              <debug>true</debug>
              <projectsDirectory>src/it</projectsDirectory>
              <cloneProjectsTo>${project.build.directory}/it</cloneProjectsTo>
              <preBuildHookScript>setup</preBuildHookScript>
              <postBuildHookScript>verify</postBuildHookScript>
              <localRepositoryPath>${project.build.directory}/local-repo</localRepositoryPath>
              <settingsFile>src/it/settings.xml</settingsFile>
              <pomIncludes>
                <pomInclude>*/pom.xml</pomInclude>
              </pomIncludes>
            </configuration>
            <executions>
              <execution>
                <id>integration-test</id>
                <goals>
                  <goal>install</goal>
                  <goal>integration-test</goal>
                  <goal>verify</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>reporting</id>
      <reporting>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-invoker-plugin</artifactId>
            <version>1.5</version>
          </plugin>
        </plugins>
      </reporting>
    </profile>
    <profile>
      <id>maven-3</id>
      <activation>
        <file>
          <!-- This employs that the basedir expression is only recognized by Maven 3.x (see MNG-2363) -->
          <exists>${basedir}</exists>
        </file>
      </activation>
      <build>
        <plugins>
          <!-- if releasing current pom with Maven 3, site descriptor must be attached -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-site-plugin</artifactId>
            <inherited>false</inherited>
            <executions>
              <execution>
                <id>attach-descriptor</id>
                <goals>
                  <goal>attach-descriptor</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>        
      </build>   
    </profile> 	
  </profiles>
</project>
