<!--
/**
 * 资产操作 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2021-11-18 12:55:30
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
	<title th:text="${lang.translate('资产操作')}">资产操作</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" th:href="@{/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css}" rel="stylesheet"/>
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon"> <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
    <style>
    </style>
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="display:none">

        <input name="id" id="id"  type="hidden"/>

         <!--开始：group 循环-->



        <div class="layui-row form-row">

             <!--开始：group 循环-->
            <div class="layui-col-xs6 form-column">

                    <div class="layui-form-item" >
                        <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('名称')}">名称</div><div class="layui-required">*</div></div>
                        <div class="layui-input-block layui-input-block-c1">
                            <input lay-filter="name" id="name" name="name" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('名称') }" type="text" class="layui-input"    lay-verify="|required"  />
                        </div>
                    </div>


                <div class="layui-form-item" >
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('审批')}">审批</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input input-type="radio" type="radio" name="approval" lay-filter="approval" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.common.StatusYNEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==-1)?'yes':'no'}">
                    </div>
                </div>


                                    <div class="layui-form-item" >
                        <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('备注')}">备注</div></div>
                        <div class="layui-input-block layui-input-block-c1">
                            <input lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('备注') }" type="text" class="layui-input"  />
                        </div>
                    </div>

                <!--结束：栏次内字段循环-->
            </div>
             <!--开始：group 循环-->
            <div class="layui-col-xs6 form-column">

                    <div class="layui-form-item" >
                        <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('分配编号')}">分配编号</div><div class="layui-required">*</div></div>
                        <div class="layui-input-block layui-input-block-c1">
                            <input lay-filter="allocateCode" id="allocateCode" name="allocateCode" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('分配编号') }" type="text" class="layui-input"    lay-verify="|required"  />
                        </div>
                    </div>


                    <div class="layui-form-item" >
                        <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('操作编码')}">操作编码</div><div class="layui-required">*</div></div>
                        <div class="layui-input-block layui-input-block-c1">
                            <div id="operateCode" input-type="select" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.AssetOperateEnum')}" extraParam="{}"></div>
                        </div>
                    </div>


                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 200px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('eam_operate:create','eam_operate:update','eam_operate:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>

<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var SELECT_OPERATECODE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetOperateEnum')}]];
    var RADIO_APPROVAL_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusYNEnum')}]];
    var VALIDATE_CONFIG={"approval":{"labelInForm":"审批","inputType":"radio_box","required":true},"name":{"labelInForm":"名称","inputType":"text_input","required":true},"allocateCode":{"labelInForm":"分配编号","inputType":"text_input","required":true},"operateCode":{"labelInForm":"操作编码","inputType":"select_box","required":true}};
    var AUTH_PREFIX="eam_operate";


</script>



<script th:src="'/business/eam/operate/operate_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/operate/operate_form.js?'+${cacheKey}"></script>

</body>
</html>
