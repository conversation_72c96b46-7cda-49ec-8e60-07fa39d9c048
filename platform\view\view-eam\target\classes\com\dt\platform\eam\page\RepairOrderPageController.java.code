package com.dt.platform.eam.page;

import org.github.foxnic.web.framework.view.controller.ViewController;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import com.dt.platform.proxy.eam.RepairOrderServiceProxy;
import javax.servlet.http.HttpServletRequest;
/**
 * <p>
 * 故障申请单模版页面控制器
 * </p>
 * <AUTHOR> , <EMAIL>
 * @since 2024-03-25 13:24:20
*/

@Controller("EamRepairOrderPageController")
@RequestMapping(RepairOrderPageController.prefix)
public class RepairOrderPageController extends ViewController {

	public static final String prefix="business/eam/repair_order";

	private RepairOrderServiceProxy proxy;

	/**
	 * 获得代理对象<br>
	 * 1、单体应用时，在应用内部调用；<br>
	 * 2、前后端分离时，通过配置，以Rest方式调用后端；<br>
	 * 3、微服务时，通过feign调用; <br>
	 * */
	public RepairOrderServiceProxy proxy() {
		if(proxy==null) {
			proxy=RepairOrderServiceProxy.api();
		}
		return proxy;
	}

	/**
	 * 故障申请单 功能主页面
	 */
	@RequestMapping("/repair_order_list.html")
	public String list(Model model,HttpServletRequest request) {
		return getTemplatePath(prefix,"repair_order_list");
	}

	/**
	 * 故障申请单 表单页面
	 */
	@RequestMapping("/repair_order_form.html")
	public String form(Model model,HttpServletRequest request , String id) {
		return getTemplatePath(prefix,"repair_order_form");
	}
}