<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 2010, 2018 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Public License v. 2.0, which is available at
    http://www.eclipse.org/legal/epl-2.0.

    This Source Code may also be made available under the following Secondary
    Licenses when the conditions for such availability set forth in the
    Eclipse Public License v. 2.0 are satisfied: GNU General Public License,
    version 2 with the GNU Classpath Exception, which is available at
    https://www.gnu.org/software/classpath/license.html.

    SPDX-License-Identifier: EPL-2.0 OR GPL-2.0 WITH Classpath-exception-2.0

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.eclipse.ee4j</groupId>
        <artifactId>project</artifactId>
        <version>1.0.5</version>
        <relativePath />
    </parent>
    <groupId>org.glassfish.hk2</groupId>
    <artifactId>hk2-parent</artifactId>
    <version>2.6.1</version>
    <packaging>pom</packaging>

    <name>GlassFish HK2</name>
    <description>Dependency Injection Kernel</description>
    <inceptionYear>2009</inceptionYear>

    <organization>
        <name>Oracle Corporation</name>
        <url>http://www.oracle.com</url>
    </organization>

    <url>https://github.com/eclipse-ee4j/glassfish-hk2</url>

    <licenses>
        <license>
            <name>EPL 2.0</name>
            <url>http://www.eclipse.org/legal/epl-2.0</url>
            <distribution>repo</distribution>
        </license>
        <license>
            <name>GPL2 w/ CPE</name>
            <url>https://www.gnu.org/software/classpath/license.html</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <contributors>
        <contributor>
            <name>Jerome Dochez</name>
            <url>http://blogs.sun.com/dochez</url>
        </contributor>
        <contributor>
            <name>Kohsuke Kawaguchi</name>
            <url>http://weblogs.java.net/blog/kohsuke</url>
        </contributor>
        <contributor>
            <name>Sanjeeb Sahoo</name>
            <url>http://weblogs.java.net/ss141213</url>
        </contributor>
        <contributor>
            <name>Andriy Zhdanov</name>
            <url>http://avalez.blogspot.com</url>
        </contributor>
        <contributor>
            <name>Jeff Trent</name>
        </contributor>
    </contributors>

    <developers>
        <developer>
            <id>jwells</id>
            <name>John Wells</name>
            <organization>Oracle, Inc</organization>
            <roles>
                <role>developer</role>
            </roles>
        </developer>
        <developer>
            <id>mtaube</id>
            <name>Mason Taube</name>
            <organization>Oracle, Inc</organization>
            <roles>
                <role>developer</role>
            </roles>
        </developer>
    </developers>

    <issueManagement>
        <system>Github</system>
        <url>https://github.com/eclipse-ee4j/glassfish-hk2/issues</url>
    </issueManagement>

    <scm>
        <connection>scm:git:https://github.com/eclipse-ee4j/glassfish-hk2.git</connection>
        <developerConnection>scm:git:**************:eclipse-ee4j/glassfish-hk2.git</developerConnection>
        <url>https://github.com/eclipse-ee4j/glassfish-hk2</url>
        <tag>HEAD</tag>
    </scm>

    <mailingLists>
        <mailingList>
            <name>GlassFish HK2 mailing list</name>
            <post><EMAIL></post>
            <subscribe>https://dev.eclipse.org/mailman/listinfo/glassfish-hk2-dev</subscribe>
            <unsubscribe>https://dev.eclipse.org/mailman/listinfo/glassfish-hk2-dev</unsubscribe>
            <archive>https://dev.eclipse.org/mhonarc/lists/glassfish-hk2-dev/</archive>
        </mailingList>
    </mailingLists>

    <pluginRepositories>
        <pluginRepository>
            <id>jvnet-nexus-snapshots</id>
            <name>Java.net Nexus Snapshots Repository</name>
            <url>https://maven.java.net/content/repositories/snapshots</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <properties>
        <jdk.version>1.6.0</jdk.version>
        <mvn.version>3.0.3</mvn.version>

        <jakarta.annotation.version>1.3.4</jakarta.annotation.version>
        <jakarta.json.version>1.1.5</jakarta.json.version>
        <glassfish.json.version>1.1.5</glassfish.json.version>
        <jaxb-api.version>2.3.1</jaxb-api.version>
        <jaxb-runtime.version>2.3.1</jaxb-runtime.version>
        <java.net.username>${user.name}</java.net.username>
        <hibernate-validator.version>6.0.10.Final</hibernate-validator.version>
        <hibernate-validator.version.upperbound>7</hibernate-validator.version.upperbound>
        <jakarta.validation.version>2.0.1.Final</jakarta.validation.version>
        <jakarta.validation.version.upperbound>3</jakarta.validation.version.upperbound>
        <jakarta.validation.osgi.version>2.0.1</jakarta.validation.osgi.version>
        <glassfish.javax.el.version>3.0.1-b10</glassfish.javax.el.version>
        <jakarta.el.version>3.0.2</jakarta.el.version>
        <jtype.version>0.1.0</jtype.version>
        <javassist.version>3.22.0-CR2</javassist.version>
        <junit.version>4.12</junit.version>
        <asm.version>7.1</asm.version>
        <woodstox.version>4.1.2</woodstox.version>
        <stax-api.version>1.0-2</stax-api.version>
        <aopalliance.version>1.0</aopalliance.version>
        <testng.version>6.9.10</testng.version>
        <assertj.version>3.9.1</assertj.version>
        <pax-exam-version>4.12.0</pax-exam-version>
        <javax-inject.version>1</javax-inject.version>
        <slf4j.version>1.7.21</slf4j.version>
        <org.jboss.logging.version>3.3.1.Final</org.jboss.logging.version>
        <jersey.version>2.28</jersey.version>
        <grizzly.version>2.3.25</grizzly.version>
        <hamcrest.version>1.3</hamcrest.version>
        <jakarta.servlet.version>4.0.2</jakarta.servlet.version>
        <jakarta.ws.rs.version>2.1.3</jakarta.ws.rs.version>
        <classmate.version>1.3.3</classmate.version>
        <springcontext.version>4.3.16.RELEASE</springcontext.version>
        <guice.version>4.2.1</guice.version>
        <protobuf.version>3.5.1</protobuf.version>
        
        <legal.doc.source>${maven.multiModuleProjectDirectory}/</legal.doc.source>     

        <findbugs.exclude />
        <findbugs.threshold>High</findbugs.threshold>
        <surefireArgLineExtra />
        <release.arguments />
        
        <manifest.location>target/classes/META-INF/MANIFEST.MF</manifest.location>
    </properties>

    <modules>
        <module>maven-plugins</module>
        <module>hk2-metadata-generator</module>
        <module>hk2-runlevel</module>
        <module>hk2-locator</module>
        <module>class-model</module>
        <module>hk2-core</module>
        <module>osgi</module>
        <module>examples</module>
        <module>hk2-testing</module>
        <module>guice-bridge</module>
        <module>spring-bridge</module>
        <module>hk2-jmx</module>
        <module>hk2</module>
        <module>bom</module>
        <module>external</module>
        <module>hk2-utils</module>
        <module>hk2-api</module>
        <module>hk2-configuration</module>
        <module>hk2-extras</module>
        <!--<module>javadocs</module>-->
    </modules>

    <build>
        <defaultGoal>install</defaultGoal>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-eclipse-plugin</artifactId>
                <configuration>
                    <downloadSources>true</downloadSources>
                    <downloadJavadocs>true</downloadJavadocs>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <argLine>-enableassertions</argLine>
                    <systemProperties>
                        <property>
                            <name>java.util.logging.config.file</name>
                            <value>logging.properties</value>
                        </property>
                    </systemProperties>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>findbugs-maven-plugin</artifactId>
                <configuration>
                    <skip>${findbugs.skip}</skip>
                    <threshold>${findbugs.threshold}</threshold>
                    <findbugsXmlWithMessages>true</findbugsXmlWithMessages>
                    <excludeFilterFile>
                        exclude-common.xml,${findbugs.exclude}
                    </excludeFilterFile>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.glassfish.findbugs</groupId>
                        <artifactId>findbugs</artifactId>
                        <version>1.0</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <configuration>
                    <!-- By default, we don't export anything. -->
                    <Export-Package />
                    <instructions>
                        <!-- Read all the configuration from osgi.bundle file, if it exists.
                             See Felix-699 to find out why we use ${basedir}.
                        -->
                        <_include>-${basedir}/osgi.bundle</_include>
                    </instructions>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifestFile>${manifest.location}</manifestFile>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <executions>
                    <execution>
                        <id>enforce-versions</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireJavaVersion>
                                    <version>[${jdk.version},)</version>
                                    <message>You need JDK ${jdk.version} and above!</message>
                                </requireJavaVersion>
                                <requireMavenVersion>
                                    <version>[${mvn.version},)</version>
                                    <message>You need Maven ${mvn.version} or above!</message>
                                </requireMavenVersion>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>add-legal-resource</id>
                        <phase>generate-resources</phase>
                        <goals>
                            <goal>add-resource</goal>
                        </goals>
                        <configuration>
                            <resources>
                                <resource>
                                    <directory>${legal.doc.source}</directory>
                                    <includes>
                                        <include>NOTICE.md</include>
                                        <include>LICENSE.md</include>
                                    </includes>
                                    <targetPath>META-INF</targetPath>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.0.1</version>
                    <configuration>
                        <additionalOptions>
                            <additionalOption>${javadoc.options}</additionalOption>
                        </additionalOptions>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-gpg-plugin</artifactId>
                    <version>1.6</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>3.0.0-M2</version>
                </plugin>
                <plugin>
                    <groupId>com.googlecode.maven-download-plugin</groupId>
                    <artifactId>maven-download-plugin</artifactId>
                    <version>1.1.0</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>findbugs-maven-plugin</artifactId>
                    <version>3.0.5</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>3.0.0-M3</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-eclipse-plugin</artifactId>
                    <version>2.10</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.1.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <version>4.1.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-plugin-plugin</artifactId>
                    <version>3.6.0</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
                <plugin>
                    <groupId>org.glassfish.hk2</groupId>
                    <artifactId>hk2-inhabitant-generator</artifactId>
                    <version>${project.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <version>1.8</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>3.1.1</version>
                </plugin>
                <plugin>
                    <groupId>org.glassfish.hk2</groupId>
                    <artifactId>osgiversion-maven-plugin</artifactId>
                    <version>${project.version}</version>
                    <executions>
                        <execution>
                            <id>compute-osgi-version</id>
                            <phase>validate</phase>
                            <goals>
                                <goal>compute-osgi-version</goal>
                            </goals>
                            <configuration>
                                <dropVersionComponent>qualifier</dropVersionComponent>
                                <versionPropertyName>project.osgi.version</versionPropertyName>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>3.2.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>3.0.0-M1</version>
                    <configuration>
                        <retryFailedDeploymentCount>10</retryFailedDeploymentCount>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.0.1</version>
                    <configuration>
                      <includePom>true</includePom>
                    </configuration>
                </plugin>
                <plugin>
                  <groupId>org.apache.maven.plugins</groupId>
                  <artifactId>maven-site-plugin</artifactId>
                  <version>3.7.1</version>
                  <configuration>
                        <skip>true</skip>
                        <skipDeploy>true</skipDeploy>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-release-plugin</artifactId>
                    <version>2.5.3</version>
                    <configuration>
                        <mavenExecutorId>forked-path</mavenExecutorId>
                        <useReleaseProfile>false</useReleaseProfile>
                        <tagNameFormat>@{project.version}</tagNameFormat>
                        <arguments>${release.arguments}</arguments>
                        <preparationGoals>install</preparationGoals>
                        <goals>deploy</goals>
                    </configuration>
                    <dependencies>
                        <dependency>
                            <groupId>org.apache.maven.scm</groupId>
                            <artifactId>maven-scm-provider-gitexe</artifactId>
                            <version>1.11.1</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.glassfish.copyright</groupId>
                    <artifactId>glassfish-copyright-maven-plugin</artifactId>
                    <version>1.50</version>
                    <configuration>
                        <scm>git</scm>
                        <scmOnly>true</scmOnly>
                        <exclude>
                          <exclude>LICENSE.md</exclude>
                          <exclude>MANIFEST.MF</exclude>
                          <exclude>README</exclude>
                          <exclude>hk2-locator/</exclude>
                          <exclude>META-INF/services/</exclude>
                          <exclude>META-INF/inhabitants/</exclude>
                          <exclude>resources/gendir</exclude>
                          <exclude>.png</exclude>
                          <exclude>.class</exclude>
                          <exclude>.json</exclude>
                          <exclude>.txt</exclude>
                          <exclude>.pbuf</exclude>
                        </exclude>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-scm-publish-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.servicemix.tooling</groupId>
                    <artifactId>depends-maven-plugin</artifactId>
                    <version>1.4.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.servicemix.tooling</groupId>
                    <artifactId>depends-maven-plugin</artifactId>
                    <version>1.4.0</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>jakarta.annotation</groupId>
                <artifactId>jakarta.annotation-api</artifactId>
                <version>${jakarta.annotation.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.json</groupId>
                <artifactId>jakarta.json-api</artifactId>
                <version>${jakarta.json.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>${jaxb-api.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>jaxb-runtime</artifactId>
                <version>${jaxb-runtime.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish</groupId>
                <artifactId>jakarta.json</artifactId>
                <version>${glassfish.json.version}</version>
            </dependency>
            <dependency>
                <groupId>woodstox</groupId>
                <artifactId>wstx-asl</artifactId>
                <version>${woodstox.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.xml.stream</groupId>
                <artifactId>stax-api</artifactId>
                <version>${stax-api.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.maven</groupId>
                <artifactId>maven-plugin-api</artifactId>
                <version>3.3.3</version>
            </dependency>
            <dependency>
                <groupId>com.sun.codemodel</groupId>
                <artifactId>codemodel</artifactId>
                <version>2.6</version>
            </dependency>
            <dependency>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.maven.shared</groupId>
                <artifactId>maven-osgi</artifactId>
                <version>0.2.0</version>
            </dependency>
            <dependency>
                <groupId>args4j</groupId>
                <artifactId>args4j</artifactId>
                <version>2.33</version>
            </dependency>
            <dependency>
                <groupId>javax.inject</groupId>
                <artifactId>javax.inject</artifactId>
                <version>${javax-inject.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.inject</groupId>
                <artifactId>guice</artifactId>
                <version>${guice.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.hk2</groupId>
                <artifactId>osgi-resource-locator</artifactId>
                <version>1.0.3</version>
            </dependency>
            <dependency>
                <groupId>org.apache.ant</groupId>
                <artifactId>ant</artifactId>
                <version>1.10.5</version>
            </dependency>
            <dependency>
                <groupId>org.apache.maven</groupId>
                <artifactId>maven-artifact</artifactId>
                <version>3.6.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.maven</groupId>
                <artifactId>maven-archiver</artifactId>
                <version>3.2.0</version>
            </dependency>
            <dependency>
                <groupId>org.testng</groupId>
                <artifactId>testng</artifactId>
                <version>${testng.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.assertj</groupId>
                <artifactId>assertj-core</artifactId>
                <version>${assertj.version}</version>
            </dependency>
            <dependency>
                <groupId>org.osgi</groupId>
                <artifactId>osgi.core</artifactId>
                <version>6.0.0</version>
            </dependency>
            <dependency>
                <groupId>org.osgi</groupId>
                <artifactId>osgi.cmpn</artifactId>
                <version>6.0.0</version>
            </dependency>
            <dependency>
                <groupId>javax.enterprise</groupId>
                <artifactId>cdi-api</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>org.osgi</groupId>
                <artifactId>org.osgi.enterprise</artifactId>
                <version>4.2.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.felix</groupId>
                <artifactId>org.apache.felix.bundlerepository</artifactId>
                <version>2.0.10</version>
            </dependency>
            <dependency>
                <groupId>org.ops4j.pax.exam</groupId>
                <artifactId>pax-exam-spi</artifactId>
                <version>${pax-exam-version}</version>
            </dependency>
            <dependency>
                <groupId>org.ops4j.pax.exam</groupId>
                <artifactId>pax-exam-junit4</artifactId>
                <version>${pax-exam-version}</version>
            </dependency>
            <dependency>
                <groupId>org.ops4j.pax.exam</groupId>
                <artifactId>pax-exam-link-mvn</artifactId>
                <version>${pax-exam-version}</version>
            </dependency>
            <dependency>
                <groupId>org.ops4j.pax.exam</groupId>
                <artifactId>pax-exam-inject</artifactId>
                <version>${pax-exam-version}</version>
            </dependency>
            <dependency>
                <groupId>org.ops4j.pax.exam</groupId>
                <artifactId>pax-exam-invoker-junit</artifactId>
                <version>${pax-exam-version}</version>
            </dependency>
            <dependency>
                <groupId>org.ops4j.pax.exam</groupId>
                <artifactId>pax-exam-extender-service</artifactId>
                <version>${pax-exam-version}</version>
            </dependency>
            <dependency>
                <groupId>org.ops4j.pax.exam</groupId>
                <artifactId>pax-exam-container-native</artifactId>
                <version>${pax-exam-version}</version>
            </dependency>
            <dependency>
                <groupId>org.ops4j.pax.exam</groupId>
                <artifactId>pax-exam-link-assembly</artifactId>
                <version>${pax-exam-version}</version>
            </dependency>
            <dependency>
                <groupId>org.ops4j.pax.url</groupId>
                <artifactId>pax-url-aether</artifactId>
                <version>2.5.4</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>1.2.3</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>1.2.3</version>
            </dependency>
            <dependency>
                <groupId>org.apache.felix</groupId>
                <artifactId>org.apache.felix.framework</artifactId>
                <version>6.0.1</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-simple</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.felix</groupId>
                <artifactId>org.apache.felix.configadmin</artifactId>
                <version>1.9.10</version>
            </dependency>
            <dependency>
                <groupId>org.ops4j.pax.logging</groupId>
                <artifactId>pax-logging-api</artifactId>
                <version>1.10.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>avalon-framework</groupId>
                        <artifactId>avalon-framework-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.ops4j.pax.logging</groupId>
                <artifactId>pax-logging-service</artifactId>
                <version>1.7.2</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${springcontext.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator-cdi</artifactId>
                <version>${hibernate-validator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.googlecode.jtype</groupId>
                <artifactId>jtype</artifactId>
                <version>${jtype.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.logging</groupId>
                <artifactId>jboss-logging</artifactId>
                <version>${org.jboss.logging.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml</groupId>
                <artifactId>classmate</artifactId>
                <version>${classmate.version}</version>
            </dependency>
            <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm</artifactId>
                <version>${asm.version}</version>
                <optional>true</optional>
            </dependency>
            <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm-commons</artifactId>
                <version>${asm.version}</version>
                <optional>true</optional>
            </dependency>
            <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm-tree</artifactId>
                <version>${asm.version}</version>
                <optional>true</optional>
            </dependency>
            <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm-util</artifactId>
                <version>${asm.version}</version>
                <optional>true</optional>
            </dependency>
            <dependency>
                <groupId>aopalliance</groupId>
                <artifactId>aopalliance</artifactId>
                <version>${aopalliance.version}</version>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
            </dependency>
            <dependency>
                <groupId>org.easymock</groupId>
                <artifactId>easymock</artifactId>
                <version>3.5</version>
            </dependency>
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${jakarta.validation.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.el</groupId>
                <artifactId>jakarta.el-api</artifactId>
                <version>${jakarta.el.version}</version>
            </dependency>
            <dependency>
                <groupId>org.javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>${javassist.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate-validator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.8.1</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.containers</groupId>
                <artifactId>jersey-container-servlet</artifactId>
                <version>${jersey.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.containers</groupId>
                <artifactId>jersey-container-grizzly2-http</artifactId>
                <version>${jersey.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.inject</groupId>
                <artifactId>jersey-hk2</artifactId>
                <version>${jersey.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.media</groupId>
                <artifactId>jersey-media-json-jackson</artifactId>
                <version>${jersey.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.grizzly</groupId>
                <artifactId>grizzly-http-servlet</artifactId>
                <version>${grizzly.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hamcrest</groupId>
                <artifactId>hamcrest-all</artifactId>
                <version>${hamcrest.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.servlet</groupId>
                <artifactId>jakarta.servlet-api</artifactId>
                <version>${jakarta.servlet.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.ws.rs</groupId>
                <artifactId>jakarta.ws.rs-api</artifactId>
                <version>${jakarta.ws.rs.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.easymock</groupId>
            <artifactId>easymock</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>sonar</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <!-- <argLine>-enableassertions</argLine> -->
                            <argLine>${surefireArgLineExtra}</argLine>
                            <systemProperties>
                                <property>
                                    <name>java.util.logging.config.file</name>
                                    <value>logging.properties</value>
                                </property>
                            </systemProperties>
                            <properties>
                              <property>
                                <name>listener</name>
                                <value>org.sonar.java.jacoco.JUnitListener</value>
                              </property>
                            </properties>
                        </configuration>
                        <dependencies>
                            <dependency>
                                <groupId>org.jacoco</groupId>
                                <artifactId>jacoco-maven-plugin</artifactId>
                                <version>0.7.4.201502262128</version>
                            </dependency>
                        </dependencies>
                    </plugin>
                    <plugin>
                        <groupId>org.jacoco</groupId>
                        <artifactId>jacoco-maven-plugin</artifactId>
                        <version>0.7.4.201502262128</version>
                        <executions>
                            <!-- Prepares the property pointing to the JaCoCo runtime agent which
                                 is passed as VM argument when Maven the Surefire plugin is executed. -->
                            <execution>
                                <id>agent-jacoco-property</id>
                                <phase>test</phase>
                                <goals>
                                    <goal>prepare-agent</goal>
                                </goals>
                                <configuration>
                                    <!-- Sets the path to the file which contains the execution data. -->
                                    <destFile>${project.build.directory}/jacoco.exec</destFile>
                                    <!-- Sets the name of the property containing the settings for JaCoCo
                                         runtime agent. -->
                                    <propertyName>surefireArgLineExtra</propertyName>
                                </configuration>
                            </execution>
                            <!-- Ensures that the code coverage report for unit tests is created
                                 after unit tests have been run. -->
                            <execution>
                                <id>post-unit-test</id>
                                <phase>test</phase>
                                <goals>
                                    <goal>report</goal>
                                </goals>
                                <configuration>
                                    <!-- Sets the path to the file which contains the execution data. -->
                                    <dataFile>${project.build.directory}/jacoco.exec</dataFile>
                                    <!-- Sets the output directory for the code coverage report. -->
                                    <outputDirectory>${project.reporting.outputDirectory}/jacoco</outputDirectory>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>sonar-maven-plugin</artifactId>
                        <version>2.5</version>
                    </plugin>
                </plugins>
            </build>
            <dependencies>
               <dependency>
                  <groupId>org.codehaus.sonar-plugins.java</groupId>
                  <artifactId>sonar-jacoco-listeners</artifactId>
                  <version>3.0</version>
                  <scope>test</scope>
               </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>javadoc-jdk8+</id>
            <activation>
                <jdk>[1.8,)</jdk>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <javadoc.options>-Xdoclint:none</javadoc.options>
            </properties>
        </profile>
    </profiles>
</project>
