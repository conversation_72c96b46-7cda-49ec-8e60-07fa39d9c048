<!--
/**
 * 故障分类 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2022-05-30 14:36:13
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
	<title th:text="${lang.translate('故障分类')}">故障分类</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style=" ">


    <form id="data-form" lay-filter="data-form" class="layui-form model-form">

<!--        <input name="id" id="id"  type="hidden"/>-->

         <!--开始：group 循环-->

            <div class="layui-col-xs12 form-column" >

                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('主键')}">主键</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input  style="background-color:#e6e6e6" lay-filter="id" disabled="disabled" id="id" name="id" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('主键') }" type="text" class="layui-input"   />
                    </div>
                </div>


                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('路径')}">路径</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input  style="background-color:#e6e6e6" lay-filter="hierarchyName" disabled="disabled" id="hierarchyName" name="hierarchyName" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('路径') }" type="text" class="layui-input"   />
                    </div>
                </div>


                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('名称')}">名称</div><div class="layui-required">*</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="repairName" id="repairName" name="repairName" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('名称') }" type="text" class="layui-input"    lay-verify="|required"  />
                        </div>
                    </div>

                                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('编码')}">编码</div><div class="layui-required">*</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="repairCode" id="repairCode" name="repairCode" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('编码') }" type="text" class="layui-input"    lay-verify="|required"  />
                        </div>
                    </div>


                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('排序')}">排序</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="sort" id="sort" name="sort" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('排序') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="true" decimal="false" allow-negative="true" step="1.0"   scale="0" />
                        </div>
                    </div>


                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->

         <!--开始：group 循环-->

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column"  style="padding-top: 0px" >
                    <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('备注')}">备注</div></div>
                    <div class="layui-input-block ">
                        <textarea lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('备注') }" class="layui-textarea" style="height: 120px" ></textarea>
                    </div>
                </div>


                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->

        <!--结束：group循环-->

        <div style="height: 8px"></div>

        <div class="layui-form-item model-form-footer1">
            <button class="layui-btn" style="margin-right: 15px;margin-left:80px;margin-top:30px"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存')}">保存</button>
        </div>

    </form>


<!--<div class="model-form-footer1">-->
<!--    <button  class="layui-btn1" style="margin-right: 15px;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存')}">保存</button>-->
<!--</div>-->

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>

<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var VALIDATE_CONFIG={"repairName":{"labelInForm":"名称","inputType":"text_input","required":true},"repairCode":{"labelInForm":"编码","inputType":"text_input","required":true}};
    var AUTH_PREFIX="eam_repair_category";


</script>



<script th:src="'/business/eam/repair_category/repair_category_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/repair_category/repair_category_form.js?'+${cacheKey}"></script>

</body>
</html>
