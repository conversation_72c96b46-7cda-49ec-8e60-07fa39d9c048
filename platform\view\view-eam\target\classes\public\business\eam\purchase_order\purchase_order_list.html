<!--
/**
 * 采购清单 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2024-04-23 14:02:21
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
    <title th:text="${lang.translate('采购清单')}">采购清单</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style=" display: none; ">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 业务编码 , businessCode ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('业务编码')}" class="search-label businessCode-label">业务编码</span><span class="search-colon">:</span></div>
                        <input id="businessCode" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 物品编码 , code ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('物品编码')}" class="search-label code-label">物品编码</span><span class="search-colon">:</span></div>
                        <input id="code" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 物品名称 , name ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('物品名称')}" class="search-label name-label">物品名称</span><span class="search-colon">:</span></div>
                        <input id="name" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 物品档案 , goodsId ,typeName=button, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('物品档案')}" class="search-label goodsId-label">物品档案</span><span class="search-colon">:</span></div>
                            <input lay-filter="goodsId" id="goodsId" name="goodsId"  type="hidden" class="layui-input"    lay-verify="|required"   />
                            <button id="goodsId-button" type="button" action-type="" class="layui-btn layui-btn-primary " style="width: 180px"><span th:text="${lang.translate('选择物品')}" th:default-label="${lang.translate('选择物品')}">按钮文本</span></button>
                    </div>
                    <!-- 物品类型 , goodsType ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('物品类型')}" class="search-label goodsType-label">物品类型</span><span class="search-colon">:</span></div>
                        <div id="goodsType" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.AssetPurchaseAssetTypeEnum')}" style="width:180px" extraParam="{}"></div>
                    </div>
                    <!-- 存放类型 , storageType ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('存放类型')}" class="search-label storageType-label">存放类型</span><span class="search-colon">:</span></div>
                        <div id="storageType" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.AssetPurchaseStorageTypeEnum')}" style="width:180px" extraParam="{}"></div>
                    </div>
                    <!-- 采购数量 , purchaseNumber ,typeName=number_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('采购数量')}" class="search-label purchaseNumber-label">采购数量</span><span class="search-colon">:</span></div>
                            <input id="purchaseNumber" class="layui-input search-input" style="width: 180px" type="text" autocomplete="off" input-type="number_input" integer="true" decimal="false" allow-negative="true" step="1.0" min-value="" max-value="" scale="0"/>
                    </div>
                    <!-- 采购单价 , unitPrice ,typeName=number_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('采购单价')}" class="search-label unitPrice-label">采购单价</span><span class="search-colon">:</span></div>
                            <input id="unitPrice" class="layui-input search-input" style="width: 180px" type="text" autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0" min-value="" max-value="" scale="2"/>
                    </div>
                    <!-- 备注 , notes ,typeName=text_area, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('备注')}" class="search-label notes-label">备注</span><span class="search-colon">:</span></div>
                        <input id="notes" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 采购单 , applyId ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('采购单')}" class="search-label applyId-label">采购单</span><span class="search-colon">:</span></div>
                        <div id="applyId" th:data="${'/service-eam/eam-purchase-apply/query-paged-list'}" style="width:180px" extraParam="{}"></div>
                    </div>
                    <!-- 验收单 , checkId ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('验收单')}" class="search-label checkId-label">验收单</span><span class="search-colon">:</span></div>
                        <input id="checkId" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 制单人 , originatorId ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('制单人')}" class="search-label originatorId-label">制单人</span><span class="search-colon">:</span></div>
                        <input id="originatorId" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 修改人ID , updateBy ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('修改人ID')}" class="search-label updateBy-label">修改人ID</span><span class="search-colon">:</span></div>
                        <input id="updateBy" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 选择数据 , selectedCode ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('选择数据')}" class="search-label selectedCode-label">选择数据</span><span class="search-colon">:</span></div>
                        <input id="selectedCode" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>


                </div>
            </div>


            <!-- 按钮区域 -->
            <div id="search-area" class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索','','cmp:table.search')}">搜索</span></button>
            </div>
        </div>

        <div id="table-area" style="margin-top: 0px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
        <button th:if="${perm.checkAuth('eam_purchase_order:create')}" id="add-button" class="layui-btn icon-btn layui-btn-sm create-new-button " lay-event="create"><i class="layui-icon">&#xe654;</i><span th:text="${lang.translate('新建','','cmp:table.button')}">新建</span></button>
    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">

    <button th:if="${perm.checkAuth('eam_purchase_order:view-form')}" class="layui-btn layui-btn-primary layui-btn-xs ops-view-button " lay-event="view"  data-id="{{d.id}}"> <span th:text="${lang.translate('查看','','cmp:table.ops')}">查看</span></button>
    <button th:if="${perm.checkAnyAuth('eam_purchase_order:update','eam_purchase_order:save')}" class="layui-btn layui-btn-primary layui-btn-xs ops-edit-button " lay-event="edit"data-id="{{d.id}}"><span th:text="${lang.translate('修改','','cmp:table.ops')}">修改</span></button>


    <button th:if="${perm.checkAuth('eam_purchase_order:delete')}" class="layui-btn layui-btn-xs layui-btn-danger ops-delete-button " lay-event="del" data-id="{{d.id}}"><span th:text="${lang.translate('删除','','cmp:table.ops')}">删除</span></button>


</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
    var SELECT_GOODSTYPE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetPurchaseAssetTypeEnum')}]];
    var SELECT_STORAGETYPE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetPurchaseStorageTypeEnum')}]];
    var AUTH_PREFIX="eam_purchase_order";

    // 页面类型
    var PAGE_TYPE = [[${pageType}]] ;
    // selectedCode
    var SELECTED_CODE = [[${selectedCode}]] ;
    // ownerId
    var OWNER_ID = [[${ownerId}]] ;

</script>

<script th:src="'/business/eam/purchase_order/purchase_order_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/purchase_order/purchase_order_list.js?'+${cacheKey}"></script>

</body>
</html>