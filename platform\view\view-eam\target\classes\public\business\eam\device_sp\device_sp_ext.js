/**
 * 备件清单 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2023-07-15 10:59:14
 */

layui.config({
    dir: layuiPath,
    base: '/module/'
}).extend({
    xmSelect: 'xm-select/xm-select',
    foxnicUpload: 'upload/foxnic-upload'
})
//
layui.define(['form', 'table', 'util', 'settings', 'admin', 'upload','foxnic','xmSelect','laydate','foxnicUpload','dropdown'],function () {

    var admin = layui.admin,settings = layui.settings,form = layui.form,upload = layui.upload,laydate= layui.laydate,dropdown=layui.dropdown;
    table = layui.table,layer = layui.layer,util = layui.util,fox = layui.foxnic,xmSelect = layui.xmSelect,foxup=layui.foxnicUpload;

    //模块基础路径
    const moduleURL="/service-eam/eam-device-sp";

    var timestamp = Date.parse(new Date());
    //列表页的扩展
    var list={
        /**
         * 列表页初始化前调用
         * */
        beforeInit:function () {
            console.log("list:beforeInit");
        },
        /**
         * 按事件名称移除表格按钮栏的按钮
         * */
        removeOperationButtonByEvent(event) {
            var template=$("#tableOperationTemplate");
            var content=template.text();
            content=content.split("\n");
            var buttons=[]
            for (let i = 0; i < content.length ; i++) {
                if(content[i] && content[i].indexOf("lay-event=\""+event+"\"")==-1) {
                    buttons.push(content[i]);
                }
            }
            template.text(buttons.join("\n"))
        },
        /**
         * 表格渲染前调用
         * @param cfg 表格配置参数
         * */
        beforeTableRender:function (cfg){
            cfg.cellMinWidth=160;;
        },
        /**
         * 表格渲染后调用
         * */
        afterTableRender :function (){

        },
        afterSearchInputReady: function() {
            console.log("list:afterSearchInputReady");
        },
        /**
         * 对话框打开之前调用，如果返回 null 则不打开对话框
         * */
        beforeDialog:function (param){
            //param.title="覆盖对话框标题";
            return param;
        },
        /**
         * 对话框回调，表单域以及按钮 会自动改变为选中的值，此处处理额外的逻辑即可
         * */
        afterDialog:function (param,result) {
            console.log('dialog',param,result);
        },
        /**
         * 当下拉框别选择后触发
         * */
        onSelectBoxChanged:function(id,selected,changes,isAdd) {
            console.log('onSelectBoxChanged',id,selected,changes,isAdd);
        },
        /**
         * 当日期选择组件选择后触发
         * */
        onDatePickerChanged:function(id,value, date, endDate) {
            console.log('onDatePickerChanged',id,value, date, endDate);
        },
        /**
         * 查询前调用
         * @param conditions 复合查询条件
         * @param param 请求参数
         * @param location 调用的代码位置
         * */
        beforeQuery:function (conditions,param,location) {
            console.log('beforeQuery',conditions,param,location);
            return true;
        },
        /**
         * 查询结果渲染后调用
         * */
        afterQuery : function (data) {

        },
        /**
         * 单行数据刷新后调用
         * */
        afterRefreshRowData: function (data,remote,context) {

        },
        /**
         * 进一步转换 list 数据
         * */
        templet:function (field,value,r) {
            if(value==null) return "";
            return value;
        },
        /**
         * 表单页面打开时，追加更多的参数信息
         * */
        makeFormQueryString:function(data,queryString,action) {
            return queryString;
        },
        /**
         * 在新建或编辑窗口打开前调用，若返回 false 则不继续执行后续操作
         * */
        beforeEdit:function (data) {
            console.log('beforeEdit',data);
            return true;
        },
        /**
         * 单行删除前调用，若返回false则不执行后续操作
         * */
        beforeSingleDelete:function (data) {
            console.log('beforeSingleDelete',data);
            return true;
        },
        afterSingleDelete:function (data){
            console.log('beforeSingleDelete',data);
            return true;
        },
        /**
         * 批量删除前调用，若返回false则不执行后续操作
         * */
        beforeBatchDelete:function (selected) {
            console.log('beforeBatchDelete',selected);
            return true;
        },
        /**
         * 批量删除后调用，若返回false则不执行后续操作
         * */
        afterBatchDelete:function (data) {
            console.log('afterBatchDelete',data);
            return true;
        },
        /**
         * 工具栏按钮事件前调用，如果返回 false 则不执行后续代码
         * */
        beforeToolBarButtonEvent:function (selected,obj) {
            console.log('beforeToolBarButtonEvent',selected,obj);
            return true;
        },
        /**
         * 列表操作栏按钮事件前调用，如果返回 false 则不执行后续代码
         * */
        beforeRowOperationEvent:function (data,obj) {
            console.log('beforeRowOperationEvent',data,obj);
            return true;
        },
        /**
         * 表格右侧操作列更多按钮事件
         * */
        moreAction:function (menu,data, it){
            console.log('moreAction',menu,data,it);
        },
        bathSure:function (selected,it){
            console.log('bathSure',selected,it);
            if(selected.length==0){
                top.layer.msg("请选择备件", {time: 2000});
                return
            }
            var btn=$('#bath-sure');
            var ps={ids:JSON.stringify(selected)}
            var url="batch-sure"
            var api=moduleURL+"/"+url;
            top.layer.confirm(fox.translate('确定进行批量确认操作吗？'), function (i) {
                top.layer.close(i);
                admin.post(api, ps, function (r) {
                    if (r.success) {
                        top.layer.msg("操作成功", {time: 1000});
                        window.module.refreshTableData();
                    } else {
                        top.layer.msg(r.message, {time: 2000});
                    }
                }, {delayLoading: 1000, elms: [btn]});
            });
        },
        modifyStatus:function (data){
            console.log('modifyStatus',data);
            admin.putTempData('eam-device-sp-status-form-data', {});
            admin.putTempData('eam-device-sp-status-form-data-form-action', "create",true);
            admin.popupCenter({
                title: "修改状态",
                resize: false,
                offset: [20,null],
                area: ["80%","85%"],
                type: 2,
                id:"eam-device-sp-status-form-data-win",
                content: '/business/eam/device_sp_status/device_sp_status_form.html?spId='+data.id,
                finish: function () {
                    window.module.refreshTableData();
                }
            });
        },

        usedDetail:function (data){
            console.log('usedDetail',data);
            admin.popupCenter({
                title: "使用记录",
                resize: false,
                offset: [20,null],
                area: ["80%","85%"],
                type: 2,
                id:"eam-device-sp-rcd-list-data-win",
                content: '/business/eam/device_sp_rcd/device_sp_rcd_s_list.html?spId='+data.id,
                finish: function () {
                }
            });

        },
        /**
         * 末尾执行
         */
        ending:function() {

        }
    }

    //表单页的扩展
    var form={
        /**
         * 表单初始化前调用 , 并传入表单数据
         * */
        beforeInit:function (action,data) {
            //获取参数，并调整下拉框查询用的URL
            //var companyId=admin.getTempData("companyId");
            //fox.setSelectBoxUrl("employeeId","/service-hrm/hrm-employee/query-paged-list?companyId="+companyId);
            console.log("form:beforeInit")
            $("#code").attr('placeholder',"自动填充");
            $("#code").attr("disabled","disabled").css("background-color","#e6e6e6");
        },
        /**
         * 窗口调节前
         * */
        beforeAdjustPopup:function (arg) {
            console.log('beforeAdjustPopup');
            return true;
        },
        /**
         * 表单数据填充前
         * */
        beforeDataFill:function (data) {
            console.log('beforeDataFill',data);
        },
        /**
         * 表单数据填充后
         * */
        afterDataFill:function (data) {
            console.log('afterDataFill',data);

            //填充物品档案
            if(data&&data.goods){
                var selData=data.goods;
                if(selData){
                    $("#goodId").val(selData.id);
                    var str=""
                    if(selData.name){
                        str=selData.name;
                        if(selData.model){
                            str=str+",型号:"+selData.model;
                        }
                        if(selData.code){
                            str=str+",物品编号:"+selData.code;
                        }
                    }
                    $("#goodId-button").html(str);
                }
            }

        },
        /**
         * 对话框打开之前调用，如果返回 null 则不打开对话框
         * */
        beforeDialog:function (param){
            //param.title="覆盖对话框标题";
            return param;
        },
        /**
         * 对话框回调，表单域以及按钮 会自动改变为选中的值，此处处理额外的逻辑即可
         * */
        afterDialog:function (param,result) {
            console.log('dialog',param,result);
        },
        /**
         * 当下拉框别选择后触发
         * */
        onSelectBoxChanged:function(id,selected,changes,isAdd) {
            console.log('onSelectBoxChanged',id,selected,changes,isAdd);
        },
        /**
         * 当日期选择组件选择后触发
         * */
        onDatePickerChanged:function(id,value, date, endDate) {
            console.log('onDatePickerChanged',id,value, date, endDate);
        },
        onRadioBoxChanged:function(id,data,checked) {
            console.log('onRadioChanged',id,data,checked);
        },
        onCheckBoxChanged:function(id,data,checked) {
            console.log('onCheckBoxChanged',id,data,checked);
        },

        /**
         * 在流程提交前处理表单数据
         * */
        processFormData4Bpm:function(processInstanceId,param,callback) {
            // 设置流程变量，并通过回调返回
            var variables={};
            // 此回调是必须的，否则流程提交会被中断
            callback(variables);
        },
        /**
         * 数据提交前，如果返回 false，停止后续步骤的执行
         * */
        beforeSubmit:function (data) {
            console.log("beforeSubmit",data);
            return true;
        },
        /**
         * 数据提交后窗口关闭前，如果返回 false，停止后续步骤的执行
         * */
        betweenFormSubmitAndClose:function (param,result) {
            console.log("betweenFormSubmitAndClose",result);
            return true;
        },
        goodsSelect:function (data,el,it) {
            console.log("chosenGoods",data,el,it);
            var data={};
            var ownerType="stock"
            var ownerCode="stock"
            var ownerTmpId="ownerTmpId";
            var operType="eam_asset_stock_goods_select"
            var pageType="create";
            admin.putTempData('eam-goods-stock-form-data-form-action', "create",true);
            var queryString="?ownerType="+ownerType+"&selectedCode="+timestamp+"&ownerCode="+ownerCode+"&ownerTmpId="+ownerTmpId+"&operType="+operType+"&pageType="+pageType;
            var formTop=2
            var index=admin.popupCenter({
                title: "选择物品档案",
                resize: false,
                offset: [formTop,null],
                area: ["80%","90%"],
                type: 2,
                id:"eam-asset-select-data-popup-index",
                content: '/business/eam/goods_stock/goods_stock_select_tree.html'+queryString,
                finish: function (data) {
                    var selData= admin.getTempData('eam-asset-select-data-popup-data');
                    console.log("finish",selData);
                    if(selData){
                        $("#goodId").val(selData.id);
                        var str=""
                        if(selData.name){
                            str=selData.name;
                            if(selData.model){str=str+",型号:"+selData.model;
                            }
                            if(selData.code){
                                str=str+",物品编号:"+selData.code;
                            }
                        }
                        $("#goodId-button").html(str);
                    }
                }
            });
            admin.putTempData('eam-asset-select-data-popup-index', index);

        },

        /**
         * 数据提交后执行
         * */
        afterSubmit:function (param,result) {
            console.log("afterSubmitt",param,result);
        },

        /**
         * 文件上传组件回调
         *  event 类型包括：
         *  afterPreview ：文件选择后，未上传前触发；
         *  afterUpload ：文件上传后触发
         *  beforeRemove ：文件删除前触发
         *  afterRemove ：文件删除后触发
         * */
        onUploadEvent: function(e) {
            console.log("onUploadEvent",e);
        },
        /**
         * 末尾执行
         */
        ending:function() {

        }
    }
    //
    window.pageExt={form:form,list:list};
});