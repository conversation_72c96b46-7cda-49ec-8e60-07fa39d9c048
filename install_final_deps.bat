@echo off
echo Installing final core dependencies...

echo Installing foxnic-api...
call apache-maven-3.6.3\bin\mvn.cmd install:install-file -Dfile=temp_download\app\lib\foxnic-api-1.8.0.RELEASE.jar -DgroupId=com.foxnicweb -DartifactId=foxnic-api -Dversion=1.8.0.RELEASE -Dpackaging=jar
if %errorlevel% neq 0 echo Failed to install foxnic-api, continuing...

echo Installing foxnic-commons...
call apache-maven-3.6.3\bin\mvn.cmd install:install-file -Dfile=temp_download\app\lib\foxnic-commons-1.8.0.RELEASE.jar -DgroupId=com.foxnicweb -DartifactId=foxnic-commons -Dversion=1.8.0.RELEASE -Dpackaging=jar
if %errorlevel% neq 0 echo Failed to install foxnic-commons, continuing...

echo Installing foxnic-dao...
call apache-maven-3.6.3\bin\mvn.cmd install:install-file -Dfile=temp_download\app\lib\foxnic-dao-1.8.0.RELEASE.jar -DgroupId=com.foxnicweb -DartifactId=foxnic-dao -Dversion=1.8.0.RELEASE -Dpackaging=jar
if %errorlevel% neq 0 echo Failed to install foxnic-dao, continuing...

echo Installing framework-boot...
call apache-maven-3.6.3\bin\mvn.cmd install:install-file -Dfile=temp_download\app\lib\framework-boot-1.8.0.RELEASE.jar -DgroupId=com.foxnicweb -DartifactId=framework-boot -Dversion=1.8.0.RELEASE -Dpackaging=jar
if %errorlevel% neq 0 echo Failed to install framework-boot, continuing...

echo Installing framework-view...
call apache-maven-3.6.3\bin\mvn.cmd install:install-file -Dfile=temp_download\app\lib\framework-view-1.8.0.RELEASE.jar -DgroupId=com.foxnicweb -DartifactId=framework-view -Dversion=1.8.0.RELEASE -Dpackaging=jar
if %errorlevel% neq 0 echo Failed to install framework-view, continuing...

echo Installing proxy...
call apache-maven-3.6.3\bin\mvn.cmd install:install-file -Dfile=temp_download\app\lib\proxy-1.8.0.RELEASE.jar -DgroupId=com.foxnicweb -DartifactId=proxy -Dversion=1.8.0.RELEASE -Dpackaging=jar
if %errorlevel% neq 0 echo Failed to install proxy, continuing...

echo All core dependencies installed successfully!
echo Now you can use Maven 3.6.3 to manage the project dependencies.
pause
