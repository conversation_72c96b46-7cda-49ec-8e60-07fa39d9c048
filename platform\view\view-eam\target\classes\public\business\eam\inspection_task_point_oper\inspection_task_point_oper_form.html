<!--
/**
 * 巡检点批量操作 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2024-03-10 14:18:29
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
	<title th:text="${lang.translate('巡检点批量操作')}">巡检点批量操作</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="opacity:0">

        <input name="id" id="id"  type="hidden"/>

         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-8295-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >

                <!-- radio_box : 巡检状态 ,  isPointStatus  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('巡检状态')}">巡检状态</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input input-type="radio" type="radio" name="isPointStatus" lay-filter="isPointStatus" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==1)}">
                    </div>
                </div>
            
                <!-- radio_box : 处理动作 ,  isActionLabel  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('处理动作')}">处理动作</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input input-type="radio" type="radio" name="isActionLabel" lay-filter="isActionLabel" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==1)}">
                    </div>
                </div>
            
                <!-- radio_box : 巡检人 ,  isInspectorId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('巡检人')}">巡检人</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input input-type="radio" type="radio" name="isInspectorId" lay-filter="isInspectorId" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==1)}">
                    </div>
                </div>
            
                <!-- radio_box : 巡检结果 ,  isContent  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('巡检结果')}">巡检结果</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input input-type="radio" type="radio" name="isContent" lay-filter="isContent" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==1)}">
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >

                <!-- radio_box : 巡检状态 ,  pointStatus  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('巡检状态')}">巡检状态</div></div>
                    <div class="layui-input-block ">
                        <input input-type="radio" type="radio" name="pointStatus" lay-filter="pointStatus" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.eam.InspectionTaskPointStatusEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==0)}">
                    </div>
                </div>
            
                <!-- select_box : 处理动作 ,  actionLabel  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('处理动作')}">处理动作</div></div>
                    <div class="layui-input-block ">
                        <div id="actionLabel" input-type="select" th:data="${'/service-eam/eam-inspection-process-action/query-list'}" extraParam="{}"></div>
                    </div>
                </div>
            
                <!-- button : 巡检人 ,  inspectorId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('巡检人')}">巡检人</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="inspectorId" id="inspectorId" name="inspectorId"  type="hidden" class="layui-input"   />
                        <button id="inspectorId-button" type="button" action-type="emp-dialog" class="layui-btn   " style="width: 100%" default-width="100%" auto-width="false"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择人员')}" th:default-label="${lang.translate('请选择人员')}">按钮文本</span></button>
                    </div>
                </div>
            
                <!-- text_input : 巡检结果 ,  content -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('巡检结果')}">巡检结果</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="content" id="content" name="content" th:placeholder="${ lang.translate('请输入'+'巡检结果') }" type="text" class="layui-input"  />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-6953-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column"  style="padding-top: 0px" >

                <!-- text_input : 备注 ,  notes -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('备注')}">备注</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入'+'备注') }" type="text" class="layui-input"  />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 80px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消','','form.button')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('eam_inspection_task_point_oper:create','eam_inspection_task_point_oper:update','eam_inspection_task_point_oper:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存','','form.button')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var RADIO_ISPOINTSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var RADIO_POINTSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.InspectionTaskPointStatusEnum')}]];
    var RADIO_ISACTIONLABEL_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var RADIO_ISINSPECTORID_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var RADIO_ISCONTENT_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var VALIDATE_CONFIG={"isActionLabel":{"labelInForm":"处理动作","inputType":"radio_box","required":true},"isInspectorId":{"labelInForm":"巡检人","inputType":"radio_box","required":true},"isContent":{"labelInForm":"巡检结果","inputType":"radio_box","required":true},"isPointStatus":{"labelInForm":"巡检状态","inputType":"radio_box","required":true}};
    var AUTH_PREFIX="eam_inspection_task_point_oper";

    // taskId
    var TASK_ID = [[${taskId}]] ;

</script>



<script th:src="'/business/eam/inspection_task_point_oper/inspection_task_point_oper_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/inspection_task_point_oper/inspection_task_point_oper_form.js?'+${cacheKey}"></script>

</body>
</html>