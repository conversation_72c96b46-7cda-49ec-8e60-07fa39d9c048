<!--
/**
 * 维修工单 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2024-03-25 13:23:33
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
    <title th:text="${lang.translate('维修工单')}">维修工单</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 申请单 , orderId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 维修班组 , groupId ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('维修班组')}" class="search-label groupId-label">维修班组</span><span class="search-colon">:</span></div>
                        <div id="groupId" th:data="${'/service-eam/eam-repair-group/query-list'}" style="width:180px" extraParam="{}"></div>
                    </div>
                    <!-- 维修人员 , executorId ,typeName=select_box, isHideInSearch=true -->
                    <!-- 维修费用 , repairCost ,typeName=number_input, isHideInSearch=true -->
                    <!-- 故障原因 , causeReasonCode ,typeName=select_box, isHideInSearch=true -->
                    <!-- 完成时间 , finishTime ,typeName=date_input, isHideInSearch=true -->
                    <!-- 维修备注 , notes ,typeName=text_area, isHideInSearch=true -->
                    <!-- 图片 , pictureId ,typeName=upload, isHideInSearch=true -->
                    <!-- 制单人员 , originatorId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 跟随验证 , withAcceptance ,typeName=text_input, isHideInSearch=true -->
                    <!-- 设备 , assetId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 修改人ID , updateBy ,typeName=text_input, isHideInSearch=true -->
                    <!-- 选择数据 , selectedCode ,typeName=text_input, isHideInSearch=true -->
                    <!-- 类型 , ownerType ,typeName=text_input, isHideInSearch=true -->
                    <!-- 维修编号 , businessCode ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('维修编号')}" class="search-label businessCode-label">维修编号</span><span class="search-colon">:</span></div>
                        <input id="businessCode" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 订单名称 , orderName ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('订单名称')}" class="search-label orderName-label">订单名称</span><span class="search-colon">:</span></div>
                        <input id="orderName" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 订单编号 , orderBusinessCode ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('订单编号')}" class="search-label orderBusinessCode-label">订单编号</span><span class="search-colon">:</span></div>
                        <input id="orderBusinessCode" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 开始时间 , startTime ,typeName=date_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('开始时间')}" class="search-label startTime-label">开始时间</span><span class="search-colon">:</span></div>
                            <input type="text" id="startTime-begin" style="width: 180px" lay-verify="date" th:placeholder="${lang.translate('开始日期')}" autocomplete="off" class="layui-input search-input search-date-input"  readonly >
                            <span class="search-dash">-</span>
                            <input type="text" id="startTime-end"  style="width: 180px"  lay-verify="date" th:placeholder="${lang.translate('结束日期')}" autocomplete="off" class="layui-input search-input search-date-input" readonly>
                    </div>


                </div>
            </div>


            <!-- 按钮区域 -->
            <div id="search-area" class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索','','cmp:table.search')}">搜索</span></button>
                <button id="search-button-advance" class="layui-btn layui-btn-primary icon-btn search-button-advance"><i class="layui-icon">&#xe671;</i><span th:text="${lang.translate('更多','','cmp:table.search')}">更多</span></button>
            </div>
        </div>

        <div id="table-area" style="margin-top: 42px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
        <button th:if="${perm.checkAuth('eam_repair_order_act:create')}" id="add-button" class="layui-btn icon-btn layui-btn-sm create-new-button " lay-event="create"><i class="layui-icon">&#xe654;</i><span th:text="${lang.translate('新建','','cmp:table.button')}">新建</span></button>
    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">

    <button th:if="${perm.checkAuth('eam_repair_order_act:view-form')}" class="layui-btn layui-btn-primary layui-btn-xs ops-view-button " lay-event="view"  data-id="{{d.id}}"> <span th:text="${lang.translate('查看','','cmp:table.ops')}">查看</span></button>
    <button th:if="${perm.checkAnyAuth('eam_repair_order_act:update','eam_repair_order_act:save')}" class="layui-btn layui-btn-primary layui-btn-xs ops-edit-button " lay-event="edit"data-id="{{d.id}}"><span th:text="${lang.translate('修改','','cmp:table.ops')}">修改</span></button>



    <button th:if="${perm.checkAuth('eam_repair_order_act:start')}"class="layui-btn layui-btn-xs  start-button " lay-event="start" data-id="{{d.id}}"><span th:text="${lang.translate('开始维修','','cmp:table.ops')}">开始维修</span></button>
    <button th:if="${perm.checkAuth('eam_repair_order_act:maintenance')}"class="layui-btn layui-btn-xs  maintenance-button " lay-event="maintenance" data-id="{{d.id}}"><span th:text="${lang.translate('维修','','cmp:table.ops')}">维修</span></button>
    <button th:if="${perm.checkAuth('eam_repair_order_act:finish')}"class="layui-btn layui-btn-xs  finish-button " lay-event="finish" data-id="{{d.id}}"><span th:text="${lang.translate('结束维修','','cmp:table.ops')}">结束维修</span></button>
    <button th:if="${perm.checkAuth('eam_repair_order_act:acceptance')}"class="layui-btn layui-btn-xs  acceptance-button " lay-event="acceptance" data-id="{{d.id}}"><span th:text="${lang.translate('验收单','','cmp:table.ops')}">验收单</span></button>
    <button th:if="${perm.checkAuth('eam_repair_order_act:cancel')}"class="layui-btn layui-btn-xs  cancel-button " lay-event="cancel" data-id="{{d.id}}"><span th:text="${lang.translate('取消 ','','cmp:table.ops')}">取消 </span></button>
    <button class="layui-btn layui-btn-xs  repair-bill " lay-event="repair-bill" data-id="{{d.id}}"><span th:text="${lang.translate(' 维修单据','','cmp:table.ops')}"> 维修单据</span></button>

</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
    var AUTH_PREFIX="eam_repair_order_act";

    // 单据ID
    var REPAIR_STATUS = [[${repairStatus}]] ;

</script>

<script th:src="'/business/eam/repair_order_act/repair_order_act_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/repair_order_act/repair_order_act_list.js?'+${cacheKey}"></script>

</body>
</html>