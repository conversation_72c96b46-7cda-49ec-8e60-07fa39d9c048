<?xml version='1.0' encoding='UTF-8'?>

<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-plugins</artifactId>
    <version>22</version>
    <relativePath>../maven-plugins/pom.xml</relativePath>
  </parent>

  <artifactId>maven-clean-plugin</artifactId>
  <version>2.5</version>
  <packaging>maven-plugin</packaging>

  <name>Maven Clean Plugin</name>
  <description>
    The Maven Clean Plugin is a plugin that removes files generated at build-time in a project's directory.
  </description>
  <inceptionYear>2001</inceptionYear>

  <prerequisites>
    <maven>${mavenVersion}</maven>
  </prerequisites>

  <scm>
    <connection>scm:svn:http://svn.apache.org/repos/asf/maven/plugins/tags/maven-clean-plugin-2.5</connection>
    <developerConnection>scm:svn:https://svn.apache.org/repos/asf/maven/plugins/tags/maven-clean-plugin-2.5</developerConnection>
    <url>http://svn.apache.org/viewvc/maven/plugins/tags/maven-clean-plugin-2.5</url>
  </scm>
  <issueManagement>
    <system>JIRA</system>
    <url>http://jira.codehaus.org/browse/MCLEAN</url>
  </issueManagement>

  <properties>
    <mavenVersion>2.0.6</mavenVersion>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-plugin-api</artifactId>
      <version>${mavenVersion}</version>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-utils</artifactId>
      <version>3.0</version>
    </dependency>

    <!-- Test -->
    <dependency>
      <groupId>org.apache.maven.shared</groupId>
      <artifactId>maven-plugin-testing-harness</artifactId>
      <version>1.1</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <profiles>
    <profile>
      <id>run-its</id>
      <build>
        <pluginManagement>
          <plugins>
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-invoker-plugin</artifactId>
              <configuration>
                <debug>true</debug>
                <addTestClassPath>true</addTestClassPath>
                <projectsDirectory>src/it</projectsDirectory>
                <cloneProjectsTo>${project.build.directory}/it</cloneProjectsTo>
                <pomIncludes>
                  <pomInclude>*/pom.xml</pomInclude>
                </pomIncludes>
                <preBuildHookScript>setup</preBuildHookScript>
                <postBuildHookScript>verify</postBuildHookScript>
                <localRepositoryPath>${project.build.directory}/local-repo</localRepositoryPath>
                <settingsFile>src/it/settings.xml</settingsFile>
                <goals>
                  <goal>clean</goal>
                </goals>
              </configuration>
            </plugin>
          </plugins>
        </pluginManagement>
      </build>
    </profile>
  </profiles>
</project>
