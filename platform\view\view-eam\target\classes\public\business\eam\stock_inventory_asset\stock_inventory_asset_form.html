<!--
/**
 * 库存资产 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2024-05-06 09:57:51
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
	<title th:text="${lang.translate('库存资产')}">库存资产</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="opacity:0">

        <input name="id" id="id"  type="hidden"/>

         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-5307-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >

                <!-- text_input : 物品类型 ,  withGoodsStockType -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('物品类型')}">物品类型</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="withGoodsStockType" id="withGoodsStockType" name="withGoodsStockType" th:placeholder="${ lang.translate('请输入'+'物品类型') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- text_input : 名称 ,  withName -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('名称')}">名称</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="withName" id="withName" name="withName" th:placeholder="${ lang.translate('请输入'+'名称') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- text_input : 仓库 ,  withWarehouse -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('仓库')}">仓库</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="withWarehouse" id="withWarehouse" name="withWarehouse" th:placeholder="${ lang.translate('请输入'+'仓库') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- text_input : 库位 ,  withPosition -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('库位')}">库位</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="withPosition" id="withPosition" name="withPosition" th:placeholder="${ lang.translate('请输入'+'库位') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- text_input : 数量 ,  withNumber -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('数量')}">数量</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="withNumber" id="withNumber" name="withNumber" th:placeholder="${ lang.translate('请输入'+'数量') }" type="text" class="layui-input"  />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >

                <!-- text_input : 厂商 ,  withManufacturer -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('厂商')}">厂商</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="withManufacturer" id="withManufacturer" name="withManufacturer" th:placeholder="${ lang.translate('请输入'+'厂商') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- text_input : 品牌 ,  withBrand -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('品牌')}">品牌</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="withBrand" id="withBrand" name="withBrand" th:placeholder="${ lang.translate('请输入'+'品牌') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- text_input : 编号 ,  withCode -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('编号')}">编号</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="withCode" id="withCode" name="withCode" th:placeholder="${ lang.translate('请输入'+'编号') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- text_input : 型号 ,  withModel -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('型号')}">型号</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="withModel" id="withModel" name="withModel" th:placeholder="${ lang.translate('请输入'+'型号') }" type="text" class="layui-input"  />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->

        <fieldset class="layui-elem-field layui-field-title form-group-title" id="random-3898-fieldset">
            <legend>盘点信息</legend>
        </fieldset>

        <div class="layui-row form-row" id="random-3898-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >

                <!-- select_box : 盘点状态 ,  inventoryStatus  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('盘点状态')}">盘点状态</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="inventoryStatus" input-type="select" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.AssetInventoryDetailStatusEnum')}" extraParam="{}"></div>
                    </div>
                </div>
            
                <!-- button : 盘点人员 ,  operId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('盘点人员')}">盘点人员</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="operId" id="operId" name="operId"  type="hidden" class="layui-input"    lay-verify="|required"   />
                        <button id="operId-button" type="button" action-type="emp-dialog" class="layui-btn   " style="width: 100%" default-width="100%" auto-width="false"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择人员')}" th:default-label="${lang.translate('请选择人员')}">按钮文本</span></button>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >

                <!-- number_input : 差异数量 ,  assetNumber  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('差异数量')}">差异数量</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="assetNumber" id="assetNumber" name="assetNumber" th:placeholder="${ lang.translate('请输入'+'差异数量') }" type="text" class="layui-input"    lay-verify="|required"   autocomplete="off" input-type="number_input" integer="true" decimal="false" allow-negative="true" step="1.0"   scale="0"  value="0.0" />
                    </div>
                </div>
            
                <!-- date_input : 盘点时间 ,  operTime  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('盘点时间')}">盘点时间</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input input-type="date" lay-filter="operTime" id="operTime" name="operTime"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择'+'盘点时间') }" type="text" class="layui-input"    lay-verify="|required"   />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-0551-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column"  style="padding-top: 0px" >

                <!-- text_area : 盘点备注 ,  inventoryNotes  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('盘点备注')}">盘点备注</div></div>
                    <div class="layui-input-block ">
                        <textarea lay-filter="inventoryNotes" id="inventoryNotes" name="inventoryNotes" th:placeholder="${ lang.translate('请输入'+'盘点备注') }" class="layui-textarea" style="height: 120px" ></textarea>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 150px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消','','form.button')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('eam_stock_inventory_asset:create','eam_stock_inventory_asset:update','eam_stock_inventory_asset:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存','','form.button')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var SELECT_INVENTORYSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetInventoryDetailStatusEnum')}]];
    var VALIDATE_CONFIG={"assetNumber":{"labelInForm":"差异数量","inputType":"number_input","required":true},"inventoryStatus":{"labelInForm":"盘点状态","inputType":"select_box","required":true},"operId":{"labelInForm":"盘点人员","inputType":"button","required":true},"operTime":{"date":true,"labelInForm":"盘点时间","inputType":"date_input","required":true}};
    var AUTH_PREFIX="eam_stock_inventory_asset";


</script>



<script th:src="'/business/eam/stock_inventory_asset/stock_inventory_asset_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/stock_inventory_asset/stock_inventory_asset_form.js?'+${cacheKey}"></script>

</body>
</html>