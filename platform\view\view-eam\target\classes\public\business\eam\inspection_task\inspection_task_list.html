<!--
/**
 * 巡检任务 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2024-03-18 08:59:42
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
    <title th:text="${lang.translate('巡检任务')}">巡检任务</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 巡检计划 , planId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 任务状态 , taskStatus ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('任务状态')}" class="search-label taskStatus-label">任务状态</span><span class="search-colon">:</span></div>
                        <div id="taskStatus" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.InspectionTaskStatusEnum')}" style="width:180px" extraParam="{}"></div>
                    </div>
                    <!-- 计划单据 , planCode ,typeName=text_input, isHideInSearch=true -->
                    <!-- 巡检顺序 , planInspectionMethod ,typeName=select_box, isHideInSearch=true -->
                    <!-- 预计用时(时) , planCompletionTime ,typeName=number_input, isHideInSearch=true -->
                    <!-- 巡检备注 , planNotes ,typeName=text_input, isHideInSearch=true -->
                    <!-- 执行人 , executorId ,typeName=button, isHideInSearch=true -->
                    <!-- 应开始时间 , planStartTime ,typeName=date_input, isHideInSearch=true -->
                    <!-- 实际完成 , actFinishTime ,typeName=date_input, isHideInSearch=true -->
                    <!-- 完成用时(时) , actTotalCost ,typeName=number_input, isHideInSearch=true -->
                    <!-- 提醒时间(时) , remindTime ,typeName=number_input, isHideInSearch=true -->
                    <!-- 超时处理 , overtimeMethod ,typeName=select_box, isHideInSearch=true -->
                    <!-- 任务反馈 , content ,typeName=text_input, isHideInSearch=true -->
                    <!-- 备注 , notes ,typeName=text_input, isHideInSearch=true -->
                    <!-- 制单人 , originatorId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 修改人ID , updateBy ,typeName=text_input, isHideInSearch=true -->
                    <!-- 选择 , selectedCode ,typeName=text_input, isHideInSearch=true -->
                    <!-- 巡检点数(总数) , pointCount ,typeName=text_input, isHideInSearch=true -->
                    <!-- 巡检点数(待检) , pointWaitCount ,typeName=text_input, isHideInSearch=true -->
                    <!-- 巡检点数(正常) , pointNormalCount ,typeName=text_input, isHideInSearch=true -->
                    <!-- 巡检点数(异常) , pointAbormalCount ,typeName=text_input, isHideInSearch=true -->
                    <!-- 巡检班组 , groupId ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('巡检班组')}" class="search-label groupId-label">巡检班组</span><span class="search-colon">:</span></div>
                        <div id="groupId" th:data="${'/service-eam/eam-inspection-group/query-paged-list'}" style="width:180px" extraParam="{}"></div>
                    </div>
                    <!-- 任务单据 , taskCode ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('任务单据')}" class="search-label taskCode-label">任务单据</span><span class="search-colon">:</span></div>
                        <input id="taskCode" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 巡检名称 , planName ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('巡检名称')}" class="search-label planName-label">巡检名称</span><span class="search-colon">:</span></div>
                        <input id="planName" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 实际开始 , actStartTime ,typeName=date_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('实际开始')}" class="search-label actStartTime-label">实际开始</span><span class="search-colon">:</span></div>
                            <input type="text" id="actStartTime-begin" style="width: 180px" lay-verify="date" th:placeholder="${lang.translate('开始日期')}" autocomplete="off" class="layui-input search-input search-date-input"  readonly >
                            <span class="search-dash">-</span>
                            <input type="text" id="actStartTime-end"  style="width: 180px"  lay-verify="date" th:placeholder="${lang.translate('结束日期')}" autocomplete="off" class="layui-input search-input search-date-input" readonly>
                    </div>


                </div>
            </div>


            <!-- 按钮区域 -->
            <div id="search-area" class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索','','cmp:table.search')}">搜索</span></button>
                <button id="search-button-advance" class="layui-btn layui-btn-primary icon-btn search-button-advance"><i class="layui-icon">&#xe671;</i><span th:text="${lang.translate('更多','','cmp:table.search')}">更多</span></button>
            </div>
        </div>

        <div id="table-area" style="margin-top: 42px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
        <button th:if="${perm.checkAuth('eam_inspection_task:create')}" id="add-button" class="layui-btn icon-btn layui-btn-sm create-new-button " lay-event="create"><i class="layui-icon">&#xe654;</i><span th:text="${lang.translate('新建','','cmp:table.button')}">新建</span></button>
    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">

    <button th:if="${perm.checkAuth('eam_inspection_task:view-form')}" class="layui-btn layui-btn-primary layui-btn-xs ops-view-button " lay-event="view"  data-id="{{d.id}}"> <span th:text="${lang.translate('查看','','cmp:table.ops')}">查看</span></button>
    <button th:if="${perm.checkAnyAuth('eam_inspection_task:update','eam_inspection_task:save')}" class="layui-btn layui-btn-primary layui-btn-xs ops-edit-button " lay-event="edit"data-id="{{d.id}}"><span th:text="${lang.translate('修改','','cmp:table.ops')}">修改</span></button>


    <button th:if="${perm.checkAuth('eam_inspection_task:delete')}" class="layui-btn layui-btn-xs layui-btn-danger ops-delete-button " lay-event="del" data-id="{{d.id}}"><span th:text="${lang.translate('删除','','cmp:table.ops')}">删除</span></button>

    <button th:if="${perm.checkAuth('eam_inspection_task:finish')}"class="layui-btn layui-btn-xs  finish-button " lay-event="finish" data-id="{{d.id}}"><span th:text="${lang.translate('完成','','cmp:table.ops')}">完成</span></button>
    <button th:if="${perm.checkAuth('eam_inspection_task:cancel')}"class="layui-btn layui-btn-xs  cancel-button " lay-event="cancel" data-id="{{d.id}}"><span th:text="${lang.translate('取消','','cmp:table.ops')}">取消</span></button>
    <button class="layui-btn layui-btn-xs  detail-button " lay-event="detail" data-id="{{d.id}}"><span th:text="${lang.translate('详情','','cmp:table.ops')}">详情</span></button>

</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
    var SELECT_TASKSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.InspectionTaskStatusEnum')}]];
    var SELECT_PLANINSPECTIONMETHOD_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.InspectionMethodEnum')}]];
    var SELECT_OVERTIMEMETHOD_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.InspectionTimeoutHandleEnum')}]];
    var AUTH_PREFIX="eam_inspection_task";

    // taskStatus
    var TASK_STATUS = [[${taskStatus}]] ;

</script>

<script th:src="'/business/eam/inspection_task/inspection_task_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/inspection_task/inspection_task_list.js?'+${cacheKey}"></script>

</body>
</html>