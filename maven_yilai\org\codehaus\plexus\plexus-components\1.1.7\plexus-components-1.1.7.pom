<project>
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <artifactId>plexus</artifactId>
    <groupId>org.codehaus.plexus</groupId>
    <version>1.0.8</version>
  </parent>
  <groupId>org.codehaus.plexus</groupId>
  <artifactId>plexus-components</artifactId>
  <packaging>pom</packaging>
  <version>1.1.7</version>
  <name>Plexus Components Parent Project</name>
  <!-- 
    TODO: should this be pushed down to all the dependencies?
      - a more stable API JAR may be useful, for the interfaces and classes such as AbstractLogEnabled
  -->
  <dependencies>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-container-default</artifactId>
      <version>1.0-alpha-8</version>
    </dependency>
  </dependencies>
  <modules>
    <module>plexus-action</module>
    <module>plexus-archiver</module>
    <module>plexus-bayesian</module>  
    <module>plexus-command</module>
    <module>plexus-compiler</module>
    <module>plexus-drools</module>        
    <module>plexus-formica</module>
    <module>plexus-formica-web</module>
    <module>plexus-hibernate</module>
    <module>plexus-i18n</module>            
    <module>plexus-interactivity</module>        
    <module>plexus-ircbot</module>    
    <module>plexus-jdo</module>
    <module>plexus-jetty-httpd</module>    
    <module>plexus-jetty</module>
    <module>plexus-mimetyper</module>    
    <module>plexus-mail-sender</module>
    <module>plexus-notification</module>
    <module>plexus-resources</module>
    <module>plexus-taskqueue</module>    
    <module>plexus-velocity</module>
    <module>plexus-xmlrpc</module>
  </modules>
  <scm> 
    <connection>scm:svn:http://svn.codehaus.org/plexus/plexus-components/trunk/</connection>
    <developerConnection>scm:svn:https://svn.codehaus.org/plexus/plexus-components/trunk</developerConnection> 
    <url>http://fisheye.codehaus.org/browse/plexus/plexus-components/trunk/</url>
  </scm>
  <build>                                                                                                                                                                                                         
    <plugins>                                                                                                                                                                                                     
      <plugin>                                                                                                                                                                                                    
        <groupId>org.codehaus.plexus</groupId>                                                                                                                                                                    
        <artifactId>plexus-maven-plugin</artifactId>                                                                                                                                                              
        <version>1.3.2</version>
        <executions>                                                                                                                                                                                              
          <execution>                                                                                                                                                                                             
            <goals>                                                                                                                                                                                               
              <goal>descriptor</goal>                                                                                                                                                                             
            </goals>                                                                                                                                                                                              
          </execution>                                                                                                                                                                                            
        </executions>                                                                                                                                                                                             
      </plugin>                                                                                                                                                                                                   
    </plugins>                                                                                                                                                                                                    
  </build>                     
</project>
