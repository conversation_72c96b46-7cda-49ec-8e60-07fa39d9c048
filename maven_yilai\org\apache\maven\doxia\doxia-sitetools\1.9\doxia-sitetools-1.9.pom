<?xml version="1.0" encoding="UTF-8"?>

<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.maven</groupId>
    <artifactId>maven-parent</artifactId>
    <version>33</version>
    <relativePath>../../pom/maven/pom.xml</relativePath>
  </parent>

  <groupId>org.apache.maven.doxia</groupId>
  <artifactId>doxia-sitetools</artifactId>
  <version>1.9</version>
  <packaging>pom</packaging>

  <name>Doxia Sitetools</name>
  <description>Doxia Sitetools generates sites, consisting of static and dynamic content that was generated by Doxia.</description>
  <url>https://maven.apache.org/doxia/doxia-sitetools/</url>
  <inceptionYear>2005</inceptionYear>

  <modules>
    <module>doxia-decoration-model</module>
    <module>doxia-skin-model</module>
    <module>doxia-integration-tools</module>
    <module>doxia-site-renderer</module>
    <module>doxia-doc-renderer</module>
  </modules>

  <scm>
    <connection>scm:git:https://gitbox.apache.org/repos/asf/maven-doxia-sitetools.git</connection>
    <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/maven-doxia-sitetools.git</developerConnection>
    <url>https://github.com/apache/maven-doxia-sitetools/tree/${project.scm.tag}</url>
    <tag>doxia-sitetools-1.9</tag>
  </scm>
  <issueManagement>
    <system>jira</system>
    <url>https://issues.apache.org/jira/browse/DOXIASITETOOLS</url>
  </issueManagement>
  <ciManagement>
    <system>Jenkins</system>
    <url>https://builds.apache.org/job/doxia-sitetools/</url>
  </ciManagement>
  <distributionManagement>
    <site>
      <id>apache.website</id>
      <url>scm:svn:https://svn.apache.org/repos/asf/maven/doxia/website/components/${maven.site.path}</url>
    </site>
  </distributionManagement>

  <properties>
    <javaVersion>7</javaVersion>
    <doxiaVersion>1.9</doxiaVersion>
    <maven.site.path>doxia-sitetools-archives/doxia-sitetools-LATEST</maven.site.path>
  </properties>

  <dependencyManagement>
    <dependencies>
      <!-- doxia -->
      <dependency>
        <groupId>org.apache.maven.doxia</groupId>
        <artifactId>doxia-logging-api</artifactId>
        <version>${doxiaVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.doxia</groupId>
        <artifactId>doxia-sink-api</artifactId>
        <version>${doxiaVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.doxia</groupId>
        <artifactId>doxia-core</artifactId>
        <version>${doxiaVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.doxia</groupId>
        <artifactId>doxia-module-apt</artifactId>
        <version>${doxiaVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.doxia</groupId>
        <artifactId>doxia-module-confluence</artifactId>
        <version>${doxiaVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.doxia</groupId>
        <artifactId>doxia-module-docbook-simple</artifactId>
        <version>${doxiaVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.doxia</groupId>
        <artifactId>doxia-module-fml</artifactId>
        <version>${doxiaVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.doxia</groupId>
        <artifactId>doxia-module-markdown</artifactId>
        <version>${doxiaVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.doxia</groupId>
        <artifactId>doxia-module-xdoc</artifactId>
        <version>${doxiaVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.doxia</groupId>
        <artifactId>doxia-module-xhtml</artifactId>
        <version>${doxiaVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.doxia</groupId>
        <artifactId>doxia-module-xhtml5</artifactId>
        <version>${doxiaVersion}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.doxia</groupId>
        <artifactId>doxia-decoration-model</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.doxia</groupId>
        <artifactId>doxia-skin-model</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- Commons -->
      <dependency>
        <groupId>commons-io</groupId>
        <artifactId>commons-io</artifactId>
        <version>2.6</version>
      </dependency>
      <!-- Plexus -->
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-container-default</artifactId>
        <version>1.0-alpha-30</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-i18n</artifactId>
        <version>1.0-beta-10</version>
        <exclusions>
          <exclusion>
            <groupId>org.codehaus.plexus</groupId>
            <artifactId>plexus-component-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-velocity</artifactId>
        <version>1.2</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-utils</artifactId>
        <version>3.0.22</version>
      </dependency>
      <!-- misc -->
      <dependency>
        <groupId>org.apache.velocity</groupId>
        <artifactId>velocity</artifactId>
        <version>1.7</version>
      </dependency>
      <!-- Test -->
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>3.8.2</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
      </resource>
      <resource>
        <directory>${project.build.directory}/generated-site/xsd</directory>
        <includes>
          <include>**/*.xsd</include>
        </includes>
      </resource>
    </resources>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <configuration>
            <tagBase>https://svn.apache.org/repos/asf/maven/doxia/doxia-sitetools/tags</tagBase>
            <autoVersionSubmodules>true</autoVersionSubmodules>
          </configuration>
        </plugin>
        <!-- TODO need to upgrade to last version or Maven parent version -->
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>clirr-maven-plugin</artifactId>
          <configuration>
            <comparisonVersion>1.1</comparisonVersion>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <configuration>
            <topSiteURL>scm:svn:https://svn.apache.org/repos/asf/maven/doxia/website/components/${maven.site.path}</topSiteURL>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-scm-publish-plugin</artifactId>
          <configuration>
            <checkoutDirectory>${maven.site.cache}/doxia/${maven.site.path}</checkoutDirectory>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.rat</groupId>
          <artifactId>apache-rat-plugin</artifactId>
          <configuration>
            <excludes combine.children="append">
              <exclude>src/test/resources/site/confluence/confluence/*.confluence</exclude>
              <exclude>src/test/resources/org/apache/maven/doxia/siterenderer/velocity-toolmanager.vm</exclude>
              <exclude>src/test/resources/org/apache/maven/doxia/siterenderer/velocity-toolmanager.expected.txt</exclude>
              <exclude>src/test/resources/dtd/xhtml-*.ent</exclude>
              <exclude>src/test/resources/dtd/xhtml1-transitional.dtd</exclude>
              <exclude>src/test/resources/xhtml-lat1.ent</exclude>
            </excludes>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.plexus</groupId>
          <artifactId>plexus-component-metadata</artifactId>
          <version>1.7.1</version>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-component-metadata</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>generate-metadata</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>clirr-maven-plugin</artifactId>
        <executions>
          <execution>
            <phase>verify</phase>
            <goals>
              <goal>check</goal>
            </goals>
            <configuration>
              <excludes>
                <exclude>org/apache/maven/doxia/site/decoration/*</exclude>
                <exclude>org/apache/maven/doxia/site/decoration/inheritance/*URLContainer</exclude>
                <!-- DOXIASITETOOLS-85 RenderingContext package changed -->
                <exclude>org/apache/maven/doxia/siterenderer/*Renderer</exclude>
                <exclude>org/apache/maven/doxia/siterenderer/sink/*</exclude>
                <!-- DOXIA-511 -->
                <exclude>org/apache/maven/doxia/docrenderer/AbstractDocumentRenderer</exclude>
                <exclude>org/apache/maven/doxia/docrenderer/itext/AbstractITextRender</exclude>
                <!-- DOXIASITETOOLS-170 -->
                <exlcude>org/apache/maven/doxia/siterenderer/SiteRenderingContext</exlcude>
              </excludes>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <id>enforce-bytecode-version</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <enforceBytecodeVersion>
                  <maxJdkVersion>${maven.compiler.target}</maxJdkVersion>
                </enforceBytecodeVersion>
              </rules>
              <fail>true</fail>
            </configuration>
          </execution>
        </executions>
        <dependencies>
          <dependency>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>extra-enforcer-rules</artifactId>
            <version>1.2</version>
            <scope>compile</scope>
          </dependency>
        </dependencies>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>reporting</id>
      <reporting>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-changes-plugin</artifactId>
            <version>2.6</version>
            <inherited>false</inherited>
            <configuration>
              <columnNames>Type,Key,Summary,Resolution,Assignee</columnNames>
              <maxEntries>1000</maxEntries>
              <onlyCurrentVersion>true</onlyCurrentVersion>
              <sortColumnNames>Key</sortColumnNames>
            </configuration>
            <reportSets>
              <reportSet>
                <reports>
                  <report>jira-report</report>
                </reports>
              </reportSet>
            </reportSets>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-jxr-plugin</artifactId>
            <reportSets>
              <reportSet>
                <id>non-aggregate</id>
                <reports>
                  <report>jxr</report>
                </reports>
              </reportSet>
              <reportSet>
                <id>aggregate</id>
                <reports>
                  <report>aggregate</report>
                </reports>
              </reportSet>
            </reportSets>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <configuration>
              <groups>
                <group>
                  <title>Doxia Site Renderer</title>
                  <packages>org.apache.maven.doxia.siterenderer*</packages>
                </group>
                <group>
                  <title>Doxia Decoration Model</title>
                  <packages>org.apache.maven.doxia.site.decoration*</packages>
                </group>
                <group>
                  <title>Doxia Skin Model</title>
                  <packages>org.apache.maven.doxia.site.skin*</packages>
                </group>
                <group>
                  <title>Doxia Integration Tools</title>
                  <packages>org.apache.maven.doxia.tools*</packages>
                </group>
                <group>
                  <title>Doxia Document Renderer</title>
                  <packages>org.apache.maven.doxia.docrenderer*</packages>
                </group>
              </groups>
            </configuration>
            <reportSets>
              <reportSet>
                <id>non-aggregate</id>
                <reports>
                  <report>javadoc</report>
                </reports>
              </reportSet>
              <reportSet>
                <id>aggregate</id>
                <reports>
                  <report>aggregate</report>
                </reports>
              </reportSet>
            </reportSets>
          </plugin>
        </plugins>
      </reporting>
    </profile>
    <profile>
      <id>jigsaw</id>
      <activation>
        <jdk>[1.9,)</jdk>
      </activation>
      <properties>
        <clirr.skip>true</clirr.skip>
      </properties>
    </profile>
  </profiles>
</project>
