<!--
/**
 * 巡检任务 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2024-03-18 08:59:42
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
	<title th:text="${lang.translate('巡检任务')}">巡检任务</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="opacity:0">

        <input name="id" id="id"  type="hidden"/>

         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-4972-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >

                <!-- text_input : 巡检名称 ,  planName -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('巡检名称')}">巡检名称</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="planName" id="planName" name="planName" th:placeholder="${ lang.translate('请输入'+'巡检名称') }" type="text" class="layui-input"    lay-verify="|required"  />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >

                <!-- text_input : 计划单据 ,  planCode -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('计划单据')}">计划单据</div></div>
                    <div class="layui-input-block ">
                        <input  readonly lay-filter="planCode" id="planCode" name="planCode" th:placeholder="${ lang.translate('请输入'+'计划单据') }" type="text" class="layui-input"  />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-7282-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column"  style="padding-top: 0px" >

                <!-- select_box : 巡检班组 ,  groupId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('巡检班组')}">巡检班组</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="groupId" input-type="select" th:data="${'/service-eam/eam-inspection-group/query-paged-list'}" extraParam="{}"></div>
                    </div>
                </div>
            
                <!-- number_input : 提醒时间(时) ,  remindTime  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('提醒时间(时)')}">提醒时间(时)</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="remindTime" id="remindTime" name="remindTime" th:placeholder="${ lang.translate('请输入'+'提醒时间(时)') }" type="text" class="layui-input"    lay-verify="|required"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="2"  value="2.0" />
                    </div>
                </div>
            
                <!-- select_box : 超时处理 ,  overtimeMethod  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('超时处理')}">超时处理</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="overtimeMethod" input-type="select" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.InspectionTimeoutHandleEnum')}" extraParam="{}"></div>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column"  style="padding-top: 0px" >

                <!-- date_input : 实际开始 ,  actStartTime  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('实际开始')}">实际开始</div></div>
                    <div class="layui-input-block ">
                        <input input-type="date" lay-filter="actStartTime" id="actStartTime" name="actStartTime"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择'+'实际开始') }" type="text" class="layui-input"    lay-verify=""   />
                    </div>
                </div>
            
                <!-- date_input : 实际完成 ,  actFinishTime  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('实际完成')}">实际完成</div></div>
                    <div class="layui-input-block ">
                        <input input-type="date" lay-filter="actFinishTime" id="actFinishTime" name="actFinishTime"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择'+'实际完成') }" type="text" class="layui-input"    lay-verify=""   />
                    </div>
                </div>
            
                <!-- number_input : 完成用时(时) ,  actTotalCost  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('完成用时(时)')}">完成用时(时)</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="actTotalCost" id="actTotalCost" name="actTotalCost" th:placeholder="${ lang.translate('请输入'+'完成用时(时)') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="2"  value="2.0" />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-5164-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column"  style="padding-top: 0px" >

                <!-- text_input : 任务反馈 ,  content -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('任务反馈')}">任务反馈</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="content" id="content" name="content" th:placeholder="${ lang.translate('请输入'+'任务反馈') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- text_input : 备注 ,  notes -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('备注')}">备注</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入'+'备注') }" type="text" class="layui-input"  />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->

        <fieldset class="layui-elem-field layui-field-title form-group-title" id="random-8286-fieldset">
            <legend>巡检计划</legend>
        </fieldset>

        <div class="layui-row form-row" id="random-8286-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >

                <!-- number_input : 预计用时(时) ,  planCompletionTime  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('预计用时(时)')}">预计用时(时)</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="planCompletionTime" id="planCompletionTime" name="planCompletionTime" th:placeholder="${ lang.translate('请输入'+'预计用时(时)') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="2"  value="2.0" />
                    </div>
                </div>
            
                <!-- date_input : 应开始时间 ,  planStartTime  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('应开始时间')}">应开始时间</div></div>
                    <div class="layui-input-block ">
                        <input input-type="date" lay-filter="planStartTime" id="planStartTime" name="planStartTime"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择'+'应开始时间') }" type="text" class="layui-input"    lay-verify=""   />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >

                <!-- select_box : 巡检顺序 ,  planInspectionMethod  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('巡检顺序')}">巡检顺序</div></div>
                    <div class="layui-input-block ">
                        <div id="planInspectionMethod" input-type="select" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.InspectionMethodEnum')}" extraParam="{}"></div>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-4322-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column"  style="padding-top: 0px" >

                <!-- text_input : 巡检备注 ,  planNotes -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('巡检备注')}">巡检备注</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="planNotes" id="planNotes" name="planNotes" th:placeholder="${ lang.translate('请输入'+'巡检备注') }" type="text" class="layui-input"  />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->
        <fieldset class="layui-elem-field layui-field-title form-group-title" id="random-4585-fieldset">
        <legend>巡检点</legend>
        </fieldset>
        <div class="layui-row form-row" style="text-align: center;" id="random-4585-content">
            <div style="display: inline-block;padding-right: 8px;padding-left: 8px" class="layui-col-xs12">
            <iframe id="random-4585-iframe" js-fn="pointSelectList" class="form-iframe" frameborder="0" style="width: 100%"></iframe>
            </div>
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 85px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消','','form.button')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('eam_inspection_task:create','eam_inspection_task:update','eam_inspection_task:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存','','form.button')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var SELECT_TASKSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.InspectionTaskStatusEnum')}]];
    var SELECT_PLANINSPECTIONMETHOD_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.InspectionMethodEnum')}]];
    var SELECT_OVERTIMEMETHOD_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.InspectionTimeoutHandleEnum')}]];
    var VALIDATE_CONFIG={"actStartTime":{"date":true,"labelInForm":"实际开始","inputType":"date_input"},"remindTime":{"labelInForm":"提醒时间(时)","inputType":"number_input","required":true},"planStartTime":{"date":true,"labelInForm":"应开始时间","inputType":"date_input"},"overtimeMethod":{"labelInForm":"超时处理","inputType":"select_box","required":true},"groupId":{"labelInForm":"巡检班组","inputType":"select_box","required":true},"planName":{"labelInForm":"巡检名称","inputType":"text_input","required":true},"actFinishTime":{"date":true,"labelInForm":"实际完成","inputType":"date_input"}};
    var AUTH_PREFIX="eam_inspection_task";


</script>



<script th:src="'/business/eam/inspection_task/inspection_task_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/inspection_task/inspection_task_form.js?'+${cacheKey}"></script>

</body>
</html>