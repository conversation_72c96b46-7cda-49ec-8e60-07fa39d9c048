<!--
/**
 * 盘点任务2 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2024-05-06 16:10:13
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
    <title th:text="${lang.translate('盘点任务2')}">盘点任务2</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 类型 , type ,typeName=text_input, isHideInSearch=true -->
                    <!-- 业务状态 , status ,typeName=select_box, isHideInSearch=true -->
                    <!-- 盘点状态 , inventoryStatus ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('盘点状态')}" class="search-label inventoryStatus-label">盘点状态</span><span class="search-colon">:</span></div>
                        <div id="inventoryStatus" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.AssetInventoryActionStatusEnum')}" style="width:180px" extraParam="{}"></div>
                    </div>
                    <!-- 数据状态 , dataStatus ,typeName=select_box, isHideInSearch=true -->
                    <!-- 盘点结束时间 , finishTime ,typeName=date_input, isHideInSearch=true -->
                    <!-- 负责人 , directorId ,typeName=button, isHideInSearch=true -->
                    <!-- 制单人 , originatorId ,typeName=button, isHideInSearch=true -->
                    <!-- 业务日期 , businessDate ,typeName=date_input, isHideInSearch=true -->
                    <!-- 计划编号 , planId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 所属 , ownerCode ,typeName=text_input, isHideInSearch=true -->
                    <!-- 修改人ID , updateBy ,typeName=text_input, isHideInSearch=true -->
                    <!-- 盘点人 , inventoryUserIds ,typeName=button, isHideInSearch=true -->
                    <!-- 资产分类 , categoryIds ,typeName=select_box, isHideInSearch=true -->
                    <!-- 待盘数 , inventoryAssetCountByNotCounted ,typeName=text_input, isHideInSearch=true -->
                    <!-- 已盘数 , inventoryAssetCountByCounted ,typeName=text_input, isHideInSearch=true -->
                    <!-- 盘亏数 , inventoryAssetCountByLoss ,typeName=text_input, isHideInSearch=true -->
                    <!-- 盘赢数 , inventoryAssetCountBySurplus ,typeName=text_input, isHideInSearch=true -->
                    <!-- 异常数 , inventoryAssetCountByException ,typeName=text_input, isHideInSearch=true -->
                    <!-- 所在仓库 , warehouseIds ,typeName=select_box, isHideInSearch=true -->
                    <!-- 业务编码 , businessCode ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('业务编码')}" class="search-label businessCode-label">业务编码</span><span class="search-colon">:</span></div>
                        <input id="businessCode" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 盘点名称 , name ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:130px"><span th:text="${lang.translate('盘点名称')}" class="search-label name-label">盘点名称</span><span class="search-colon">:</span></div>
                        <input id="name" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 备注 , notes ,typeName=text_area, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:130px"><span th:text="${lang.translate('备注')}" class="search-label notes-label">备注</span><span class="search-colon">:</span></div>
                        <input id="notes" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 盘点开始时间 , startTime ,typeName=date_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('盘点开始时间')}" class="search-label startTime-label">盘点开始时间</span><span class="search-colon">:</span></div>
                            <input type="text" id="startTime-begin" style="width: 180px" lay-verify="date" th:placeholder="${lang.translate('开始日期')}" autocomplete="off" class="layui-input search-input search-date-input"  readonly >
                            <span class="search-dash">-</span>
                            <input type="text" id="startTime-end"  style="width: 180px"  lay-verify="date" th:placeholder="${lang.translate('结束日期')}" autocomplete="off" class="layui-input search-input search-date-input" readonly>
                    </div>


                </div>
            </div>


            <!-- 按钮区域 -->
            <div id="search-area" class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索','','cmp:table.search')}">搜索</span></button>
            </div>
        </div>

        <div id="table-area" style="margin-top: 84px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
        <button th:if="${perm.checkAuth('eam_stock_inventory_task:create')}" id="add-button" class="layui-btn icon-btn layui-btn-sm create-new-button " lay-event="create"><i class="layui-icon">&#xe654;</i><span th:text="${lang.translate('新建','','cmp:table.button')}">新建</span></button>
        <button id="inventory-start"  class="layui-btn icon-btn layui-btn-sm " lay-event="tool-inventory-start"><span th:text="${lang.translate('开始盘点','','cmp:table.button')}">开始盘点</span></button>
        <button id="inventory-finish"  class="layui-btn icon-btn layui-btn-sm " lay-event="tool-inventory-finish"><span th:text="${lang.translate('结束盘点','','cmp:table.button')}">结束盘点</span></button>
        <button id="inventory-cancel"  class="layui-btn icon-btn layui-btn-sm " lay-event="tool-inventory-cancel"><span th:text="${lang.translate('取消','','cmp:table.button')}">取消</span></button>
        <button id="inventory-data-sync"  class="layui-btn icon-btn layui-btn-sm " lay-event="tool-inventory-data-sync"><span th:text="${lang.translate('数据同步','','cmp:table.button')}">数据同步</span></button>
    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">

    <button th:if="${perm.checkAuth('eam_stock_inventory_task:view-form')}" class="layui-btn layui-btn-primary layui-btn-xs ops-view-button " lay-event="view"  data-id="{{d.id}}"> <span th:text="${lang.translate('查看','','cmp:table.ops')}">查看</span></button>
    <button th:if="${perm.checkAnyAuth('eam_stock_inventory_task:update','eam_stock_inventory_task:save')}" class="layui-btn layui-btn-primary layui-btn-xs ops-edit-button " lay-event="edit"data-id="{{d.id}}"><span th:text="${lang.translate('修改','','cmp:table.ops')}">修改</span></button>


    <button th:if="${perm.checkAuth('eam_stock_inventory_task:delete')}" class="layui-btn layui-btn-xs layui-btn-danger ops-delete-button " lay-event="del" data-id="{{d.id}}"><span th:text="${lang.translate('删除','','cmp:table.ops')}">删除</span></button>

    <button class="layui-btn layui-btn-xs  inventory-detail-button " lay-event="inventory-detail" data-id="{{d.id}}"><span th:text="${lang.translate('明细','','cmp:table.ops')}">明细</span></button>

</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetHandleStatusEnum')}]];
    var SELECT_INVENTORYSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetInventoryActionStatusEnum')}]];
    var SELECT_DATASTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetInventoryDataStatusEnum')}]];
    var AUTH_PREFIX="eam_stock_inventory_task";

    // OWNER_CODE
    var OWNER_CODE = [[${ownerCode}]] ;

</script>

<script th:src="'/business/eam/stock_inventory_task/stock_inventory_task_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/stock_inventory_task/stock_inventory_task_list.js?'+${cacheKey}"></script>

</body>
</html>