<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 2010, 2018 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Public License v. 2.0, which is available at
    http://www.eclipse.org/legal/epl-2.0.

    This Source Code may also be made available under the following Secondary
    Licenses when the conditions for such availability set forth in the
    Eclipse Public License v. 2.0 are satisfied: GNU General Public License,
    version 2 with the GNU Classpath Exception, which is available at
    https://www.gnu.org/software/classpath/license.html.

    SPDX-License-Identifier: EPL-2.0 OR GPL-2.0 WITH Classpath-exception-2.0

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.glassfish.hk2</groupId>
        <artifactId>hk2-parent</artifactId>
        <version>2.6.1</version>
    </parent>
    <artifactId>hk2-locator</artifactId>
    <name>ServiceLocator Default Implementation</name>
    <description>${project.name}</description>

    <build>
        <plugins>
            <plugin>
                <groupId>org.glassfish.hk2</groupId>
                <artifactId>osgiversion-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <executions>
                    <execution>
                        <id>bundle-manifest</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>manifest</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <!-- Turns on security manager and turns off the soft
                       cache in order to increase code coverage
                    -->
                    <argLine>-Dlocal.repo=${settings.localRepository} -Dbuild.dir=${project.build.directory} -Djava.security.manager -Djava.security.policy=${project.build.directory}/test-classes/policy.txt -Dorg.jvnet.hk2.properties.useSoftReference=false ${surefireArgLineExtra}</argLine>
                    <trimStackTrace>false</trimStackTrace>
                    <!-- -Djava.security.debug=access,failure,domain -->
                </configuration>
            </plugin>

        </plugins>
    </build>
    <dependencies>
        <dependency>
            <groupId>org.glassfish.hk2.external</groupId>
            <artifactId>jakarta.inject</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.glassfish.hk2.external</groupId>
            <artifactId>aopalliance-repackaged</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.glassfish.hk2</groupId>
            <artifactId>hk2-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.glassfish.hk2</groupId>
            <artifactId>hk2-utils</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>jakarta.annotation</groupId>
            <artifactId>jakarta.annotation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.javassist</groupId>
            <artifactId>javassist</artifactId>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>jdk5</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <jdk>1.5</jdk>
            </activation>
            <dependencies>
                <dependency>
                    <groupId>jakarta.xml.stream</groupId>
                    <artifactId>stax-api</artifactId>
                    <scope>provided</scope>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>jdk8-</id>
            <activation>
                <jdk>(,1.8)</jdk>
                <activeByDefault>false</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                          <groupId>org.apache.maven.plugins</groupId>
                          <artifactId>maven-compiler-plugin</artifactId>
                          <executions>
                                <execution>
                                    <id>default-testCompile</id>
                                    <configuration>
                                          <testExcludes>
                                              <testExclude>**/lambda/*</testExclude>
                                          </testExcludes>
                                    </configuration>
                                </execution>
                          </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>jdk8+</id>
            <activation>
                <jdk>[1.8,)</jdk>
                <activeByDefault>false</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                          <groupId>org.apache.maven.plugins</groupId>
                          <artifactId>maven-compiler-plugin</artifactId>
                          <executions>
                                <execution>
                                    <id>default-testCompile</id>
                                    <configuration>
                                        <source>1.8</source>
                                        <target>1.8</target>
                                    </configuration>
                                </execution>
                          </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
