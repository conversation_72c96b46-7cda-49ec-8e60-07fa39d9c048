<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 2011, 2019 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Public License v. 2.0, which is available at
    http://www.eclipse.org/legal/epl-2.0.

    This Source Code may also be made available under the following Secondary
    Licenses when the conditions for such availability set forth in the
    Eclipse Public License v. 2.0 are satisfied: GNU General Public License,
    version 2 with the GNU Classpath Exception, which is available at
    https://www.gnu.org/software/classpath/license.html.

    SPDX-License-Identifier: EPL-2.0 OR GPL-2.0 WITH Classpath-exception-2.0

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.glassfish.jersey</groupId>
        <artifactId>project</artifactId>
        <version>2.30.1</version>
    </parent>

    <groupId>org.glassfish.jersey.core</groupId>
    <artifactId>jersey-client</artifactId>
    <packaging>jar</packaging>
    <name>jersey-core-client</name>

    <description>Jersey core client implementation</description>

    <build>
        <plugins>
            <plugin>
                <groupId>com.sun.istack</groupId>
                <artifactId>istack-commons-maven-plugin</artifactId>
                <inherited>true</inherited>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <inherited>true</inherited>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <inherited>false</inherited>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <compilerArguments>
                        <!-- Do not warn about using sun.misc.Unsafe -->
                        <XDignore.symbol.file />
                    </compilerArguments>
                    <showWarnings>false</showWarnings>
                    <fork>false</fork>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <!-- Execute test classes in parallel - 1 thread per CPU core. -->
                    <parallel>classesAndMethods</parallel>
                    <perCoreThreadCount>true</perCoreThreadCount>
                    <threadCount>1</threadCount>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <executions>
                    <execution>
                        <id>default-jar</id>
                        <phase>package</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <inherited>true</inherited>
                <configuration>
                    <instructions>
                        <Import-Package>
                            ${javax.annotation.osgi.version},
                            *
                        </Import-Package>
                        <unpackBundle>true</unpackBundle>
                    </instructions>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>jakarta.ws.rs</groupId>
            <artifactId>jakarta.ws.rs-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.glassfish.jersey.core</groupId>
            <artifactId>jersey-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>org.glassfish.hk2.external</groupId>
            <artifactId>jakarta.inject</artifactId>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-library</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-all</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.glassfish.jersey.inject</groupId>
            <artifactId>jersey-hk2</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>sonar</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <!-- disable 'reuseForks' because 'AutoDiscoverableClientTest.testAutoDiscoverableClosing' test fails if executed together with JaCoCo agent with other tests in a single JVM -->
                            <reuseForks>false</reuseForks>

                            <!-- disable JaCoCo listener because with switched 'reuseForks' to 'false' (due to
                            https://jira.sonarsource.com/browse/SONARJAVA-728 (https://github.com/SonarSource/sonar-java/pull/324) -->
                            <properties combine.self="override" />

                            <!-- parallel execution doesn't need to be disabled since JaCoCo listener is disabled -->
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>
