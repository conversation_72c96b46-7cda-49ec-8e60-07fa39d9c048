<!--
/**
 * 保养项目 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2022-06-06 21:12:01
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <title th:text="${lang.translate('保养项目')}">保养项目</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

<!--        <div class="search-bar" style="">-->

<!--            <div class="search-input-rows" style="opacity: 0">-->
<!--                &lt;!&ndash; 搜索输入区域 &ndash;&gt;-->
<!--                <div class="layui-form toolbar search-inputs">-->
<!--                    &lt;!&ndash; 主键 , id ,typeName=text_input, isHideInSearch=true &ndash;&gt;-->
<!--                    &lt;!&ndash; 状态 , status ,typeName=select_box, isHideInSearch=false &ndash;&gt;-->
<!--                    <div class="search-unit">-->
<!--                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('状态')}" class="search-label status-label">状态</span><span class="search-colon">:</span></div>-->
<!--                        <div id="status" th:data="${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}" style="width:150px" extraParam="{}"></div>-->
<!--                    </div>-->
<!--                    &lt;!&ndash; 标准工时(小时) , baseCose ,typeName=number_input, isHideInSearch=true &ndash;&gt;-->
<!--                    &lt;!&ndash; 保养周期 , actionCycleId ,typeName=text_input, isHideInSearch=true &ndash;&gt;-->
<!--                    &lt;!&ndash; 保养手册 , attachId ,typeName=upload, isHideInSearch=true &ndash;&gt;-->
<!--                    &lt;!&ndash; 备注 , notes ,typeName=text_area, isHideInSearch=true &ndash;&gt;-->
<!--                    &lt;!&ndash; 选择 , selectedCode ,typeName=text_input, isHideInSearch=true &ndash;&gt;-->
<!--                    &lt;!&ndash; 保养类型 , maintainType ,typeName=select_box, isHideInSearch=false &ndash;&gt;-->
<!--                    <div class="search-unit">-->
<!--                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('保养类型')}" class="search-label maintainType-label">保养类型</span><span class="search-colon">:</span></div>-->
<!--                        <div id="maintainType" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_maintain_type'}" style="width:150px" extraParam="{}"></div>-->
<!--                    </div>-->
<!--                    &lt;!&ndash; 项目编号 , code ,typeName=text_input, isHideInSearch=false &ndash;&gt;-->
<!--                    <div class="search-unit">-->
<!--                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('项目编号')}" class="search-label code-label">项目编号</span><span class="search-colon">:</span></div>-->
<!--                        <input id="code" class="layui-input search-input" style="width: 150px" type="text" />-->
<!--                    </div>-->
<!--                    &lt;!&ndash; 项目名称 , name ,typeName=text_input, isHideInSearch=false &ndash;&gt;-->
<!--                    <div class="search-unit">-->
<!--                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('项目名称')}" class="search-label name-label">项目名称</span><span class="search-colon">:</span></div>-->
<!--                        <input id="name" class="layui-input search-input" style="width: 150px" type="text" />-->
<!--                    </div>-->


<!--                </div>-->
<!--            </div>-->


<!--            &lt;!&ndash; 按钮区域 &ndash;&gt;-->
<!--            <div id="search-area" class="layui-form toolbar search-buttons" style="opacity: 0">-->
<!--                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索')}">搜索</span></button>-->
<!--            </div>-->
<!--        </div>-->

        <div id="table-area" style="margin-top: 8px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>

<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
        <button id="add-button" class="layui-btn icon-btn layui-btn-sm create-new-button " lay-event="create"><i class="layui-icon">&#xe654;</i><span th:text="${lang.translate('选择')}">选择</span></button>
        <button id="delete-button" class="layui-btn icon-btn layui-btn-danger layui-btn-sm batch-delete-button " lay-event="batch-del"><i class="layui-icon">&#xe67e;</i><span th:text="${lang.translate('删除')}">删除</span></button>
    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">

    <button  class="layui-btn layui-btn-primary layui-btn-xs ops-view-button " lay-event="view"  data-id="{{d.id}}"> <span th:text="${lang.translate('查看')}">查看</span></button>

</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var AUTH_PREFIX="eam_maintain_project_selected";

    var SELECTED_CODE = [[${selectedCode}]] ;
    var OWNER_ID = [[${ownerId}]] ;
    var OWNER_TYPE = [[${ownerType}]] ;
    var PAGE_TYPE = [[${pageType}]] ;

</script>

<script th:src="'/business/eam/maintain_project/maintain_project_selected_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/maintain_project/maintain_project_selected_list.js?'+${cacheKey}"></script>

</body>
</html>
