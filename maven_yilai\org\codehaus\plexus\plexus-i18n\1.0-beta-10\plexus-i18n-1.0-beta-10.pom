<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>plexus-components</artifactId>
    <groupId>org.codehaus.plexus</groupId>
    <version>1.1.12</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>plexus-i18n</artifactId>
  <name>Plexus I18N Component</name>
  <version>1.0-beta-10</version>
  
  <properties>
  	<distMgmtSnapshotsName>Plexus Central Development Repository</distMgmtSnapshotsName>
  	<distMgmtSnapshotsUrl>dav:https://dav.codehaus.org/snapshots.repository/plexus</distMgmtSnapshotsUrl>
  </properties>
  
  <distributionManagement>
	<snapshotRepository>
		<id>codehaus.org</id>
		<name>${distMgmtSnapshotsName}</name>
		<url>${distMgmtSnapshotsUrl}</url>
	</snapshotRepository>
  </distributionManagement>
  
  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-maven-plugin</artifactId>
        <version>1.3.5</version>
        <executions>
          <execution>
            <goals>
              <goal>descriptor</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>1.5</source>
          <target>1.5</target>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <dependencies>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-utils</artifactId>
      <version>1.4.5</version>
    </dependency>
  </dependencies>

  <scm>
    <connection>scm:svn:http://svn.codehaus.org/plexus/plexus-components/tags/plexus-i18n-1.0-beta-10</connection>
    <developerConnection>scm:svn:https://svn.codehaus.org/plexus/plexus-components/tags/plexus-i18n-1.0-beta-10</developerConnection>
    <url>http://fisheye.codehaus.org/browse/plexus/plexus-components/tags/plexus-i18n-1.0-beta-10</url>
  </scm>
</project>
