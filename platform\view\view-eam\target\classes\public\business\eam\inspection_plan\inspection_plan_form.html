<!--
/**
 * 巡检计划 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2023-08-04 08:45:13
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
	<title th:text="${lang.translate('巡检计划')}">巡检计划</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="opacity:0">

        <input name="id" id="id"  type="hidden"/>

         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-0624-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                <!-- text_input : 计划名称 ,  name -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('计划名称')}">计划名称</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="name" id="name" name="name" th:placeholder="${ lang.translate('请输入'+'计划名称') }" type="text" class="layui-input"    lay-verify="|required"  />
                    </div>
                </div>
            
                <!-- select_box : 计划状态 ,  planStatus  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('计划状态')}">计划状态</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="planStatus" input-type="select" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.EamPlanStatusEnum')}" extraParam="{}"></div>
                    </div>
                </div>
            
                <!-- select_box : 计划类型 ,  planType  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('计划类型')}">计划类型</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="planType" input-type="select" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.InspectionPlanTypeEnum')}" extraParam="{}"></div>
                    </div>
                </div>
            
                <!-- select_box : 巡检班组 ,  groupId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('巡检班组')}">巡检班组</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="groupId" input-type="select" th:data="${'/service-eam/eam-inspection-group/query-paged-list'}" extraParam="{}"></div>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                <!-- date_input : 开始日期 ,  startDate  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('开始日期')}">开始日期</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input input-type="date" lay-filter="startDate" id="startDate" name="startDate"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择'+'开始日期') }" type="text" class="layui-input"    lay-verify="|required"   />
                    </div>
                </div>
            
                <!-- date_input : 截止日期 ,  endDate  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('截止日期')}">截止日期</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input input-type="date" lay-filter="endDate" id="endDate" name="endDate"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择'+'截止日期') }" type="text" class="layui-input"    lay-verify="|required"   />
                    </div>
                </div>
            
                <!-- select_box : 巡检顺序 ,  inspectionMethod  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('巡检顺序')}">巡检顺序</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="inspectionMethod" input-type="select" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.InspectionMethodEnum')}" extraParam="{}"></div>
                    </div>
                </div>
            
                <!-- text_input : 位置范围 ,  posDetail -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('位置范围')}">位置范围</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="posDetail" id="posDetail" name="posDetail" th:placeholder="${ lang.translate('请输入'+'位置范围') }" type="text" class="layui-input"  />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                <!-- text_input : 计划周期 ,  actionCycleId -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('计划周期')}">计划周期</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="actionCycleId" id="actionCycleId" name="actionCycleId" th:placeholder="${ lang.translate('请输入'+'计划周期') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- number_input : 提醒时间(时) ,  remindTime  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('提醒时间(时)')}">提醒时间(时)</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="remindTime" id="remindTime" name="remindTime" th:placeholder="${ lang.translate('请输入'+'提醒时间(时)') }" type="text" class="layui-input"    lay-verify="|required"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="2"  value="2.0" />
                    </div>
                </div>
            
                <!-- number_input : 预计用时(时) ,  completionTime  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('预计用时(时)')}">预计用时(时)</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="completionTime" id="completionTime" name="completionTime" th:placeholder="${ lang.translate('请输入'+'预计用时(时)') }" type="text" class="layui-input"    lay-verify="|required"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="2"  value="2.0" />
                    </div>
                </div>
            
                <!-- select_box : 超时处理 ,  overtimeMethod  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('超时处理')}">超时处理</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="overtimeMethod" input-type="select" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.InspectionTimeoutHandleEnum')}" extraParam="{}"></div>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-3800-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column"  style="padding-top: 0px" >

                <!-- text_area : 备注 ,  notes  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('备注')}">备注</div></div>
                    <div class="layui-input-block ">
                        <textarea lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入'+'备注') }" class="layui-textarea" style="height: 120px" ></textarea>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->
        <fieldset class="layui-elem-field layui-field-title form-group-title" id="random-6606-fieldset">
        <legend>巡检点</legend>
        </fieldset>
        <div class="layui-row form-row" style="text-align: center;" id="random-6606-content">
            <div style="display: inline-block;padding-right: 8px;padding-left: 8px" class="layui-col-xs12">
            <iframe id="random-6606-iframe" js-fn="pointSelectList" class="form-iframe" frameborder="0" style="width: 100%"></iframe>
            </div>
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 20px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消','','form.button')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('eam_inspection_plan:create','eam_inspection_plan:update','eam_inspection_plan:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存','','form.button')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetHandleStatusEnum')}]];
    var SELECT_PLANSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.EamPlanStatusEnum')}]];
    var SELECT_PLANTYPE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.InspectionPlanTypeEnum')}]];
    var SELECT_INSPECTIONMETHOD_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.InspectionMethodEnum')}]];
    var SELECT_OVERTIMEMETHOD_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.InspectionTimeoutHandleEnum')}]];
    var VALIDATE_CONFIG={"completionTime":{"labelInForm":"预计用时(时)","inputType":"number_input","required":true},"remindTime":{"labelInForm":"提醒时间(时)","inputType":"number_input","required":true},"planType":{"labelInForm":"计划类型","inputType":"select_box","required":true},"overtimeMethod":{"labelInForm":"超时处理","inputType":"select_box","required":true},"endDate":{"date":true,"labelInForm":"截止日期","inputType":"date_input","required":true},"groupId":{"labelInForm":"巡检班组","inputType":"select_box","required":true},"name":{"labelInForm":"计划名称","inputType":"text_input","required":true},"planStatus":{"labelInForm":"计划状态","inputType":"select_box","required":true},"startDate":{"date":true,"labelInForm":"开始日期","inputType":"date_input","required":true},"inspectionMethod":{"labelInForm":"巡检顺序","inputType":"select_box","required":true}};
    var AUTH_PREFIX="eam_inspection_plan";


</script>



<script th:src="'/business/eam/inspection_plan/inspection_plan_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/inspection_plan/inspection_plan_form.js?'+${cacheKey}"></script>

</body>
</html>