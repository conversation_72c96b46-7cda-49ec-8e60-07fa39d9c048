<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.camunda.bpm</groupId>
    <artifactId>camunda-database-settings</artifactId>
    <version>7.17.0</version>
    <relativePath>../database</relativePath>
  </parent>

  <artifactId>camunda-webapp</artifactId>
  <groupId>org.camunda.bpm.webapp</groupId>
  <packaging>war</packaging>
  <name>Camunda Platform - webapp - app</name>
  <inceptionYear>2014</inceptionYear>

  <properties>
    <version.jetty>9.4.31.v20200723</version.jetty>

    <web.resources.override>src/main/runtime/default/webapp</web.resources.override>
    <java.resources.override>src/main/runtime/default/resources</java.resources.override>
    <properties.override>src/main/runtime/default/config.properties</properties.override>

    <history.level>full</history.level>

    <frontend.app.path>${project.build.directory}/webapp/</frontend.app.path>

    <frontend.development.cockpit.path>${frontend.app.path}</frontend.development.cockpit.path>
    <frontend.development.tasklist.path>${frontend.app.path}</frontend.development.tasklist.path>
    <frontend.react.path>ui/cockpit</frontend.react.path>

    <exec.shell>bash</exec.shell>
    <exec.arg>-c</exec.arg>

    <skip.frontend.build>false</skip.frontend.build>

    <authorizationCheckRevokes>auto</authorizationCheckRevokes>

    <jdbcBatchProcessing>true</jdbcBatchProcessing>

    <license.includeTransitiveDependencies>false</license.includeTransitiveDependencies>

    <!-- generate a bom of compile time dependencies for the license book.
    Note: Every compile time dependency will end up in the license book. Please
    declare only dependencies that are actually needed -->
    <skip-third-party-bom>false</skip-third-party-bom>

    <!-- frontend-sources artifact is for internal use only.
    Artifact generation is skipped when it comes to maven central deployment -->
    <skip-zip-frontend-sources>false</skip-zip-frontend-sources>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.camunda.bpm</groupId>
        <artifactId>camunda-core-internal-dependencies</artifactId>
        <version>${project.version}</version>
        <scope>import</scope>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>org.camunda.bpm</groupId>
        <artifactId>camunda-engine-rest-core</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.camunda.bpm</groupId>
        <artifactId>camunda-engine-rest</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>javax.activation</groupId>
        <artifactId>activation</artifactId>
        <version>1.1</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <repositories>
    <repository>
      <id>camunda-nexus</id>
      <name>Camunda Platform Maven Repository</name>
      <url>https://artifacts.camunda.com/artifactory/public/</url>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
    </repository>
  </repositories>

  <dependencies>

    <!-- jee spec -->
    <dependency>
      <groupId>org.jboss.spec</groupId>
      <artifactId>jboss-javaee-6.0</artifactId>
      <type>pom</type>
      <scope>provided</scope>
      <exclusions>
        <exclusion>
          <groupId>org.jboss.spec.javax.ws.rs</groupId>
          <artifactId>jboss-jaxrs-api_1.1_spec</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.jboss.spec.javax.ws.rs</groupId>
      <artifactId>jboss-jaxrs-api_2.1_spec</artifactId>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>org.camunda.bpm</groupId>
      <artifactId>camunda-engine</artifactId>
      <scope>provided</scope>
    </dependency>

    <!-- rest api -->
    <dependency>
      <groupId>org.camunda.bpm</groupId>
      <artifactId>camunda-engine-rest-core</artifactId>
    </dependency>

    <!-- tests -->
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>com.fasterxml.uuid</groupId>
      <artifactId>java-uuid-generator</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.h2database</groupId>
      <artifactId>h2</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-inline</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-test</artifactId>
      <version>${version.spring.framework}</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-web</artifactId>
      <version>${version.spring.framework}</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.camunda.bpm</groupId>
      <artifactId>camunda-engine-rest-core</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
      <classifier>tests</classifier>
    </dependency>

    <dependency>
      <groupId>org.eclipse.jetty</groupId>
      <artifactId>jetty-webapp</artifactId>
      <version>${version.jetty}</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.glassfish.jersey.core</groupId>
      <artifactId>jersey-common</artifactId>
      <version>${version.jersey2}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <finalName>camunda-webapp</finalName>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
      </resource>
      <resource>
        <directory>${java.resources.override}</directory>
        <filtering>true</filtering>
      </resource>
    </resources>

    <testResources>
      <testResource>
        <directory>src/test/resources</directory>
        <filtering>true</filtering>
      </testResource>
    </testResources>

    <pluginManagement>
      <plugins>
        <!-- test plugins -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <configuration>
            <systemPropertyVariables>
              <myWorkingDir>${project.build.directory}</myWorkingDir>
            </systemPropertyVariables>
            <argLine>-XX:MaxMetaspaceSize=128m</argLine>
          </configuration>
        </plugin>

        <!-- code plugins -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>2.8</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-resources-plugin</artifactId>
          <version>2.6</version>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-antrun-plugin</artifactId>
          <version>1.8</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>build-helper-maven-plugin</artifactId>
          <version>1.9.1</version>
        </plugin>

      </plugins>
    </pluginManagement>

    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-war-plugin</artifactId>
        <configuration>
          <webResources>
            <resource>
              <filtering>true</filtering>
              <directory>${web.resources.override}</directory>
            </resource>
            <resource>
              <filtering>false</filtering>
              <directory>target/webapp</directory>
              <excludes>
                <exclude>**/*.html</exclude>
              </excludes>
            </resource>
            <resource>
              <filtering>true</filtering>
              <directory>target/webapp</directory>
              <!-- no filtering for favicon because linux destroys images through carriage return -->
              <includes>
                <include>**/*.html</include>
              </includes>
            </resource>
          </webResources>
          <!-- exclude development resources (task forms etc.) -->
          <packagingExcludes>
            develop/**
          </packagingExcludes>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <failIfNoTests>false</failIfNoTests>
          <trimStackTrace>false</trimStackTrace>
          <redirectTestOutputToFile>true</redirectTestOutputToFile>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <id>create-jar</id>
            <goals>
              <goal>jar</goal>
            </goals>
            <phase>package</phase>
            <configuration>
              <classifier>classes</classifier>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>license-maven-plugin</artifactId>
        <version>1.14</version>
        <configuration>
          <excludedScopes>test</excludedScopes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-assembly-plugin</artifactId>
        <executions>
          <execution>
            <id>zip-frontend-sources</id>
            <phase>generate-resources</phase>
            <goals>
              <goal>single</goal>
            </goals>
            <configuration>
              <skipAssembly>${skip-zip-frontend-sources}</skipAssembly>
              <descriptors>
                <descriptor>assembly.xml</descriptor>
              </descriptors>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>sonatype-oss-release</id>
      <properties>
        <!-- skip building the frontend-sources zip artifact
        when releasing the community edition (alpha/minor releases) -->
        <skip-zip-frontend-sources>true</skip-zip-frontend-sources>
      </properties>
    </profile>

    <profile>
      <id>os</id>
      <activation>
        <os>
          <family>Windows</family>
        </os>
      </activation>
      <properties>
        <exec.shell>cmd</exec.shell>
        <exec.arg>/c</exec.arg>
      </properties>
    </profile>

    <profile>
      <id>develop</id>
      <dependencies>
        <dependency>
          <groupId>org.jboss.resteasy</groupId>
          <artifactId>resteasy-jaxrs</artifactId>
          <scope>runtime</scope>
        </dependency>
        <dependency>
          <groupId>org.camunda.bpm</groupId>
          <artifactId>camunda-engine</artifactId>
        </dependency>
        <dependency>
          <groupId>org.camunda.bpm</groupId>
          <artifactId>camunda-engine-plugin-spin</artifactId>
        </dependency>
        <dependency>
          <groupId>org.camunda.spin</groupId>
          <artifactId>camunda-spin-dataformat-json-jackson</artifactId>
        </dependency>
        <dependency>
          <groupId>org.camunda.spin</groupId>
          <artifactId>camunda-spin-dataformat-xml-dom</artifactId>
        </dependency>
        <dependency>
          <groupId>org.camunda.bpm</groupId>
          <artifactId>camunda-engine-plugin-connect</artifactId>
        </dependency>
        <dependency>
          <groupId>org.camunda.connect</groupId>
          <artifactId>camunda-connect-http-client</artifactId>
        </dependency>
        <dependency>
          <groupId>org.camunda.connect</groupId>
          <artifactId>camunda-connect-soap-http-client</artifactId>
        </dependency>
        <dependency>
          <groupId>org.camunda.bpm.identity</groupId>
          <artifactId>camunda-identity-ldap</artifactId>
          <version>${project.version}</version>
        </dependency>
        <dependency>
          <groupId>com.h2database</groupId>
          <artifactId>h2</artifactId>
        </dependency>
        <dependency>
          <groupId>com.fasterxml.uuid</groupId>
          <artifactId>java-uuid-generator</artifactId>
        </dependency>

        <dependency>
          <groupId>org.apache.tomcat</groupId>
          <artifactId>tomcat-jdbc</artifactId>
          <version>${version.tomcat}</version>
          <exclusions>
            <exclusion>
              <groupId>org.apache.tomcat</groupId>
              <artifactId>tomcat-juli</artifactId>
            </exclusion>
          </exclusions>
        </dependency>

        <dependency>
          <groupId>ch.qos.logback</groupId>
          <artifactId>logback-classic</artifactId>
        </dependency>

      </dependencies>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-antrun-plugin</artifactId>
            <executions>
              <execution>
                <phase>generate-resources</phase>
                <configuration>
                  <target>
                    <mkdir dir="${project.basedir}/target/webapp" />
                  </target>
                </configuration>
                <goals>
                  <goal>run</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-maven-plugin</artifactId>
            <version>${version.jetty}</version>
            <configuration>
              <webAppConfig>
                <contextPath>/camunda</contextPath>
                <defaultsDescriptor>${project.basedir}/src/main/runtime/develop/jetty/webdefault.xml</defaultsDescriptor>
                <resourceBases>
                  <resourceBase>${project.basedir}/target/webapp</resourceBase>
                  <resourceBase>${project.basedir}/src/main/webapp</resourceBase>
                </resourceBases>
              </webAppConfig>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>build-helper-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>add-sources</id>
                <goals>
                  <goal>add-source</goal>
                </goals>
                <configuration>
                  <sources>
                    <source>src/main/runtime/develop/java</source>
                  </sources>
                </configuration>
              </execution>
              <execution>
                <id>add-resources</id>
                <goals>
                  <goal>add-resource</goal>
                </goals>
                <configuration>
                  <resources>
                    <resource>
                      <directory>src/main/runtime/develop/resources</directory>
                    </resource>
                  </resources>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
      <properties>
        <java.resources.override>src/main/runtime/develop/resources</java.resources.override>
        <skip.frontend.build>true</skip.frontend.build>
      </properties>
    </profile>

    <profile>
      <id>mariadb</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <excludedGroups>org.camunda.bpm.cockpit.plugin.base.SlowMariaDbTest</excludedGroups>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>h2</id>
      <properties>
        <project.parent.basedir>./</project.parent.basedir>
      </properties>
    </profile>

    <profile>
      <id>dev-e2e</id>
      <dependencies>
        <dependency>
          <groupId>org.jboss.resteasy</groupId>
          <artifactId>resteasy-jaxrs</artifactId>
          <scope>runtime</scope>
        </dependency>
        <dependency>
          <groupId>org.apache.tomcat</groupId>
          <artifactId>tomcat-jdbc</artifactId>
          <version>${version.tomcat}</version>
          <exclusions>
            <exclusion>
              <groupId>org.apache.tomcat</groupId>
              <artifactId>tomcat-juli</artifactId>
            </exclusion>
          </exclusions>
        </dependency>
      </dependencies>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-antrun-plugin</artifactId>
            <executions>
              <execution>
                <phase>generate-resources</phase>
                <configuration>
                  <target>
                    <mkdir dir="${project.basedir}/target/webapp" />
                  </target>
                </configuration>
                <goals>
                  <goal>run</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-maven-plugin</artifactId>
            <version>${version.jetty}</version>
            <configuration>
              <webAppConfig>
                <contextPath>/camunda</contextPath>
                <defaultsDescriptor>${project.basedir}/src/main/runtime/test/jetty/webdefault.xml</defaultsDescriptor>
                <resourceBases>
                  <resourceBase>${project.basedir}/target/webapp</resourceBase>
                  <resourceBase>${project.basedir}/src/main/webapp</resourceBase>
                </resourceBases>
              </webAppConfig>
              <contextHandlers>
                <contextHandler implementation="org.eclipse.jetty.maven.plugin.JettyWebAppContext">
                  <war>${basedir}/target/engine-rest.war</war>
                  <contextPath>/engine-rest</contextPath>
                </contextHandler>
              </contextHandlers>
            </configuration>
            <dependencies>
              <dependency>
                <groupId>org.camunda.bpm</groupId>
                <artifactId>camunda-engine</artifactId>
                <version>${project.version}</version>
              </dependency>
              <dependency>
                <groupId>org.camunda.bpm.identity</groupId>
                <artifactId>camunda-identity-ldap</artifactId>
                <version>${project.version}</version>
              </dependency>
              <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-jdk14</artifactId>
                <version>1.7.26</version>
              </dependency>
              <dependency>
                <groupId>com.h2database</groupId>
                <artifactId>h2</artifactId>
                <version>1.4.181</version>
              </dependency>
              <dependency>
                <groupId>com.fasterxml.uuid</groupId>
                <artifactId>java-uuid-generator</artifactId>
                <version>3.1.2</version>
              </dependency>
            </dependencies>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>build-helper-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>add-sources</id>
                <goals>
                  <goal>add-source</goal>
                </goals>
                <configuration>
                  <sources>
                    <source>src/main/runtime/test/java</source>
                  </sources>
                </configuration>
              </execution>
              <execution>
                <id>add-resources</id>
                <goals>
                  <goal>add-resource</goal>
                </goals>
                <configuration>
                  <resources>
                    <resource>
                      <directory>src/main/runtime/test/resources</directory>
                    </resource>
                  </resources>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-dependency-plugin</artifactId>
            <version>2.10</version>
            <executions>
              <execution>
                <id>copy</id>
                <phase>generate-resources</phase>
                <goals>
                  <goal>copy</goal>
                </goals>
                <configuration>
                  <artifactItems>
                    <artifactItem>
                      <groupId>org.camunda.bpm</groupId>
                      <artifactId>camunda-engine-rest</artifactId>
                      <version>${project.version}</version>
                      <type>war</type>
                      <classifier>tomcat</classifier>
                      <overWrite>true</overWrite>
                      <outputDirectory>${project.build.directory}/</outputDirectory>
                      <destFileName>engine-rest.war</destFileName>
                    </artifactItem>
                  </artifactItems>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
      <properties>
        <java.resources.override>src/main/runtime/test/resources</java.resources.override>
        <skip.frontend.build>true</skip.frontend.build>
      </properties>
    </profile>

    <profile>
      <id>test-e2e</id>
      <dependencies>
        <dependency>
          <groupId>org.jboss.resteasy</groupId>
          <artifactId>resteasy-jaxrs</artifactId>
          <scope>runtime</scope>
        </dependency>
        <dependency>
          <groupId>org.apache.tomcat</groupId>
          <artifactId>tomcat-jdbc</artifactId>
          <version>${version.tomcat}</version>
          <exclusions>
            <exclusion>
              <groupId>org.apache.tomcat</groupId>
              <artifactId>tomcat-juli</artifactId>
            </exclusion>
          </exclusions>
        </dependency>
        <dependency>
          <groupId>org.camunda.bpm.identity</groupId>
          <artifactId>camunda-identity-ldap</artifactId>
          <version>${project.version}</version>
          <scope>test</scope>
        </dependency>
      </dependencies>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <skipTests>true</skipTests>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-maven-plugin</artifactId>
            <version>${version.jetty}</version>
            <configuration>
              <webAppConfig>
                <contextPath>/camunda</contextPath>
                <defaultsDescriptor>${project.basedir}/src/main/runtime/test/jetty/webdefault.xml</defaultsDescriptor>
              </webAppConfig>
              <contextHandlers>
                <contextHandler implementation="org.eclipse.jetty.maven.plugin.JettyWebAppContext">
                  <war>${basedir}/target/engine-rest.war</war>
                  <contextPath>/engine-rest</contextPath>
                </contextHandler>
              </contextHandlers>
              <stopKey>STOP</stopKey>
              <stopPort>9999</stopPort>
              <stopWait>10</stopWait>
            </configuration>
            <executions>
              <execution>
                <id>start-jetty</id>
                <phase>pre-integration-test</phase>
                <goals>
                  <goal>deploy-war</goal>
                </goals>
                <configuration>
                  <scanIntervalSeconds>0</scanIntervalSeconds>
                  <daemon>true</daemon>
                </configuration>
              </execution>
              <execution>
                <id>stop-jetty</id>
                <phase>post-integration-test</phase>
                <goals>
                  <goal>stop</goal>
                </goals>
              </execution>
            </executions>
            <dependencies>
              <dependency>
                <groupId>org.camunda.bpm</groupId>
                <artifactId>camunda-engine</artifactId>
                <version>${project.version}</version>
              </dependency>
              <dependency>
                <groupId>org.camunda.bpm.identity</groupId>
                <artifactId>camunda-identity-ldap</artifactId>
                <version>${project.version}</version>
              </dependency>
              <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-jdk14</artifactId>
                <version>1.7.7</version>
              </dependency>
              <dependency>
                <groupId>com.h2database</groupId>
                <artifactId>h2</artifactId>
                <version>1.4.181</version>
              </dependency>
              <dependency>
                <groupId>com.fasterxml.uuid</groupId>
                <artifactId>java-uuid-generator</artifactId>
                <version>3.1.2</version>
              </dependency>
            </dependencies>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-antrun-plugin</artifactId>
            <executions>
              <execution>
                <id>grunt-test</id>
                <phase>integration-test</phase>
                <goals>
                  <goal>run</goal>
                </goals>
                <inherited>false</inherited>
                <configuration>
                  <target>
                    <!-- grunt test-e2e -->
                    <exec executable="./node_modules/grunt-cli/bin/grunt" failonerror="true" osfamily="unix">
                      <env key="XUNIT_FILE" value="target/xunit.xml" />
                      <env key="LOG_XUNIT" value="true" />
                      <arg value="test-e2e" />
                      <arg value="--color=false" />
                    </exec>
                    <exec executable="cmd" failonerror="true" osfamily="windows">
                      <env key="XUNIT_FILE" value="target/xunit.xml" />
                      <env key="LOG_XUNIT" value="true" />
                      <arg value="/c" />
                      <arg value="node_modules\.bin\grunt.cmd" />
                      <arg value="test-e2e" />
                      <arg value="--color=false" />
                    </exec>
                  </target>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>build-helper-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>add-sources</id>
                <goals>
                  <goal>add-source</goal>
                </goals>
                <configuration>
                  <sources>
                    <source>src/main/runtime/test/java</source>
                  </sources>
                </configuration>
              </execution>
              <execution>
                <id>add-resources</id>
                <goals>
                  <goal>add-resource</goal>
                </goals>
                <configuration>
                  <resources>
                    <resource>
                      <directory>src/main/runtime/test/resources</directory>
                    </resource>
                  </resources>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-dependency-plugin</artifactId>
            <version>2.10</version>
            <executions>
              <execution>
                <id>copy</id>
                <phase>generate-resources</phase>
                <goals>
                  <goal>copy</goal>
                </goals>
                <configuration>
                  <artifactItems>
                    <artifactItem>
                      <groupId>org.camunda.bpm</groupId>
                      <artifactId>camunda-engine-rest</artifactId>
                      <version>${project.version}</version>
                      <type>war</type>
                      <classifier>tomcat</classifier>
                      <overWrite>true</overWrite>
                      <outputDirectory>${project.build.directory}/</outputDirectory>
                      <destFileName>engine-rest.war</destFileName>
                    </artifactItem>
                  </artifactItems>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
      <properties>
        <java.resources.override>src/main/runtime/test/resources</java.resources.override>
      </properties>
    </profile>

    <!-- database QA profiles -->
    <profile>
      <id>database</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>exec-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>database-test-drop-schema-before-test</id>
                <phase>process-test-classes</phase>
                <goals>
                  <goal>java</goal>
                </goals>
              </execution>
              <execution>
                <id>database-test-drop-schema-after-test</id>
                <phase>prepare-package</phase>
                <goals>
                  <goal>java</goal>
                </goals>
              </execution>
            </executions>
            <configuration>
              <mainClass>org.camunda.bpm.engine.impl.db.DbSchemaDrop</mainClass>
              <classpathScope>test</classpathScope>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>

    <!-- profile for running cockpit testsuite with database table prefix -->
    <profile>
      <id>db-table-prefix</id>
      <!-- imports the DbSchemaPrefixTestHelper class -->
      <dependencies>
        <dependency>
          <groupId>org.camunda.bpm</groupId>
          <artifactId>camunda-engine</artifactId>
          <version>${project.version}</version>
          <scope>test</scope>
          <classifier>tests</classifier>
        </dependency>
      </dependencies>

      <build>
        <plugins>
          <plugin>
            <artifactId>maven-antrun-plugin</artifactId>
            <executions>
              <execution>
                <id>database-test-create-schema</id>
                <phase>process-test-classes</phase>
                <goals>
                  <goal>run</goal>
                </goals>
                <configuration>
                  <target>
                    <copy file="target/test-classes/camunda.cfg.prefix.xml" tofile="target/test-classes/camunda.cfg.xml" overwrite="true" />
                  </target>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>node-build-windows</id>
      <activation>
        <os>
          <family>windows</family>
        </os>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-antrun-plugin</artifactId>
            <executions>
              <execution>
                <id>generate-zip</id>
                <phase>generate-resources</phase>
                <goals>
                  <goal>run</goal>
                </goals>
                <inherited>false</inherited>
                <configuration>
                  <skip>${skip.frontend.build}</skip>
                  <target>
                    <!-- run node build -->
                    <exec executable="cmd" failonerror="true">
                      <arg value="/c" />
                      <arg value="npm" />
                      <arg value="install" />
                    </exec>
                    <exec executable="cmd" failonerror="true">
                      <arg value="/c" />
                      <arg value="node_modules\.bin\grunt.cmd" />
                      <arg value="build" />
                      <arg value="--color=false" />
                    </exec>
                  </target>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>node-build-unix</id>
      <activation>
        <os>
          <family>!windows</family>
        </os>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-antrun-plugin</artifactId>
            <executions>
              <execution>
                <id>generate-zip</id>
                <phase>generate-resources</phase>
                <goals>
                  <goal>run</goal>
                </goals>
                <inherited>false</inherited>
                <configuration>
                  <skip>${skip.frontend.build}</skip>
                  <target>
                    <!-- run node build -->
                    <exec executable="npm" failonerror="true">
                      <arg value="install" />
                    </exec>
                    <exec executable="./node_modules/grunt-cli/bin/grunt" failonerror="true">
                      <arg value="build" />
                      <arg value="--color=false" />
                    </exec>
                  </target>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>doFrontendBuild</id>
      <properties>
        <skip.frontend.build>false</skip.frontend.build>
      </properties>
    </profile>

    <profile>
      <id>skipFrontendBuild</id>
      <properties>
        <skip.frontend.build>true</skip.frontend.build>
      </properties>
    </profile>

    <profile>
      <id>cfgAuthorizationCheckRevokesAlways</id>
      <properties>
        <authorizationCheckRevokes>always</authorizationCheckRevokes>
      </properties>
    </profile>

    <!--  check history audit -->
    <profile>
      <id>cfghistoryaudit</id>
      <properties>
        <history.level>audit</history.level>
      </properties>
    </profile>

    <!--  check history activity -->
    <profile>
      <id>cfghistoryactivity</id>
      <properties>
        <history.level>activity</history.level>
      </properties>
    </profile>

    <!--  check history none -->
    <profile>
      <id>cfghistorynone</id>
      <properties>
        <history.level>none</history.level>
      </properties>
    </profile>

    <profile>
      <id>cfgJdbcBatchProcessingOff</id>
      <properties>
        <jdbcBatchProcessing>false</jdbcBatchProcessing>
      </properties>
    </profile>

    <profile>
      <id>license-header-check</id>
      <build>
        <plugins>
          <plugin>
            <groupId>com.mycila</groupId>
            <artifactId>license-maven-plugin</artifactId>
          </plugin>
        </plugins>
      </build>
    </profile>

  </profiles>

</project>
