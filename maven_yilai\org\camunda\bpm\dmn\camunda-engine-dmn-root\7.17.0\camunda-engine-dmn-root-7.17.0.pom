<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.camunda.bpm</groupId>
    <artifactId>camunda-database-settings</artifactId>
    <relativePath>../database</relativePath>
    <version>7.17.0</version>
  </parent>

  <groupId>org.camunda.bpm.dmn</groupId>
  <artifactId>camunda-engine-dmn-root</artifactId>
  <name>camunda DMN - engine - root</name>
  <inceptionYear>2015</inceptionYear>
  <packaging>pom</packaging>

  <properties>
    <version.juel>2.2.7</version.juel>
  </properties>

  <dependencyManagement>
    <dependencies>

      <dependency>
        <groupId>org.camunda.bpm</groupId>
        <artifactId>camunda-core-internal-dependencies</artifactId>
        <version>${project.version}</version>
        <scope>import</scope>
        <type>pom</type>
      </dependency>

      <dependency>
        <groupId>de.odysseus.juel</groupId>
        <artifactId>juel-api</artifactId>
        <version>${version.juel}</version>
      </dependency>

      <dependency>
        <groupId>de.odysseus.juel</groupId>
        <artifactId>juel-impl</artifactId>
        <version>${version.juel}</version>
      </dependency>

      <dependency>
        <groupId>de.odysseus.juel</groupId>
        <artifactId>juel-spi</artifactId>
        <version>${version.juel}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <modules>
    <module>bom</module>
    <module>engine</module>
    <module>feel-api</module>
    <module>feel-juel</module>
    <module>feel-scala</module>
  </modules>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <configuration>
            <redirectTestOutputToFile>true</redirectTestOutputToFile>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>

    <plugins>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
      </plugin>
    </plugins>
  </build>

</project>
