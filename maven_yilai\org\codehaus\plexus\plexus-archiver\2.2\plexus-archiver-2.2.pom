<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.codehaus.plexus</groupId>
    <artifactId>plexus-components</artifactId>
    <version>1.1.20</version>
  </parent>

  <artifactId>plexus-archiver</artifactId>
  <version>2.2</version>

  <name>Plexus Archiver Component</name>

  <scm>
    <connection>scm:git:**************:sonatype/plexus-archiver.git</connection>
    <developerConnection>scm:git:**************:sonatype/plexus-archiver.git</developerConnection>
    <url>http://github.com/sonatype/plexus-archiver</url>
    <tag>plexus-archiver-2.2</tag>
  </scm>
  <issueManagement>
    <system>jira</system>
    <url>http://jira.codehaus.org/browse/PLXCOMP/component/12540</url>
  </issueManagement>

  <properties>
    <useJvmChmod>true</useJvmChmod>
  </properties>

  <contributors>
    <contributor>
      <name>Dan Tran</name>
    </contributor>
    <contributor>
      <name>Richard van der Hoff</name>
    </contributor>
  </contributors>

  <dependencies>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-container-default</artifactId>
      <version>1.0-alpha-9-stable-1</version>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-utils</artifactId>
      <version>3.0.7</version>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-io</artifactId>
      <version>2.0.4</version>
    </dependency>
  </dependencies>
  
  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>2.12.2</version>
        <configuration>
          <systemPropertyVariables>
            <useJvmChmod>${useJvmChmod}</useJvmChmod>
          </systemPropertyVariables>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-release-plugin</artifactId>
        <version>2.3.2</version>
        <configuration>
          <!-- olamy: exclude files with strange names as failed here on osx -->
          <checkModificationExcludes>
            <checkModificationExclude>**/src/test/resources/utf8/**</checkModificationExclude>
          </checkModificationExcludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <version>1.1.1</version>
        <executions>
          <execution>
            <id>enforce-java</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireJavaVersion>
                  <version>1.7.0</version>
                </requireJavaVersion>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  
</project>
