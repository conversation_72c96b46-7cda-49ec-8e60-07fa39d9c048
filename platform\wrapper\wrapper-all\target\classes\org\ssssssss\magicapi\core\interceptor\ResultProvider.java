package org.ssssssss.magicapi.core.interceptor;

import org.ssssssss.magicapi.model.RequestEntity;

/**
 * 兼容性适配器类
 * 用于解决magic-api版本兼容性问题
 * 将错误的路径引用重定向到正确的类
 */
public interface ResultProvider {
    /**
     * 构建结果
     * @param requestEntity 请求实体
     * @param code 状态码
     * @param message 消息
     * @param data 数据
     * @return 构建的结果
     */
    Object buildResult(RequestEntity requestEntity, int code, String message, Object data);
}
