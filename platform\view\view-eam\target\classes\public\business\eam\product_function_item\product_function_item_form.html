<!--
/**
 * 内容 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2024-04-08 07:38:32
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
	<title th:text="${lang.translate('内容')}">内容</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="opacity:0">

        <input name="id" id="id"  type="hidden"/>

         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-8381-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column" >

                <!-- text_input : 名称 ,  name -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('名称')}">名称</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="name" id="name" name="name" th:placeholder="${ lang.translate('请输入'+'名称') }" type="text" class="layui-input"    lay-verify="|required"  />
                    </div>
                </div>
            
                <!-- text_input : 标识符 ,  identifier -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('标识符')}">标识符</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="identifier" id="identifier" name="identifier" th:placeholder="${ lang.translate('请输入'+'标识符') }" type="text" class="layui-input"    lay-verify="|required"  />
                    </div>
                </div>
            
                <!-- text_input : 描述 ,  functionDesc -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('描述')}">描述</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="functionDesc" id="functionDesc" name="functionDesc" th:placeholder="${ lang.translate('请输入'+'描述') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- text_input : 单位 ,  functionUnit -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('单位')}">单位</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="functionUnit" id="functionUnit" name="functionUnit" th:placeholder="${ lang.translate('请输入'+'单位') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- select_box : 数据类型 ,  dataType  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('数据类型')}">数据类型</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <div id="dataType" input-type="select" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.IotDataTypeEnum')}" extraParam="{}"></div>
                    </div>
                </div>
            
                <!-- text_input : 内容定义 ,  dataValue -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('内容定义')}">内容定义</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="dataValue" id="dataValue" name="dataValue" th:placeholder="${ lang.translate('请输入'+'内容定义') }" type="text" class="layui-input"  />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 180px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消','','form.button')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('iot_product_function_item:create','iot_product_function_item:update','iot_product_function_item:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存','','form.button')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var SELECT_DATATYPE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.IotDataTypeEnum')}]];
    var VALIDATE_CONFIG={"identifier":{"labelInForm":"标识符","inputType":"text_input","required":true},"dataType":{"labelInForm":"数据类型","inputType":"select_box","required":true},"name":{"labelInForm":"名称","inputType":"text_input","required":true}};
    var AUTH_PREFIX="iot_product_function_item";

    // ownerId
    var OWNER_ID = [[${ownerId}]] ;

</script>



<script th:src="'/business/eam/product_function_item/product_function_item_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/product_function_item/product_function_item_form.js?'+${cacheKey}"></script>

</body>
</html>