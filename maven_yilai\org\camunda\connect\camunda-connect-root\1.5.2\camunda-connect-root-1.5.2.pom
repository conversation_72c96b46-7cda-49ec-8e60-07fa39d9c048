<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.camunda.connect</groupId>
    <artifactId>camunda-connect-bom</artifactId>
    <version>1.5.2</version>
    <relativePath>bom</relativePath>
  </parent>

  <artifactId>camunda-connect-root</artifactId>
  <name>camunda BPM - connect - root</name>
  <inceptionYear>2014</inceptionYear>
  <packaging>pom</packaging>

  <properties>
    <connect.version.old>1.5.0</connect.version.old>
    <license.includeTransitiveDependencies>false</license.includeTransitiveDependencies>
    <additionalparam>-Xdoclint:none</additionalparam>
  </properties>

  <modules>
    <module>bom</module>
    <module>core</module>
    <module>http-client</module>
    <module>soap-http-client</module>
    <module>connectors-all</module>
  </modules>

  <dependencyManagement>
    <dependencies>

      <dependency>
        <groupId>org.camunda.commons</groupId>
        <artifactId>camunda-commons-bom</artifactId>
        <version>1.9.0</version>
        <scope>import</scope>
        <type>pom</type>
      </dependency>

      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient</artifactId>
        <version>4.5.13</version>
      </dependency>
      
      <dependency>
        <groupId>commons-codec</groupId>
        <artifactId>commons-codec</artifactId>
        <version>1.15</version>
      </dependency>

      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>4.12</version>
      </dependency>

      <dependency>
        <groupId>org.assertj</groupId>
        <artifactId>assertj-core</artifactId>
        <version>2.9.1</version>
      </dependency>

      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-classic</artifactId>
        <version>1.1.2</version>
      </dependency>

    </dependencies>
  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.felix</groupId>
          <artifactId>maven-bundle-plugin</artifactId>
          <configuration>
            <instructions>
              <Export-Package>
                org.camunda.connect*
              </Export-Package>
            </instructions>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.17</version>
          <configuration>
            <redirectTestOutputToFile>true</redirectTestOutputToFile>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>license-maven-plugin</artifactId>
        <version>1.14</version>
        <configuration>
          <acceptPomPackaging>true</acceptPomPackaging>
          <excludedScopes>test</excludedScopes>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>license-header-check</id>
      <build>
        <plugins>
          <plugin>
            <groupId>com.mycila</groupId>
            <artifactId>license-maven-plugin</artifactId>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

  <scm>
    <url>https://github.com/camunda/camunda-connect</url>
    <connection>scm:git:**************:camunda/camunda-connect.git</connection>
    <developerConnection>scm:git:**************:camunda/camunda-connect.git</developerConnection>
    <tag>1.5.2</tag>
  </scm>

</project>
