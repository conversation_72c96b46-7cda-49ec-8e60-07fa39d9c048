<!--
/**
 * 保养任务 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2024-12-02 09:16:46
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
	<title th:text="${lang.translate('保养任务')}">保养任务</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="opacity:0">

        <input name="id" id="id"  type="hidden"/>

         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-5557-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                <!-- text_input : 任务名称 ,  name -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('任务名称')}">任务名称</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="name" id="name" name="name" th:placeholder="${ lang.translate('请输入'+'任务名称') }" type="text" class="layui-input"    lay-verify="|required"  />
                    </div>
                </div>
            
                <!-- select_box : 保养类型 ,  maintainType  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('保养类型')}">保养类型</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="maintainType" input-type="select" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_maintain_type'}" extraParam="{}"></div>
                    </div>
                </div>
            
                <!-- select_box : 保养班组 ,  groupId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('保养班组')}">保养班组</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="groupId" input-type="select" th:data="${'/service-eam/eam-maintain-group/query-list'}" extraParam="{}"></div>
                    </div>
                </div>
            
                <!-- button : 执行人 ,  executorId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('执行人')}">执行人</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="executorId" id="executorId" name="executorId"  type="hidden" class="layui-input"   />
                        <button id="executorId-button" type="button" action-type="emp-dialog" class="layui-btn   " style="width: 100%" default-width="100%" auto-width="false"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择人员')}" th:default-label="${lang.translate('请选择人员')}">按钮文本</span></button>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                <!-- number_input : 预计工时(时) ,  totalCost  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('预计工时(时)')}">预计工时(时)</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="totalCost" id="totalCost" name="totalCost" th:placeholder="${ lang.translate('请输入'+'预计工时(时)') }" type="text" class="layui-input"    lay-verify="|required"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="2"  value="0.0" />
                    </div>
                </div>
            
                <!-- number_input : 超时工时(分) ,  timeout  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('超时工时(分)')}">超时工时(分)</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="timeout" id="timeout" name="timeout" th:placeholder="${ lang.translate('请输入'+'超时工时(分)') }" type="text" class="layui-input"    lay-verify="|required"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="2"  value="2.0" />
                    </div>
                </div>
            
                <!-- number_input : 实际工时(时) ,  actTotalCost  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('实际工时(时)')}">实际工时(时)</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="actTotalCost" id="actTotalCost" name="actTotalCost" th:placeholder="${ lang.translate('请输入'+'实际工时(时)') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="2"  value="0.0" />
                    </div>
                </div>
            
                <!-- number_input : 费用 ,  cost  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('费用')}">费用</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="cost" id="cost" name="cost" th:placeholder="${ lang.translate('请输入'+'费用') }" type="text" class="layui-input"    lay-verify="|required"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="2"  value="2.0" />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                <!-- date_input : 应开始时间 ,  planStartTime  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('应开始时间')}">应开始时间</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input input-type="date" lay-filter="planStartTime" id="planStartTime" name="planStartTime"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择'+'应开始时间') }" type="text" class="layui-input"    lay-verify="|required"   />
                    </div>
                </div>
            
                <!-- date_input : 实际开始 ,  actStartTime  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('实际开始')}">实际开始</div></div>
                    <div class="layui-input-block ">
                        <input input-type="date" lay-filter="actStartTime" id="actStartTime" name="actStartTime"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择'+'实际开始') }" type="text" class="layui-input"    lay-verify=""   />
                    </div>
                </div>
            
                <!-- date_input : 实际完成 ,  actFinishTime  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('实际完成')}">实际完成</div></div>
                    <div class="layui-input-block ">
                        <input input-type="date" lay-filter="actFinishTime" id="actFinishTime" name="actFinishTime"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择'+'实际完成') }" type="text" class="layui-input"    lay-verify=""   />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-2211-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column"  style="padding-top: 0px" >

                <!-- text_area : 备注 ,  notes  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('备注')}">备注</div></div>
                    <div class="layui-input-block ">
                        <textarea lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入'+'备注') }" class="layui-textarea" style="height: 120px" ></textarea>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->

        <fieldset class="layui-elem-field layui-field-title form-group-title" id="random-9216-fieldset">
            <legend>设备信息</legend>
        </fieldset>

        <div class="layui-row form-row" id="random-9216-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                <!-- select_box : 保养设备 ,  assetId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('保养设备')}">保养设备</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="assetId" input-type="select" th:data="${'/service-eam/eam-asset/query-paged-list?ownerCode=asset'}" extraParam="{}"></div>
                    </div>
                </div>
            
                <!-- text_input : 设备名称 ,  assetName -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('设备名称')}">设备名称</div></div>
                    <div class="layui-input-block ">
                        <input  readonly lay-filter="assetName" id="assetName" name="assetName" th:placeholder="${ lang.translate('请输入'+'设备名称') }" type="text" class="layui-input"  />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                <!-- text_input : 设备编码 ,  assetCode -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('设备编码')}">设备编码</div></div>
                    <div class="layui-input-block ">
                        <input  readonly lay-filter="assetCode" id="assetCode" name="assetCode" th:placeholder="${ lang.translate('请输入'+'设备编码') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- select_box : 设备状态 ,  assetStatus  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('设备状态')}">设备状态</div></div>
                    <div class="layui-input-block ">
                        <div id="assetStatus" input-type="select" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.AssetStatusEnum')}" extraParam="{}"></div>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                <!-- text_input : 设备型号 ,  assetModel -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('设备型号')}">设备型号</div></div>
                    <div class="layui-input-block ">
                        <input  readonly lay-filter="assetModel" id="assetModel" name="assetModel" th:placeholder="${ lang.translate('请输入'+'设备型号') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- text_input : 设备序列 ,  assetSn -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('设备序列')}">设备序列</div></div>
                    <div class="layui-input-block ">
                        <input  readonly lay-filter="assetSn" id="assetSn" name="assetSn" th:placeholder="${ lang.translate('请输入'+'设备序列') }" type="text" class="layui-input"  />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->
        <fieldset class="layui-elem-field layui-field-title form-group-title" id="random-8453-fieldset">
        <legend>保养项目</legend>
        </fieldset>
        <div class="layui-row form-row" style="text-align: center;" id="random-8453-content">
            <div style="display: inline-block;padding-right: 8px;padding-left: 8px" class="layui-col-xs12">
            <iframe id="random-8453-iframe" js-fn="maintainSelectList" class="form-iframe" frameborder="0" style="width: 100%"></iframe>
            </div>
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 80px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消','','form.button')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('eam_maintain_task:create','eam_maintain_task:update','eam_maintain_task:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存','','form.button')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var SELECT_ASSETSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetStatusEnum')}]];
    var SELECT_OVERDUE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.MaintainTaskOverdueEnum')}]];
    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.MaintainTaskStatusEnum')}]];
    var VALIDATE_CONFIG={"actStartTime":{"date":true,"labelInForm":"实际开始","inputType":"date_input"},"cost":{"labelInForm":"费用","inputType":"number_input","required":true},"planStartTime":{"date":true,"labelInForm":"应开始时间","inputType":"date_input","required":true},"assetId":{"labelInForm":"保养设备","inputType":"select_box","required":true},"groupId":{"labelInForm":"保养班组","inputType":"select_box","required":true},"name":{"labelInForm":"任务名称","inputType":"text_input","required":true},"actFinishTime":{"date":true,"labelInForm":"实际完成","inputType":"date_input"},"maintainType":{"labelInForm":"保养类型","inputType":"select_box","required":true},"timeout":{"labelInForm":"超时工时(分)","inputType":"number_input","required":true},"totalCost":{"labelInForm":"预计工时(时)","inputType":"number_input","required":true}};
    var AUTH_PREFIX="eam_maintain_task";

    // groupEmployee
    var GROUP_EMPLOYEE = [[${groupEmployee}]] ;

</script>



<script th:src="'/business/eam/maintain_task/maintain_task_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/maintain_task/maintain_task_form.js?'+${cacheKey}"></script>

</body>
</html>