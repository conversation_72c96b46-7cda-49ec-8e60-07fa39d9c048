<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 2015, 2019 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Public License v. 2.0, which is available at
    http://www.eclipse.org/legal/epl-2.0.

    This Source Code may also be made available under the following Secondary
    Licenses when the conditions for such availability set forth in the
    Eclipse Public License v. 2.0 are satisfied: GNU General Public License,
    version 2 with the GNU Classpath Exception, which is available at
    https://www.gnu.org/software/classpath/license.html.

    SPDX-License-Identifier: EPL-2.0 OR GPL-2.0 WITH Classpath-exception-2.0

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.glassfish.jersey.media</groupId>
        <artifactId>project</artifactId>
        <version>2.30.1</version>
    </parent>

    <artifactId>jersey-media-jaxb</artifactId>
    <packaging>jar</packaging>
    <name>jersey-media-jaxb</name>

    <description>
        JAX-RS features based upon JAX-B.
    </description>

    <build>
        <resources>
            <resource>
                <directory>${basedir}/src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <configuration>
                        <!--
                            Excluding only *.tests.* packages for this module. At least one class has to exist in non-excluded
                            packages to generate proper -javadoc.jar file. See https://jira.codehaus.org/browse/MJAVADOC-329
                          -->
                        <excludePackageNames>*.tests.*</excludePackageNames>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <inherited>false</inherited>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <compilerArguments>
                        <!-- Do not warn about using sun.misc.Unsafe -->
                        <XDignore.symbol.file />
                    </compilerArguments>
                    <showWarnings>false</showWarnings>
                    <fork>false</fork>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.sun.istack</groupId>
                <artifactId>istack-commons-maven-plugin</artifactId>
                <inherited>true</inherited>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <inherited>true</inherited>
            </plugin>
            <plugin>
                <groupId>de.jflex</groupId>
                <artifactId>maven-jflex-plugin</artifactId>
                <version>1.4.3</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/generated-sources/rsrc-gen</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <inherited>true</inherited>
                <extensions>true</extensions>
                <configuration>
                    <instructions>
                        <Import-Package>javax.xml.bind.*;version="!",*</Import-Package>
                        <Export-Package>org.glassfish.jersey.jaxb.*;version=${project.version}</Export-Package>
                    </instructions>
                    <unpackBundle>true</unpackBundle>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <dependencies>

        <dependency>
            <groupId>org.glassfish.jersey.core</groupId>
            <artifactId>jersey-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>jakarta.xml.bind</groupId>
            <artifactId>jakarta.xml.bind-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.glassfish.hk2.external</groupId>
            <artifactId>jakarta.inject</artifactId>
        </dependency>
        <dependency>
            <groupId>org.glassfish.hk2</groupId>
            <artifactId>osgi-resource-locator</artifactId>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>jdk11+</id>
            <activation>
                <jdk>[11,)</jdk>
            </activation>
            <dependencies>
              <dependency>
                  <groupId>com.sun.xml.bind</groupId>
                  <artifactId>jaxb-osgi</artifactId>
                  <scope>test</scope>
              </dependency>
            </dependencies>
        </profile>
    </profiles>
</project>
