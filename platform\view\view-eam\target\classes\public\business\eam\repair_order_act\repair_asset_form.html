<!--
/**
 * 分单规则 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2022-06-02 09:38:41
 */
 -->
<!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <title th:text="${lang.translate('设备信息')}">设备信息</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="height:250px">




        <div class="layui-row form-row" id="random-1810-content">

            <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >

                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('名称')}">名称</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="name" id="name" name="name" type="text" class="layui-input"      />
                    </div>
                </div>

                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('设备编号')}">设备编号</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="assetCode" id="assetCode" name="assetCode" type="text" class="layui-input"     />
                    </div>
                </div>



                <!--结束：栏次内字段循环-->
            </div>
            <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >



                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('型号')}">型号</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="model" id="model" name="model" type="text" class="layui-input"     />
                    </div>
                </div>




                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--开始：group 循环-->





    </form>

</div>


<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>

<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var AUTH_PREFIX="eam_repair_form_1";
    var ORDER_ID = [[${orderId}]] ;
</script>



<script th:src="'/business/eam/repair_order_act/repair_asset_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/repair_order_act/repair_asset_form.js?'+${cacheKey}"></script>

</body>
</html>
