<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 2013, 2018 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Public License v. 2.0, which is available at
    http://www.eclipse.org/legal/epl-2.0.

    This Source Code may also be made available under the following Secondary
    Licenses when the conditions for such availability set forth in the
    Eclipse Public License v. 2.0 are satisfied: GNU General Public License,
    version 2 with the GNU Classpath Exception, which is available at
    https://www.gnu.org/software/classpath/license.html.

    SPDX-License-Identifier: EPL-2.0 OR GPL-2.0 WITH Classpath-exception-2.0

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.glassfish.hk2</groupId>
        <artifactId>external</artifactId>
        <version>2.6.1</version>
    </parent>
    <groupId>org.glassfish.hk2.external</groupId>
    <artifactId>aopalliance-repackaged</artifactId>

    <name>aopalliance version ${aopalliance.version} repackaged as a module</name>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>step1-unpack-sources</id>
                        <phase>process-sources</phase>
                        <goals>
                            <goal>unpack</goal>
                        </goals>
                        <configuration>
                            <artifactItems>
                                <artifactItem>
                                    <groupId>aopalliance</groupId>
                                    <artifactId>aopalliance</artifactId>
                                    <version>${aopalliance.version}</version>
                                    <classifier>sources</classifier>
                                    <overWrite>false</overWrite>
                                    <outputDirectory>${project.build.directory}/alternateLocation</outputDirectory>
                                </artifactItem>
                            </artifactItems>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>step2-add-sources</id>
                        <phase>process-sources</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>${project.build.directory}/alternateLocation</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <configuration>
                    <instructions>
                        <Embed-Dependency>
                            *;scope=compile;inline=true
                        </Embed-Dependency>
                        <!-- We set a mandatory attribute for these packages, because we don't
                             want them to be visible to just anyone. An importer must specify
                             the mandatory attribute for it to be able to import. See 
                             section #3.6.5 of OSGi R4 spec.
                             Why is this required?
                             See https://glassfish.dev.java.net/issues/show_bug.cgi?id=5385
                             By doing this, we allow user have any version of apache-commons lib
                             in their application class loader hierarchy. They don't even have to
                             set delegation=false in sun-web.xml! This is the true advantage of
                             using OSGi. Sahoo.
                        -->
                        <Export-Package>org.aopalliance.*;version=${aopalliance.version}</Export-Package>
                        <Private-Package>!*</Private-Package>
                    </instructions>
                </configuration>
                <executions>
                    <execution>
                        <id>osgi-manifest</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>manifest</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    
    <dependencies>
        <dependency>
            <groupId>aopalliance</groupId>
            <artifactId>aopalliance</artifactId>
            <version>${aopalliance.version}</version>
            <optional>true</optional>
        </dependency>
    </dependencies>
</project>
