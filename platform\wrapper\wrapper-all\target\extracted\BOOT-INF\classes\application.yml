server:
  session:
    # session超时间时间
    timeout: 12000s
  servlet:
    session:
      # 会话超时时间，如 30s , 20m 等
      timeout: 12000s
  # 应用web访问端口,可按照实际情况进行修改
  port: 8089
spring:
  main:
    allow-bean-definition-overriding: true
  application:
    name: service-all
    description: Foxnic Web Service All
  servlet:
    multipart:
      max-file-size: 512MB
      max-request-size: 512MB
  mvc:
    static-path-pattern: /**
  resource:
    static-locations: classpath:/static/,classpath:/public/
  datasource:
    # Druid 监控 , 访问 ${url-prefix}/login.html
    monitor:
      url-prefix: druid
      username: root
      password: P@ssw0rd123456
      # allow: **************,127.0.0.1
      # deny: ************
      resetEnable: true
    druid:
      primary:
        # 数据库连接信息url,username,password三个属性是否加密，如果enable为true，则加密，加密因子从file指定的路径获取
        # 加密文件在 develop.encrypt 配置
        encrypt: false
        # 8.x
        #driver-class-name: com.mysql.cj.jdbc.Driver
        # 5.x
        #driver-class-name: com.mysql.jdbc.Driver
#        url: bOfoWsTmRXXNZ0PJIvNUhUWY9QIencf1xUgWg3JXN9wRsy+//nysIrOGtLzU/jSlKj5omytE6kA2n0reP4uRPA26r3NJyVKMevbr0GohnbCG71bfrkAzU4olIDjlDw+c+skqRkBbYTLNZdrNEEWGdiemKxgCcfaQCiAiSlNS7YYC0OxoXN7a35nMng2URpZslkU2K1uiCFBx/xvjebwtJWChSKd+sYjK/P2zKRIgNTWdaxkJKb8aurSsg5Lc37R042hAtBQMqU2nlydvJHOjvKDiMZjxsq1eURFw/xiCSTG7AZkejM28etaPDkA+as6Rnfd46VoINscBOFuceFqHcogM0jXxoGx2J3kZwxDH0kk=
#        username: tD5wkJAfnCt04A+Y+QN5GA==
#        password: ODGx5F84xpWJgfRFnzAaYhViHJqrTO3uxxDZ2lRlZNk=
        #url: ***********************************************************************************************************************************************************************************************************************************************************************************************
        url: ***************************************************************************************************************************************************************************************************************************************************************************************
        username: foxnic_user
        password: 123456
        # 启动程序时，在连接池中初始化多少个连接
        initialSize: 5
        # 回收空闲连接时，将保证至少有minIdle个连接.
        minIdle: 5
        # 连接池中最多支持多少个活动会话
        maxActive: 300
        # 程序向连接池中请求连接时,超过maxWait的值后，认为本次请求失败，即连接池没有可用连接，单位毫秒，设置-1时表示无限等待
        maxWait: 60000
        socketTimeout: 60000
        connectTimeout: 60000
        # 池中某个连接的空闲时长达到 N 毫秒后, 连接池在下次检查空闲连接时，将回收该连接,要小于防火墙超时设置
        #一个空闲连接在连接池中最小的存活时间，单位为毫秒
        minEvictableIdleTimeMillis: 300000
        #一个空闲连接在连接池中最大的存活时间，单位为毫秒
        maxEvictableIdleTimeMillis: 1800000
        #  3600000 一个小时 检查空闲连接的频率，单位毫秒, 非正整数时表示不进行检查程序没有close连接且空闲时长超过 minEvictableIdleTimeMillis,则会执行validationQuery指定的SQL,以保证该程序连接不会池kill掉,其范围不超过minIdle指定的连接个数。
        # 配置间隔多久启动一次DestroyThread，对连接池内连接进行一次检测，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        #keepAliveBetweenTimeMillis: 3600000
        # 要求程序从池中get到连接后, N 秒后必须close,否则druid 会强制回收该连接,不管该连接中是活动还是空闲。
        # 建议设置为false。设置为True时，会执行连接泄露检查：
        removeAbandoned: false
        # 设置druid 强制回收连接的时限，当程序从池中get到连接开始算起，超过此值后，druid将强制回收该连接，单位秒。
        removeAbandonedTimeout: 180
        # 当druid强制回收连接后，是否将stack trace 记录到日志中
        logAbandoned: true
        # 当程序请求连接，池在分配连接时，是否先检查该连接是否有效。(高效)
        # 建议设置为true，从连接池重用一个空闲连接时，会根据连接的空闲时间决定是否执行validationQuery去判断连接的有效性
        testWhileIdle: true
        # 用来保持连接有效性的，只有空闲时间大于keepAliveBetweenTimeMillis并且小于minEvictableIdleTimeMillis该参数才会有用
        keepAlive: true
        # 检查池中的连接是否仍可用的 SQL 语句,drui会连接到数据库执行该SQL, 如果正常返回，则表示连接可用，否则表示连接不可用
        validationQuery: select 1
        validationQueryTimeout: 5000
        # 程序 申请 连接时,进行连接有效性检查（低效，影响性能）
        # 建议设置为true。设置为True时，从连接池复用一个连接时均执行validationQuery检测连接有效性
        testOnBorrow: true
        # 程序 返还 连接时,进行连接有效性检查（低效，影响性能）
        # 建议设置为false。 归还连接到连接池时执行validationQuery检测连接是否有效
        testOnReturn: false
        # 是否缓存preparedStatement，也就是PSCache。PSCache对支持游标的数据库性能提升巨大，比如说oracle。在mysql下建议关闭。
        poolPreparedStatements: false
        # 要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true。在Druid中，不会存在Oracle下PSCache占用内存过多的问题，可以把这个数值配置大一些，比如说100
        maxOpenPreparedStatements: -1
        # 要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true。在Druid中，不会存在Oracle下PSCache占用内存过多的问题，可以把这个数值配置大一些，比如说100
        maxPoolPreparedStatementPerConnectionSize: -1
        # 是否打印语句，生产环境建议关闭
        printSQL: true
        # 是否使用简单模式输出日志
        printSQLSimple: true
        # 是否输出调用栈
        printSQLCallstack: false
  thymeleaf:
    prefix: classpath:/public/
    check-template-location: true
    suffix: .html
    encoding: UTF-8
    content-type: text/html
    mode: HTML5
    cache: false
  session:
    # 使用外部存储 SESSION ，可以是：NONE、HAZELCAST、JDBC、MONGODB、REDIS，目前仅支持 REDIS
    store-type: NONE
  redis:
    # 是否启用单节点的 redis
    enable: false
    pool:
      #连接池最大连接数（使用负值表示没有限制）
      max-active: 8
      #连接池最大阻塞等待时间（使用负值表示没有限制）
      max-wait: -1
      #连接池中的最大空闲连接
      max-idle: 8
      #连接池中的最小空闲连接
      min-idle: 0
      #连接超时时间（毫秒）
      timeout: 1000
    # 单节配置
    port: 6379
    # redis地址
    host: *************
    password: P@ssw0rd123456
    database: 13
    # 哨兵配置
    sentinel:
      # 是否启用哨兵模式的 redis
      enable: false
      master: common
      nodes: ************:26379,************:26379
    #集群配置
    cluster:
      # 是否启用集群模式的 redis
      enable: false
      nodes: ************:6389,************:6479,************:6579
netdisk:
  # 存储类型，目前支持 minio,disk
  storage-type: minio
  location:
    #本地文件存储方式文件管理
    windows: c:/netdisk
    mac: /Users/<USER>/netdisk
    linux: /app/app/app/netdisk
foxnic:
  api:
    log-enable: true
  config:
    # 按顺序读取 profile 下的配置值，默认使用 default Profile 的配置值，如果位于靠前的 profile 已经有配置，则不再往后读取，即前面 profile 的覆盖后面的 profile ;
    profiles: default
    # 按模块控制菜单呈现，注意在开发环境下，此配置无效。
    module-authorities:
      # 指定最顶层菜单的权限Key，用逗号分隔；若未指定值则忽略该规则，不做控制。
      ########################  public key list  ####################
      #system:report,system:employee,pay,system_monitor_list,hrm,bpm-mgr,auto_perm,changes,hr,basic_permissions,common_system,docs,sys_file:mngr,data_backup,pcm,oauth,public_resource,bpm,mobile_application,dp_rule:mngr,system_config,sys_tpl_file:mngr,job
      ########################  common key list  #######################
      #system_ops
      ########################  eam key list  #######################
      #eam,eam_equipment_mgr
      ########################  ops key list  #######################
      #ops,zbx
      ########################  hr key list  #######################
      #hrm
      ########################  oa key list  #######################
      #oa
      auth-keys:
      #auth-keys:
      # 是否在首页体现 module-authorities 控制
      apply-index-page: true
      # 是否在菜单树体现 module-authorities 控制，主要包括菜单管理、角色选择等。注意，此项若开启可能导致菜单权限勾选不全，建议仅在生产环境开启。
      apply-menu-tree: false
  # 设置 API 接口
  job:
    enable: true
    force-run-job-ids:
    # 指定 Quartz 使用的DAO数据源
    dao-bean-name: primaryDAO
    # 容许的最大作业延长时间，超出则认为 misfire 发生，单位毫秒
    misfire-threshold: 1000
    # 日志保留天数
    log-keep-days: 30
  storage:
    #minio,disk
    mode: disk
    # 是否加密配置，加密文件在 develop.encrypt 配置
    encrypt: true
    disk:
      location:
        #在不同的环境下选择不同的路径
        windows: c:/uploads
        mac: /Users/<USER>/upload
        linux: /app/app/app/upload
    oss-aliyun:
      access-key-Id: WwzbiM7QkNGkw9X0tPA/tJcb9HFyBWtXZh+1Jd0h/zA=
      access-secret: UCYyxDXhItJRZFMApz9cZh45oUPTijVfwycIGpXnj0M=
      end-point: oGiGbQZSuB91rdrCSBfAh8EYYtdKG95securityf2wiI3LdVD98=
      public-bucket: KblbYnUiyWSxvKrTtmmVWg==
      private-bucket: 6ptaLbXYSHWjCGFbLZckLA==
    minio:
      # endpoint: http://101.35.255.173:39090
      endpoint: http://101.35.255.173:39090
      accessKey: iLD9PcLSqv5Q0ELXpXQN
      secretKey: nTdOWm5rW54ZpyZ6iCJovEedOwUq5uXe9W6H4kHW
      bucketAppData: appdata
      bucketNetdisk: appnetdisk
      secure: false
  # 设置 BPM 相关内容
  bpm:
    enable: true
    debug:
      enable: true
      process-definition-code-list: eam_asset_repair,eam_asset_handle,hr_person_income_certificate_apply,hr_recruitment_plan_apply,hr_personnel_requirement_apply,hr_person_confirm,hr_person_leave,hr_person_absencehr_person_official_business,eam_asset_stock_goods_use,bpm_common_action2,bpm_common_action1,eam_asset_equipment_repair,sys_bpm_common,eam_asset_purchase_apply,eam_asset_employee_apply,eam_asset_employee_handover,eam_asset_employee_loss,eam_asset_employee_repair,eam_asset_storage,eam_asset_tranfer,eam_asset_allocate,eam_asset_borrow,eam_asset_borrow_return,eam_asset_scrap,eam_asset_collection,eam_asset_repair_order,eam_asset_collection_return,demo-busi-case
  # 设置 Join 的二级缓存
  cache:
    # 默认配置
    default:
      # local,remote,both,none
      mode: local
      # 本地缓存配置 : 最大元素个数
      local-limit: 10240
      # 本地缓存配置 : 本地缓存超时时长；-1 表示用不过期
      local-expire: 1200000
      # 远程缓存配置 :  超时 20 分钟，单位毫秒; -1 表示用不过期
      remote-expire: 1200000
    # 按实体类型配置缓存细节
    details:
      "org.github.foxnic.web.domain.system.Config":
        strategy:
          by-code:
            is-accurate: true
            cache-empty-result: true
            properties: code
      "org.github.foxnic.web.domain.oauth.Resourze":
        #mode: local
        local-limit: 10240
        local-expire: 1200000
        remote-expire: 1200000
      "org.github.foxnic.web.domain.system.DictItem":
        #mode: local
        local-limit: 10240
        local-expire: 1200000
        remote-expire: 1200000
        strategy:
          query-list:
            is-accurate: true
            cache-empty-result: true
            properties: dictCode
          query-code:
            is-accurate: true
            cache-empty-result: true
            properties: dictCode,code
      "org.github.foxnic.web.domain.system.DbCache":
        #mode: local
        local-limit: 10240
        local-expire: 1200000
        remote-expire: 1200000
        strategy:
          query-list:
            is-accurate: true
            cache-empty-result: true
            properties: area,catalog,ownerType,ownerId
      "org.github.foxnic.web.domain.pcm.Catalog":
        #mode: local
        local-limit: 10240
        local-expire: 1200000
        remote-expire: 1200000
  # 配置集群节点简单替代注册中心，如果启用微服务，则一下配置无效
  cluster:
    # 节点间用于通信的 key，各节点要保持一致
    key: FOXNIC-EAM-202200701-3386
    service:
      service-camunda:
        enable: true
        # 多个节点时用逗号隔开,格式： 地址,权重 | 地址,权重 | ...
        end-points: http://127.0.0.1:8099,1 | http://127.0.0.1:8099,1
      service-common:
        enable: true
        # 多个节点时用逗号隔开,格式： 地址,权重 | 地址,权重 | ...
        end-points: http://127.0.0.1:8089,1 | http://127.0.0.1:8089,1
      service-hrm:
        enable: true
        # 多个节点时用逗号隔开,格式： 地址,权重 | 地址,权重 | ...
        end-points: http://127.0.0.1:8089,1 | http://127.0.0.1:8089,1
      service-oauth:
        enable: true
        # 多个节点时用逗号隔开,格式： 地址,权重 | 地址,权重 | ...
        end-points: http://127.0.0.1:8089,1 | http://127.0.0.1:8089,1
      service-job:
        enable: true
        # 多个节点时用逗号隔开,格式： 地址,权重 | 地址,权重 | ...
        end-points: http://127.0.0.1:8089,1 | http://127.0.0.1:8089,1
      service-eam:
        enable: true
        # 多个节点时用逗号隔开,格式： 地址,权重 | 地址,权重 | ...
        end-points: http://127.0.0.1:8089,1 | http://127.0.0.1:8089,1
      service-ops:
        enable: true
        # 多个节点时用逗号隔开,格式： 地址,权重 | 地址,权重 | ...
        end-points: http://127.0.0.1:8089,1 | http://127.0.0.1:8089,1
      service-hr:
        enable: true
        # 多个节点时用逗号隔开,格式： 地址,权重 | 地址,权重 | ...
        end-points: http://127.0.0.1:8089,1 | http://127.0.0.1:8089,1
  language:
    translator:
      enable: false
      # 配置是否加密，加密文件在 develop.encrypt 配置
      encrypt: false
      baidu:
        appId: 20250713002405324
        securityKey: cpBuOIHhJ
security:
  # 模式：session / jwt
  mode: both
  # 配置无需鉴权可以访问的地址
  # /service-storage/sys-file/upload/**,/service-storage/sys-file/image/** 移动端访问开放，后续修正
  ignored-urls: /syscomm/public/**,/ops/public/**,/hr/public/**,/eam/public/**,/test/**,/magic/web/**,/business/eam/asset/asset_info_scan.html,/jmreport/**/*,/jmreport/**,/jmreport/list,/ureport/**/*,/business/common/common/sso_login.html,/service-common/system/sso-check,/service-eam/eam-c-cust-repair-type/query-paged-list,/business/eam/c_cust_repair_apply/c_cust_repair_apply_form_full_p.html,/service-eam/eam-c-cust-repair-apply/query-paged-list,/service-eam/eam-c-cust-repair-apply/get-by-id,/service-eam/eam-c-cust-repair-apply/insert,/business/eam/c_cust_repair_apply/c_cust_repair_apply_list_public.html,/business/eam/c_cust_repair_apply/c_cust_repair_apply_form_public.html,/share/public.html,/service-storage/sys-file/download,/business/ops/software_media/software_media_public_list.html,/service-ops/ops-software-media/query-paged-list,/service-ops/ops-information-system/query-paged-list,/business/ops/information_system/information_system_public_list.html,/service-ops/ops-host/query-paged-list,/**/host_public_list.html,/service-ops/ops-db-info/public-query-paged-list,/**/db_info_public_list.html,/service-ops/ops-db-info/public-query-paged-list,/**/db_info_public_list.html,/service-eam/eam-asset/user-login-data ,/back_to_portal.html,/service-storage/sys-file/upload/** , /service-storage/sys-file/image/** ,   /public.html ,/share/public.html , /console/** , /assets/** , /pages/** , /*.ico ,/static/**,/extmodule/** , /module/** ,  /**/*.css , /**/*.js , /error , /druid/**,/business/eam/user_book/**
  # mode=session 时配置的登录页面
  login-page: /login.html
  # 配置登录标识的范围与优先级,可选值在 LoginIdentityType 枚举中定义
  login-identity-priority: employee_badge,user_account,user_phone,user_id
  token-readers: com.dt.platform.common.sso.ApplicationTokenReader
  jwt:
    key-location: foxnic-web.jks
    key-alias: foxnic-web
    key-pass: fox-123456-nic
    # 缓存类型  local/remote/both
    cache-type: local
    iss: felord.cn
    sub: all
    # accessToken 超时秒数，1800秒 即 30分钟
    access-expire-seconds: 1800
    # refreshToken 超时秒数，3600秒 即 60分钟;28800 即 8小时
    refresh-expire-seconds: 28800
  build-in:
    # 是否显示内置账户
    display-user: true
    # 是否显示内置角色
    display-role: true
    # 是否显示内置业务角色
    display-busi-role: true
logging:
  config: classpath:logback-config.xml
snowflake:
  datacenterId: 1
  workerId: 1
management:
  endpoints:
    web:
      exposure:
        include: "*"
#自动扫描API
springfox:
  documentation:
    auto-startup: false

knife4j:
  enable: true
  title: ${spring.application.name}
  description: ${spring.application.description}
  version: 1.0
  setting:
    enableSwaggerModels: true
    swaggerModelName: EamModel

develop:
  #language: defaults
  start-relation-monitor: true
  deploy:
    enable: false
    # 可以使用以当前项目为基础的相对路径，指定静态资源自动部署的项目目录
    projects: ../view/view-eam
  # 加密配置
  encrypt:
    file:
      #密码文件，文件内容为任意，用于对yml文件中相关涉及加密的密文参数进行解密，该文件内容作为加解密因子的一部分
      #根据密码文件内容+敏感参数明文->生成yml文件的密文参数
      #如果yml相关敏感参数不需要加密，可以按照手册要去禁用对敏感参数的加密
      windows: d:/foxnic/passwd.txt
      mac: /Users/<USER>/foxnic/passwd.txt
      linux: /foxnic/passwd.txt
minidao:
  base-package: org.jeecg.modules.jmreport.desreport.dao*
  db-type: mysql
app:
  # 租户ID
  tenant-id: T001
  sso:
    token-validate-api: http://127.0.0.1:8089/service-common/system/sso-check
    client-name: APP-SSO3
magic-api:
  show-url: false #是否显示接口地址
  web: /magic/web    # magic-api控制台访问地址
  task:
    thread-name-prefix: magic-task- #线程池名字前缀
    pool:
      size: 8 #线程池大小，默认值为CPU核心数
    shutdown:
      awaitTermination: false #关闭时是否等待任务执行完毕，默认为false
      awaitTerminationPeriod: 10s # 关闭时最多等待任务执行完毕的时间
  resource:          # magic-api数据存储信息配置
    type: database
    tableName: magic_api_file_v2
    location: workspace/magic/data      # location表示使用本地文件存储，存储路径为工作目录workspace/magic/data下
    #datasource:  primaryDataSource     # datasource表示数据源存储，这里指定使用的数据源， datasource和location选择一个
    readonly: false # 是否是只读模式
  auto-import-package: java.lang.*,java.util.* #自动导包
  show-sql: true #配置打印SQL
  secret-key: magic@KL123456
  date-pattern: # 配置请求参数支持的日期格式
    - yyyy-MM-dd
    - yyyy-MM-dd HH:mm:ss
    - yyyyMMddHHmmss
    - yyyyMMdd
  response: |- #配置JSON格式，格式为magic-script中的表达式
    {
      code: code,
      message: message,
      data,
      timestamp,
      requestTime,
      executeTime,
    }
  response-code:
    success: 1 #执行成功的code值
    invalid: 0 #参数验证未通过的code值
    exception: -1 #执行出现异常的code值
  banner: false #是否显示banner
  backup: #备份相关配置
    enable: true #是否启用
    max-history: -1 #备份保留天数，-1为永久保留
    #datasource: magic  #指定数据源（单数据源时无需配置，多数据源时默认使用主数据源，如果存在其他数据源中需要指定。）
    table-name: magic_backup_record_v2 #使用数据库存储备份时的表名
  #security: # 安全配置
    #username: admin # 登录用的用户名
    #password: 123456 # 登录用的密码
  debug:
    timeout: 60 # 断点超时时间，默认60s
liteflow:
  #规则文件路径
  #rule-source: flow.xml
  rule-source: el_xml:com.yomahub.liteflow.parser.sql.SQLXmlELParser
  #-----------------以下非必须-----------------
  #liteflow是否开启，默认为true
  enable: true
  #liteflow的banner打印是否开启，默认为true
  print-banner: true
  #zkNode的节点，只有使用zk作为配置源的时候才起作用，默认为/lite-flow/flow
  zk-node: /lite-flow/flow
  #上下文的最大数量槽，默认值为1024
  slot-size: 1024
  #FlowExecutor的execute2Future的线程数，默认为64
  main-executor-works: 64
  #FlowExecutor的execute2Future的自定义线程池Builder，LiteFlow提供了默认的Builder
  main-executor-class: com.yomahub.liteflow.thread.LiteFlowDefaultMainExecutorBuilder
  #自定义请求ID的生成类，LiteFlow提供了默认的生成类
  request-id-generator-class: com.yomahub.liteflow.flow.id.DefaultRequestIdGenerator
  #并行节点的线程池Builder，LiteFlow提供了默认的Builder
  thread-executor-class: com.yomahub.liteflow.thread.LiteFlowDefaultWhenExecutorBuilder
  #异步线程最长的等待时间(只用于when)，默认值为15000
  when-max-wait-time: 15000
  #异步线程最长的等待时间(只用于when)，默认值为MILLISECONDS，毫秒
  when-max-wait-time-unit: MILLISECONDS
  #when节点全局异步线程池最大线程数，默认为16
  when-max-workers: 16
  #并行循环子项线程池最大线程数，默认为16
  parallel-max-workers: 16
  #并行循环子项线程池等待队列数，默认为512
  parallel-queue-limit: 512
  #并行循环子项的线程池Builder，LiteFlow提供了默认的Builder
  parallel-loop-executor-class: com.yomahub.liteflow.thread.LiteFlowDefaultParallelLoopExecutorBuilder
  #when节点全局异步线程池等待队列数，默认为512
  when-queue-limit: 512
  #设置解析模式，一共有三种模式，PARSE_ALL_ON_START | PARSE_ALL_ON_FIRST_EXEC | PARSE_ONE_ON_FIRST_EXEC
  parse-mode: PARSE_ALL_ON_START
  #全局重试次数，默认为0
  retry-count: 3
  #是否支持不同类型的加载方式混用，默认为false
  support-multiple-type: false
  #全局默认节点执行器
  node-executor-class: com.yomahub.liteflow.flow.executor.DefaultNodeExecutor
  #是否打印执行中过程中的日志，默认为true
  print-execution-log: true
  #是否开启本地文件监听，默认为false
  enable-monitor-file: false
  #是否开启快速解析模式，默认为false
  fast-load: false
  #简易监控配置选项
  monitor:
    #监控是否开启，默认不开启
    enable-log: false
    #监控队列存储大小，默认值为200
    queue-limit: 200
    #监控一开始延迟多少执行，默认值为300000毫秒，也就是5分钟
    delay: 300000
    #监控日志打印每过多少时间执行一次，默认值为300000毫秒，也就是5分钟
    period: 300000
  rule-source-ext-data-map:
    driverClassName: com.mysql.jdbc.Driver
    url: *******************************************************************************************************************************************************************************************************************************************************************
    username: foxnic_user
    password: 123456
    applicationName: app
    #是否开启SQL日志
    sqlLogEnabled: true
    #是否开启SQL数据轮询自动刷新机制 默认不开启
    pollingEnabled: true
    pollingIntervalSeconds: 60
    pollingStartSeconds: 60
    #以下是chain表的配置，这个一定得有
    chainTableName: sys_liteflow_chain
    chainApplicationNameField: application_name
    chainNameField: chain_name
    elDataField: el_data
    #以下是决策路由字段的配置，如果你没用到决策路由，可以不配置
    routeField: route
    namespaceField: namespace
    #是否启用这条规则
    chainEnableField: status
    #以下是script表的配置，如果你没使用到脚本，下面可以不配置
    scriptTableName: sys_liteflow_script
    scriptApplicationNameField: application_name
    scriptIdField: script_id
    scriptNameField: script_name
    scriptDataField: script_data
    scriptTypeField: script_type
    scriptLanguageField: script_language
    scriptEnableField: status
third-party:
  map-baidu:
    api: http://api.baidu.com
    web-ak: NHSaW6fQRiAb5rcUYFrjzqUQ8WjzSHcn
    service-ak: I1iruTOiEMidZAWfs9QEzUbuKHLmeWAO
###
#st方式登录，st一般为对方token
#-->调用com.dt.platform.common.sso.ApplicationTokenReader
#-->调用(token-validate-api)验证，第三方系统验证，根据第三方token返回empno
#-->返回empno
#-->系统sso登录
#lang:en,ch
#format:json
#原的
#http://127.0.0.1:8089/security/sso-login.html?st=123456789&lang=ch&format=json
#覆盖重写
#http://127.0.0.1:8089/business/common/common/sso_login.html?st=123456789&lang=ch&format=json

###
#uid方式,uid 为sys_user的账户ID
#http://127.0.0.1:8089/security/sso-login.html?uid=110588348101165911&lang=ch&format=json
#http://127.0.0.1:8089/security/sso-login.html?uid=110588348101165911&lang=ch
#http://127.0.0.1:8089/security/sso-login.html?uid=819262007478845440&lang=ch&format=json



