<!--
/**
 * 保养方案 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2024-12-03 19:11:19
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
	<title th:text="${lang.translate('保养方案')}">保养方案</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="opacity:0">

        <input name="id" id="id"  type="hidden"/>

         <!--开始：group 循环-->

        <fieldset class="layui-elem-field layui-field-title form-group-title" id="random-0008-fieldset">
            <legend>基本信息</legend>
        </fieldset>

        <div class="layui-row form-row" id="random-0008-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                <!-- text_input : 计划名称 ,  name -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('计划名称')}">计划名称</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="name" id="name" name="name" th:placeholder="${ lang.translate('请输入'+'计划名称') }" type="text" class="layui-input"    lay-verify="|required"  />
                    </div>
                </div>
            
                <!-- select_box : 保养班组 ,  groupId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('保养班组')}">保养班组</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="groupId" input-type="select" th:data="${'/service-eam/eam-maintain-group/query-list'}" extraParam="{}"></div>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                <!-- select_box : 保养类型 ,  maintainType  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('保养类型')}">保养类型</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="maintainType" input-type="select" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_maintain_type'}" extraParam="{}"></div>
                    </div>
                </div>
            
                <!-- number_input : 超时工时(分) ,  timeout  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('超时工时(分)')}">超时工时(分)</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="timeout" id="timeout" name="timeout" th:placeholder="${ lang.translate('请输入'+'超时工时(分)') }" type="text" class="layui-input"    lay-verify="|required"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="2"  value="2.0" />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                <!-- number_input : 预计工时(时) ,  totalCost  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('预计工时(时)')}">预计工时(时)</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="totalCost" id="totalCost" name="totalCost" th:placeholder="${ lang.translate('请输入'+'预计工时(时)') }" type="text" class="layui-input"    lay-verify="|required"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="2"  value="0.0" />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-3391-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column"  style="padding-top: 0px" >

                <!-- text_area : 方案说明 ,  info  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('方案说明')}">方案说明</div></div>
                    <div class="layui-input-block ">
                        <textarea lay-filter="info" id="info" name="info" th:placeholder="${ lang.translate('请输入'+'方案说明') }" class="layui-textarea" style="height: 80px" ></textarea>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column"  style="padding-top: 0px" >

                <!-- text_area : 备注 ,  notes  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('备注')}">备注</div></div>
                    <div class="layui-input-block ">
                        <textarea lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入'+'备注') }" class="layui-textarea" style="height: 80px" ></textarea>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->

        <fieldset class="layui-elem-field layui-field-title form-group-title" id="random-9198-fieldset">
            <legend>计划周期</legend>
        </fieldset>

        <div class="layui-row form-row" id="random-9198-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >

                <!-- text_input : 保养周期 ,  actionCycleId -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('保养周期')}">保养周期</div></div>
                    <div class="layui-input-block ">
                        <input  readonly lay-filter="actionCycleId" id="actionCycleId" name="actionCycleId" th:placeholder="${ lang.translate('请输入'+'保养周期') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- date_input : 开始时间 ,  startTime  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('开始时间')}">开始时间</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input input-type="date" lay-filter="startTime" id="startTime" name="startTime"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择'+'开始时间') }" type="text" class="layui-input"    lay-verify="|required"   />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >

                <!-- date_input : 结束时间 ,  endTime  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('结束时间')}">结束时间</div></div>
                    <div class="layui-input-block ">
                        <input input-type="date" lay-filter="endTime" id="endTime" name="endTime"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择'+'结束时间') }" type="text" class="layui-input"    lay-verify=""   />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->
        <fieldset class="layui-elem-field layui-field-title form-group-title" id="random-9565-fieldset">
        <legend>设备范围</legend>
        </fieldset>
        <div class="layui-row form-row" style="text-align: center;" id="random-9565-content">
            <div style="display: inline-block;padding-right: 8px;padding-left: 8px" class="layui-col-xs12">
            <iframe id="random-9565-iframe" js-fn="assetSelectList" class="form-iframe" frameborder="0" style="width: 100%"></iframe>
            </div>
        </div>
         <!--开始：group 循环-->
        <fieldset class="layui-elem-field layui-field-title form-group-title" id="random-2558-fieldset">
        <legend>保养项目</legend>
        </fieldset>
        <div class="layui-row form-row" style="text-align: center;" id="random-2558-content">
            <div style="display: inline-block;padding-right: 8px;padding-left: 8px" class="layui-col-xs12">
            <iframe id="random-2558-iframe" js-fn="maintainSelectList" class="form-iframe" frameborder="0" style="width: 100%"></iframe>
            </div>
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 80px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消','','form.button')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('eam_maintain_plan:create','eam_maintain_plan:update','eam_maintain_plan:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存','','form.button')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var SELECT_CYCLEMETHOD_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.MaintainCycleMethodEnum')}]];
    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.EamPlanStatusEnum')}]];
    var VALIDATE_CONFIG={"groupId":{"labelInForm":"保养班组","inputType":"select_box","required":true},"name":{"labelInForm":"计划名称","inputType":"text_input","required":true},"startTime":{"date":true,"labelInForm":"开始时间","inputType":"date_input","required":true},"endTime":{"date":true,"labelInForm":"结束时间","inputType":"date_input"},"maintainType":{"labelInForm":"保养类型","inputType":"select_box","required":true},"timeout":{"labelInForm":"超时工时(分)","inputType":"number_input","required":true},"totalCost":{"labelInForm":"预计工时(时)","inputType":"number_input","required":true}};
    var AUTH_PREFIX="eam_maintain_plan";

    // 单据ID
    var BILL_ID = [[${billId}]] ;
    // 单据类型
    var BILL_TYPE = [[${billType}]] ;

</script>



<script th:src="'/business/eam/maintain_plan/maintain_plan_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/maintain_plan/maintain_plan_form.js?'+${cacheKey}"></script>

</body>
</html>