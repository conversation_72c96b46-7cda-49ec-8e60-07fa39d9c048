<!--
/**
 * 资产 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2021-09-20 21:49:28
 */
 -->
<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <title th:text="${lang.translate('资产')}">资产</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" th:href="@{/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css}" rel="stylesheet"/>
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
    <style>
    </style>
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">


            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 资产分类 , categoryId ,typeName=select_box, isHideInSearch=true -->
                    <!-- 分类编码 , categoryCode ,typeName=text_input, isHideInSearch=true -->
                    <!-- 流程 , procId ,typeName=text_input, isHideInSearch=true -->




                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('资产状态')}" class="search-label assetStatus-label">资产状态</span><span class="search-colon">:</span></div>
                        <div id="assetStatus" th:data="${'/service-eam/eam-asset-status/query-list'}"  style="width:150px" extraParam="{}" ></div>
                    </div>
                    <!-- 资产编号 , assetCode ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('资产编号')}" class="search-label assetCode-label">资产编号</span><span class="search-colon">:</span></div>
                        <input id="assetCode" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>
                    <!-- 来源 , sourceId ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('资产来源')}" class="search-label sourceId-label">来源</span><span class="search-colon">:</span></div>
                        <div id="sourceId" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_source'}" style="width:150px"></div>
                    </div>

                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('存放位置')}" class="search-label positionId-label">位置</span><span class="search-colon">:</span></div>
                        <div id="positionId" th:data="${'/service-eam/eam-position/query-paged-list'}" style="width:150px"></div>
                    </div>

                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 名称 , name ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('资产名称')}" class="search-label name-label">名称</span><span class="search-colon">:</span></div>
                        <input id="name" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>
                    <!-- 规格型号 , model ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('规格型号')}" class="search-label model-label">规格型号</span><span class="search-colon">:</span></div>
                        <input id="model" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>
                    <!-- 序列号 , serialNumber ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('序列号')}" class="search-label serialNumber-label">序列号</span><span class="search-colon">:</span></div>
                        <input id="serialNumber" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>
                    <!-- 资产备注 , assetNotes ,typeName=text_area, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('资产备注')}" class="search-label assetNotes-label">资产备注</span><span class="search-colon">:</span></div>
                        <input id="assetNotes" class="layui-input search-input" style="width: 150px" type="text" />
                    </div>


                </div>

                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 位置 , positionId ,typeName=select_box, isHideInSearch=false -->
                    <!-- 厂商 , manufacturerId ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('资产厂商')}" class="search-label manufacturerId-label">厂商</span><span class="search-colon">:</span></div>
                        <div id="manufacturerId" th:data="${'/service-eam/eam-manufacturer/query-list'}" style="width:150px"></div>
                    </div>
                    <!-- 维保商 , maintainerId ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('维保商')}" class="search-label maintainerId-label">维保商</span><span class="search-colon">:</span></div>
                        <div id="maintainerId" th:data="${'/service-eam/eam-maintainer/query-list'}" style="width:150px"></div>
                    </div>
                    <!-- 采购日期 , purchaseDate ,typeName=date_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('采购日期')}" class="search-label purchaseDate-label">采购日期</span><span class="search-colon">:</span></div>
                        <input type="text" id="purchaseDate-begin" style="width: 150px" lay-verify="date" th:placeholder="${lang.translate('开始日期')}" autocomplete="off" class="layui-input search-input search-date-input"  readonly >
                        <span class="search-dash">-</span>
                        <input type="text" id="purchaseDate-end"  style="width: 150px"  lay-verify="date" th:placeholder="${lang.translate('结束日期')}" autocomplete="off" class="layui-input search-input search-date-input" readonly>
                    </div>


                </div>

                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 所属公司 , ownCompanyId ,typeName=button, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('所属公司')}" class="search-label ownCompanyId-label">所属公司</span><span class="search-colon">:</span></div>
                        <input lay-filter="ownCompanyId" id="ownCompanyId" name="ownCompanyId"  type="hidden" class="layui-input"   />
                        <button id="ownCompanyId-button" type="button" action-type="org-dialog" class="layui-btn layui-btn-primary   " style="width:150px"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择公司')}" th:default-label="${lang.translate('请选择公司')}">按钮文本</span></button>
                    </div>
                    <!-- 使用公司/部门 , useOrganizationId ,typeName=button, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('使用部门')}" class="search-label useOrganizationId-label">使用公司/部门</span><span class="search-colon">:</span></div>
                        <input lay-filter="useOrganizationId" id="useOrganizationId" name="useOrganizationId"  type="hidden" class="layui-input"   />
                        <button id="useOrganizationId-button" type="button" action-type="org-dialog" class="layui-btn layui-btn-primary   " style="width:150px"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择组织节点')}" th:default-label="${lang.translate('请选择组织节点')}">按钮文本</span></button>
                    </div>
                    <!-- 管理人员 , managerId ,typeName=button, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('管理人员')}" class="search-label managerId-label">管理人员</span><span class="search-colon">:</span></div>
                        <input lay-filter="managerId" id="managerId" name="managerId"  type="hidden" class="layui-input"   />
                        <button id="managerId-button" type="button" action-type="emp-dialog" class="layui-btn layui-btn-primary   " style="width:150px"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择人员')}" th:default-label="${lang.translate('请选择人员')}">按钮文本</span></button>
                    </div>
                    <!-- 使用人员 , useUserId ,typeName=button, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:70px"><span th:text="${lang.translate('使用人员')}" class="search-label useUserId-label">使用人员</span><span class="search-colon">:</span></div>
                        <input lay-filter="useUserId" id="useUserId" name="useUserId"  type="hidden" class="layui-input"   />
                        <button id="useUserId-button" type="button" action-type="emp-dialog" class="layui-btn layui-btn-primary   " style="width:150px"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择人员')}" th:default-label="${lang.translate('请选择人员')}">按钮文本</span></button>
                    </div>


                </div>
            </div>



            <!-- 按钮区域 -->
            <div class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索')}">搜索</span></button>
                <button id="search-button-advance" class="layui-btn layui-btn-primary icon-btn search-button-advance"><i class="layui-icon">&#xe671;</i><span th:text="${lang.translate('更多')}">更多</span></button>
            </div>
        </div>

        <div style="margin-top: 84px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>

<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">

    <button th:if="${perm.checkAuth('eam_asset:view-form')}" class="layui-btn layui-btn-primary layui-btn-xs ops-view-button" lay-event="view" th:text="${lang.translate('查看')}" data-id="{{d.id}}">查看</button>
<!--    <button class="layui-btn layui-btn-xs " lay-event="asset-data-change" th:text="${lang.translate('历史记录')}" data-id="{{d.id}}">历史记录</button>-->

</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];

    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetHandleStatusEnum')}]];
    var SELECT_ASSETSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetStatusEnum')}]];
    var SELECT_EQUIPMENTSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetEquipmentStatusEnum')}]];

    var AUTH_PREFIX="eam_asset_5";

    var OWNER_CODE="asset";
    var ATTRIBUTE_LIST_DATA =  [[${attributeListData}]];

</script>
<script th:src="'/business/eam/asset/asset_column_list.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/asset/asset_search/asset_wait_clean_search_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/asset/asset_search/asset_wait_clean_search.js?'+${cacheKey}"></script>
</body>
</html>
