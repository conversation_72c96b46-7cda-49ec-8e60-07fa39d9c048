<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>camunda-connect-root</artifactId>
    <groupId>org.camunda.connect</groupId>
    <version>1.5.2</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>camunda-connect-connectors-all</artifactId>
  <name>camunda BPM - connect - all connectors in one</name>
  <build>
    <plugins>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <createDependencyReducedPom>true</createDependencyReducedPom>
              <createSourcesJar>true</createSourcesJar>
              <promoteTransitiveDependencies>true</promoteTransitiveDependencies>
              <artifactSet>
                <excludes>
                  <exclude>org.camunda.commons:*</exclude>
                  <exclude>org.camunda.connect:camunda-connect-core</exclude>
                  <exclude>org.slf4j:*</exclude>
                </excludes>
              </artifactSet>
              <relocations>
                <relocation>
                  <pattern>org.apache</pattern>
                  <shadedPattern>connectjar.org.apache</shadedPattern>
                </relocation>
              </relocations>
              <transformers>
                <transformer />
              </transformers>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <configuration>
          <includeDependencySources>true</includeDependencySources>
          <includeTransitiveDependencySources>true</includeTransitiveDependencySources>
          <dependencySourceIncludes>
            <include>org.camunda.connect:*</include>
          </dependencySourceIncludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <executions>
          <execution>
            <id>bundle-manifest</id>
            <phase>process-classes</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <unpackBundle>false</unpackBundle>
          <instructions>
            <Import-Package>!connectjar*,
              javax.*;resolution:=optional,
              org.camunda.commons.logging</Import-Package>
            <Bundle-ClassPath>.</Bundle-ClassPath>
            <Embed-Dependency>*;inline=true</Embed-Dependency>
            <Embed-Transitive>true</Embed-Transitive>
          </instructions>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <dependencies>
    <dependency>
      <groupId>org.camunda.connect</groupId>
      <artifactId>camunda-connect-core</artifactId>
      <version>1.5.2</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.camunda.commons</groupId>
      <artifactId>camunda-commons-logging</artifactId>
      <version>1.9.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.26</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.camunda.commons</groupId>
      <artifactId>camunda-commons-utils</artifactId>
      <version>1.9.0</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
  <properties>
    <skip-third-party-bom>false</skip-third-party-bom>
  </properties>
</project>
