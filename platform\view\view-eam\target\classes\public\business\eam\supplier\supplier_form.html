<!--
/**
 * 供应商 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2022-07-30 08:31:36
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
	<title th:text="${lang.translate('供应商')}">供应商</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="display:none">

        <input name="id" id="id"  type="hidden"/>

         <!--开始：group 循环-->



        <div class="layui-row form-row" id="random-7854-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('名称')}">名称</div><div class="layui-required">*</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="supplierName" id="supplierName" name="supplierName" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('名称') }" type="text" class="layui-input"    lay-verify="|required"  />
                        </div>
                    </div>

                                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('编码')}">编码</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="code" id="code" name="code" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('编码') }" type="text" class="layui-input"  />
                        </div>
                    </div>

                                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('统一社会信用代码')}">统一社会信用代码</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="unitCode" id="unitCode" name="unitCode" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('统一社会信用代码') }" type="text" class="layui-input"  />
                        </div>
                    </div>

                <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >


                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('评级')}">评级</div><div class="layui-required">*</div></div>
                        <div class="layui-input-block ">
                            <div id="grade" input-type="select" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_supplier_grade'}" extraParam="{}"></div>
                        </div>
                    </div>


                                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('商务联系人')}">商务联系人</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="businessContacts" id="businessContacts" name="businessContacts" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('商务联系人') }" type="text" class="layui-input"  />
                        </div>
                    </div>

                                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('商务联系方式')}">商务联系方式</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="businessContactsInfo" id="businessContactsInfo" name="businessContactsInfo" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('商务联系方式') }" type="text" class="layui-input"  />
                        </div>
                    </div>

                <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('售后联系人')}">售后联系人</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="afterSalesContacts" id="afterSalesContacts" name="afterSalesContacts" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('售后联系人') }" type="text" class="layui-input"  />
                        </div>
                    </div>

                                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('售后联系方式')}">售后联系方式</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="afterSalesContactsInfo" id="afterSalesContactsInfo" name="afterSalesContactsInfo" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('售后联系方式') }" type="text" class="layui-input"  />
                        </div>
                    </div>

                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->



        <div class="layui-row form-row" id="random-3260-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column"  style="padding-top: 0px" >


                    <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('地址')}">地址</div></div>
                    <div class="layui-input-block ">
                        <textarea lay-filter="address" id="address" name="address" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('地址') }" class="layui-textarea" style="height: 120px" ></textarea>
                    </div>
                </div>



                    <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('备注')}">备注</div></div>
                    <div class="layui-input-block ">
                        <textarea lay-filter="supplierNotes" id="supplierNotes" name="supplierNotes" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('备注') }" class="layui-textarea" style="height: 120px" ></textarea>
                    </div>
                </div>


                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 20px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('eam_supplier:create','eam_supplier:update','eam_supplier:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var VALIDATE_CONFIG={"supplierName":{"labelInForm":"名称","inputType":"text_input","required":true},"grade":{"labelInForm":"评级","inputType":"select_box","required":true}};
    var AUTH_PREFIX="eam_supplier";


</script>



<script th:src="'/business/eam/supplier/supplier_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/supplier/supplier_form.js?'+${cacheKey}"></script>

</body>
</html>
