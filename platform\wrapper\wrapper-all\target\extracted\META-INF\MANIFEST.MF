Manifest-Version: 1.0
Implementation-Title: wrapper-all
Implementation-Version: *******
Built-By: LWT&YFDYF
Implementation-Vendor-Id: com.dt.platform
Class-Path: lib/service-storage-1.8.0.RELEASE.jar lib/framework-boot-1
 .8.0.RELEASE.jar lib/foxnic-commons-1.8.0.RELEASE.jar lib/javaparser-
 symbol-solver-core-3.24.4.jar lib/javaparser-core-3.24.4.jar lib/java
 ssist-3.29.0-GA.jar lib/httpclient-4.5.13.jar lib/httpcore-4.4.14.jar
  lib/commons-pool2-2.8.1.jar lib/jedis-3.3.0.jar lib/reflectasm-1.11.
 9.jar lib/spring-jms-5.2.15.RELEASE.jar lib/spring-messaging-5.2.15.R
 ELEASE.jar lib/sentinel-core-1.8.0.jar lib/easy-captcha-1.6.2.jar lib
 /spring-boot-starter-data-redis-2.3.12.RELEASE.jar lib/lettuce-core-5
 .3.7.RELEASE.jar lib/netty-common-4.1.65.Final.jar lib/netty-handler-
 4.1.65.Final.jar lib/netty-resolver-4.1.65.Final.jar lib/netty-buffer
 -4.1.65.Final.jar lib/netty-codec-4.1.65.Final.jar lib/netty-transpor
 t-4.1.65.Final.jar lib/reactor-core-3.3.17.RELEASE.jar lib/reactive-s
 treams-1.0.3.jar lib/druid-spring-boot-starter-1.2.14.jar lib/druid-1
 .2.14.jar lib/domain-1.8.0.RELEASE.jar lib/foxnic-api-1.8.0.RELEASE.j
 ar lib/proxy-1.8.0.RELEASE.jar lib/javax.servlet-api-4.0.1.jar lib/al
 iyun-sdk-oss-3.16.1.jar lib/jdom2-2.0.6.jar lib/jettison-1.5.2.jar li
 b/aliyun-java-sdk-core-4.5.10.jar lib/gson-2.8.7.jar lib/jaxb-api-2.3
 .1.jar lib/javax.activation-api-1.2.0.jar lib/org.jacoco.agent-0.8.5-
 runtime.jar lib/ini4j-0.5.4.jar lib/opentracing-api-0.33.0.jar lib/op
 entracing-util-0.33.0.jar lib/opentracing-noop-0.33.0.jar lib/aliyun-
 java-sdk-ram-3.1.0.jar lib/aliyun-java-sdk-kms-2.11.0.jar lib/service
 -system-1.8.0.RELEASE.jar lib/service-oauth-1.8.0.RELEASE.jar lib/spr
 ing-boot-starter-security-2.3.12.RELEASE.jar lib/spring-aop-5.2.15.RE
 LEASE.jar lib/spring-security-config-5.3.9.RELEASE.jar lib/spring-sec
 urity-web-5.3.9.RELEASE.jar lib/spring-security-oauth2-2.3.8.RELEASE.
 jar lib/spring-beans-5.2.15.RELEASE.jar lib/spring-core-5.2.15.RELEAS
 E.jar lib/spring-context-5.2.15.RELEASE.jar lib/spring-security-core-
 5.3.9.RELEASE.jar lib/jackson-mapper-asl-1.9.13.jar lib/jackson-core-
 asl-1.9.13.jar lib/spring-security-jwt-1.1.1.RELEASE.jar lib/bcpkix-j
 dk15on-1.64.jar lib/bcprov-jdk15on-1.64.jar lib/spring-security-tagli
 bs-5.3.9.RELEASE.jar lib/service-hrm-1.8.0.RELEASE.jar lib/service-pc
 m-1.8.0.RELEASE.jar lib/cangariza-1.8.0.RELEASE.jar lib/cangaroo-1.8.
 0.RELEASE.jar lib/service-changes-1.8.0.RELEASE.jar lib/service-bpm-1
 .8.0.RELEASE.jar lib/camunda-bpmn-model-7.17.0.jar lib/camunda-xml-mo
 del-7.17.0.jar lib/service-job-1.8.0.RELEASE.jar lib/quartz-2.3.2.jar
  lib/mchange-commons-java-0.2.15.jar lib/slf4j-api-1.7.30.jar lib/ser
 vice-dataperm-1.8.0.RELEASE.jar lib/service-docs-1.8.0.RELEASE.jar li
 b/view-docs-1.8.0.RELEASE.jar lib/framework-view-1.8.0.RELEASE.jar li
 b/spring-boot-starter-thymeleaf-2.3.12.RELEASE.jar lib/thymeleaf-spri
 ng5-3.0.12.RELEASE.jar lib/thymeleaf-3.0.12.RELEASE.jar lib/attoparse
 r-2.0.5.RELEASE.jar lib/unbescape-1.1.6.RELEASE.jar lib/thymeleaf-ext
 ras-java8time-3.0.4.RELEASE.jar lib/UserAgentUtils-1.21.jar lib/view-
 dataperm-1.8.0.RELEASE.jar lib/view-pcm-1.8.0.RELEASE.jar lib/view-ch
 anges-1.8.0.RELEASE.jar lib/view-console-1.8.0.RELEASE.jar lib/view-b
 pm-1.8.0.RELEASE.jar lib/domain-eam-*******.jar lib/foxnic-sql-1.8.0.
 RELEASE.jar lib/swagger-annotations-1.5.22.jar lib/hibernate-jpa-2.1-
 api-1.0.2.Final.jar lib/enjoy-4.9.08.jar lib/contract-domain-1.0.0.2.
 jar lib/domain-web-*******.jar lib/knife4j-micro-spring-boot-starter-
 3.0.2.jar lib/knife4j-spring-boot-autoconfigure-3.0.2.jar lib/knife4j
 -spring-3.0.2.jar lib/knife4j-annotations-3.0.2.jar lib/swagger-annot
 ations-2.1.2.jar lib/knife4j-core-3.0.2.jar lib/springfox-swagger2-3.
 0.0.jar lib/springfox-spi-3.0.0.jar lib/springfox-schema-3.0.0.jar li
 b/springfox-swagger-common-3.0.0.jar lib/springfox-spring-web-3.0.0.j
 ar lib/classgraph-4.8.83.jar lib/springfox-spring-webflux-3.0.0.jar l
 ib/mapstruct-1.3.1.Final.jar lib/springfox-spring-webmvc-3.0.0.jar li
 b/springfox-core-3.0.0.jar lib/springfox-oas-3.0.0.jar lib/swagger-mo
 dels-2.1.2.jar lib/springfox-bean-validators-3.0.0.jar lib/swagger-mo
 dels-1.5.22.jar lib/swagger-core-1.5.22.jar lib/jackson-dataformat-ya
 ml-2.11.4.jar lib/springfox-boot-starter-3.0.0.jar lib/springfox-data
 -rest-3.0.0.jar lib/classmate-1.5.1.jar lib/spring-plugin-core-2.0.0.
 RELEASE.jar lib/spring-plugin-metadata-2.0.0.RELEASE.jar lib/framewor
 k-eam-*******.jar lib/foxnic-dao-1.8.0.RELEASE.jar lib/foxnic-springb
 oot-1.8.0.RELEASE.jar lib/spring-boot-starter-jdbc-2.3.12.RELEASE.jar
  lib/HikariCP-3.4.5.jar lib/mysql-connector-java-5.1.49.jar lib/xmlbe
 ans-3.0.2.jar lib/framework-oa-*******.jar lib/domain-oa-*******.jar 
 lib/customer-domain-1.0.0.2.jar lib/framework-web-*******.jar lib/pro
 xy-eam-*******.jar lib/contract-proxy-1.0.0.2.jar lib/customer-proxy-
 1.0.0.2.jar lib/spring-cloud-openfeign-core-2.2.0.RELEASE.jar lib/spr
 ing-boot-autoconfigure-2.3.12.RELEASE.jar lib/spring-cloud-netflix-ri
 bbon-2.2.0.RELEASE.jar lib/spring-cloud-netflix-archaius-2.2.0.RELEAS
 E.jar lib/spring-boot-starter-aop-2.3.12.RELEASE.jar lib/aspectjweave
 r-1.9.6.jar lib/feign-form-spring-3.8.0.jar lib/feign-form-3.8.0.jar 
 lib/feign-core-10.4.0.jar lib/service-job-web-*******.jar lib/proxy-w
 eb-*******.jar lib/service-common-*******.jar lib/snakeyaml-1.26.jar 
 lib/liteflow-spring-boot-starter-********.jar lib/liteflow-spring-2.1
 2.2.1.jar lib/liteflow-core-********.jar lib/dom4j-2.1.4.jar lib/tran
 smittable-thread-local-2.12.3.jar lib/byte-buddy-1.10.22.jar lib/QLEx
 press-3.3.2.jar lib/liteflow-rule-sql-********.jar lib/hutool-crypto-
 5.8.26.jar lib/hutool-core-5.8.26.jar lib/liteflow-script-javascript-
 ********.jar lib/liteflow-script-java-********.jar lib/janino-3.1.4.j
 ar lib/commons-compiler-3.1.4.jar lib/magic-api-plugin-task-2.2.0.jar
  lib/easypoi-base-4.4.0.jar lib/commons-lang3-3.10.jar lib/ognl-3.2.6
 .jar lib/validation-api-2.0.1.Final.jar lib/easypoi-annotation-4.4.0.
 jar lib/oshi-core-6.4.0.jar lib/jna-5.12.1.jar lib/jna-platform-5.12.
 1.jar lib/ureport2-console-2.2.9.jar lib/commons-fileupload-1.3.2.jar
  lib/velocity-1.7.jar lib/commons-collections-3.2.1.jar lib/commons-l
 ang-2.4.jar lib/ureport2-core-2.2.9.jar lib/commons-beanutils-core-1.
 8.3.jar lib/antlr4-runtime-4.5.3.jar lib/dom4j-1.6.1.jar lib/xml-apis
 -1.0.b2.jar lib/itextpdf-5.5.13.jar lib/poi-scratchpad-3.16.jar lib/u
 report2-font-2.0.1.jar lib/commons-io-2.15.1.jar lib/service-mobile-2
 .1.0.0.jar lib/view-console-*******.jar lib/view-common-*******.jar l
 ib/view-mobile-*******.jar lib/service-oa-*******.jar lib/proxy-oa-1.
 1.0.0.jar lib/view-oa-*******.jar lib/service-job-eam-*******.jar lib
 /view-eam-*******.jar lib/service-eam-*******.jar lib/commons-jexl3-3
 .2.1.jar lib/commons-logging-1.2.jar lib/barcodes-7.2.4.jar lib/font-
 asian-7.2.4.jar lib/forms-7.2.4.jar lib/hyph-7.2.4.jar lib/io-7.2.4.j
 ar lib/commons-7.2.4.jar lib/kernel-7.2.4.jar lib/layout-7.2.4.jar li
 b/pdfa-7.2.4.jar lib/sign-7.2.4.jar lib/styled-xml-parser-7.2.4.jar l
 ib/svg-7.2.4.jar lib/javase-3.3.0.jar lib/core-3.3.0.jar lib/jcommand
 er-1.48.jar lib/jai-imageio-core-1.3.1.jar lib/poi-tl-1.9.1.jar lib/p
 oi-4.1.2.jar lib/commons-collections4-4.4.jar lib/commons-math3-3.6.1
 .jar lib/SparseBitSet-1.2.jar lib/poi-ooxml-4.1.2.jar lib/curvesapi-1
 .06.jar lib/poi-ooxml-schemas-4.1.2.jar lib/domain-ops-*******.jar li
 b/magic-api-spring-boot-starter-1.6.0.jar lib/spring-jdbc-5.2.15.RELE
 ASE.jar lib/spring-tx-5.2.15.RELEASE.jar lib/magic-api-1.6.0.jar lib/
 spring-boot-starter-websocket-2.3.12.RELEASE.jar lib/spring-websocket
 -5.2.15.RELEASE.jar lib/commons-text-1.6.jar lib/commons-beanutils-1.
 9.4.jar lib/magic-script-1.5.10.jar lib/magic-editor-1.6.0.jar lib/sp
 ring-boot-starter-web-2.3.12.RELEASE.jar lib/spring-boot-starter-2.3.
 12.RELEASE.jar lib/spring-boot-2.3.12.RELEASE.jar lib/spring-boot-sta
 rter-logging-2.3.12.RELEASE.jar lib/logback-classic-1.2.3.jar lib/log
 back-core-1.2.3.jar lib/jul-to-slf4j-1.7.30.jar lib/jakarta.annotatio
 n-api-1.3.5.jar lib/spring-boot-starter-json-2.3.12.RELEASE.jar lib/j
 ackson-datatype-jdk8-2.11.4.jar lib/jackson-datatype-jsr310-2.11.4.ja
 r lib/jackson-module-parameter-names-2.11.4.jar lib/spring-web-5.2.15
 .RELEASE.jar lib/spring-webmvc-5.2.15.RELEASE.jar lib/spring-expressi
 on-5.2.15.RELEASE.jar lib/spring-boot-starter-undertow-2.3.12.RELEASE
 .jar lib/undertow-core-2.1.7.Final.jar lib/jboss-logging-3.4.2.Final.
 jar lib/xnio-api-3.8.0.Final.jar lib/wildfly-common-1.5.2.Final.jar l
 ib/wildfly-client-config-1.0.1.Final.jar lib/xnio-nio-3.8.0.Final.jar
  lib/jboss-threads-3.1.0.Final.jar lib/undertow-servlet-2.1.7.Final.j
 ar lib/jboss-annotations-api_1.3_spec-2.0.1.Final.jar lib/undertow-we
 bsockets-jsr-2.1.7.Final.jar lib/jboss-websocket-api_1.1_spec-2.0.0.F
 inal.jar lib/jakarta.servlet-api-4.0.4.jar lib/jakarta.el-3.0.3.jar l
 ib/spring-session-data-redis-2.3.3.RELEASE.jar lib/spring-data-redis-
 2.3.9.RELEASE.jar lib/spring-data-keyvalue-2.3.9.RELEASE.jar lib/spri
 ng-data-commons-2.3.9.RELEASE.jar lib/spring-oxm-5.2.15.RELEASE.jar l
 ib/spring-context-support-5.2.15.RELEASE.jar lib/spring-session-core-
 2.3.3.RELEASE.jar lib/spring-jcl-5.2.15.RELEASE.jar lib/minio-8.5.11.
 jar lib/simple-xml-safe-2.7.1.jar lib/guava-33.0.0-jre.jar lib/failur
 eaccess-1.0.2.jar lib/listenablefuture-9999.0-empty-to-avoid-conflict
 -with-guava.jar lib/jsr305-3.0.2.jar lib/j2objc-annotations-2.8.jar l
 ib/jackson-annotations-2.11.4.jar lib/jackson-core-2.11.4.jar lib/jac
 kson-databind-2.11.4.jar lib/bcprov-jdk18on-1.78.jar lib/commons-comp
 ress-1.26.0.jar lib/commons-codec-1.14.jar lib/snappy-java-1.1.10.5.j
 ar lib/okhttp-4.9.0.jar lib/okio-2.8.0.jar lib/kotlin-stdlib-common-1
 .3.72.jar lib/kotlin-stdlib-1.3.72.jar lib/annotations-13.0.jar lib/c
 affeine-2.8.8.jar lib/checker-qual-3.8.0.jar lib/error_prone_annotati
 ons-2.4.0.jar lib/fastjson-1.2.83.jar
Spring-Boot-Version: 2.3.12.RELEASE
Main-Class: org.springframework.boot.loader.JarLauncher
Spring-Boot-Classpath-Index: BOOT-INF/classpath.idx
Start-Class: com.dt.platform.wrapper.WrapperAllApp
Spring-Boot-Classes: BOOT-INF/classes/
Spring-Boot-Lib: BOOT-INF/lib/
Created-By: Apache Maven 3.6.3
Build-Jdk: 1.8.0_442
Implementation-URL: https://spring.io/projects/spring-boot/parent-eam/
 ./wrapper/wrapper-all

