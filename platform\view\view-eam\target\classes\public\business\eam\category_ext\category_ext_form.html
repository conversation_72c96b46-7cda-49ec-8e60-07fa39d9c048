<!--
/**
 * 资产分类扩展 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2025-02-15 19:13:27
 */
 -->
<!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
    <title th:text="${lang.translate('资产分类扩展')}">资产分类扩展</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <div class="layui-form toolbar" style="padding:6px; border-bottom:1px #e6e6e6 solid; " id="toolbar">
        <table><tr>
            <td>
                <button id="catalog-list" style="margin-right:20px;" class="layui-btn icon-btn layui-btn-sm" lay-event="catalogList"> <span>分类列表</span></button>
            </td>
            <td>
                <button id="catalog-flush" style="margin-right:20px;" class="layui-btn icon-btn layui-btn-sm" lay-event="catalogFlush"> <span>刷新</span></button>
            </td>
        </tr>
        </table>
    </div>

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="opacity:0">

        <input name="id" id="id"  type="hidden"/>

        <!--开始：group 循环-->

        <div class="layui-row form-row" id="random-8542-content">

            <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column" >

                <!-- text_input : 分类名称 ,  pcmCatalogName -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('分类名称')}">分类名称</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="pcmCatalogName" id="pcmCatalogName" name="pcmCatalogName" th:placeholder="${ lang.translate('请输入'+'分类名称') }" type="text" class="layui-input"  />
                    </div>
                </div>

                <!-- text_input : 分类编码 ,  pcmCatalogCode -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('分类编码')}">分类编码</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="pcmCatalogCode" id="pcmCatalogCode" name="pcmCatalogCode" th:placeholder="${ lang.translate('请输入'+'分类编码') }" type="text" class="layui-input"  />
                    </div>
                </div>

                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('资产价值')}">资产价值</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <div id="costType" input-type="select" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_goods_cost_type'}" extraParam="{}"></div>
                    </div>
                </div>


                <!-- number_input : 使用周期 ,  lifeCycle  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('使用周期(月)')}">使用周期(月)</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="lifeCycle" id="lifeCycle" name="lifeCycle" th:placeholder="${ lang.translate('请输入'+'使用周期') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="2" />
                    </div>
                </div>

                <!-- text_input : 计量单位 ,  unit -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('计量单位')}">计量单位</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="unit" id="unit" name="unit" th:placeholder="${ lang.translate('请输入'+'计量单位') }" type="text" class="layui-input"  />
                    </div>
                </div>

                <!-- number_input : 残值率 ,  residualsRate  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('残值率')}">残值率</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="residualsRate" id="residualsRate" name="residualsRate" th:placeholder="${ lang.translate('请输入'+'残值率') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="0" />
                    </div>
                </div>

                <!-- text_input : 标签 ,  label -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('标签')}">标签</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <input lay-filter="label" id="label" name="label" th:placeholder="${ lang.translate('请输入'+'标签') }" type="text" class="layui-input"  />
                    </div>
                </div>

                <!-- text_area : 备注 ,  notes  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('备注')}">备注</div></div>
                    <div class="layui-input-block layui-input-block-c1">
                        <textarea lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入'+'备注') }" class="layui-textarea" style="height: 80px" ></textarea>
                    </div>
                </div>
                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 50px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button  class="layui-btn" style="margin-right: 15px;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存','','form.button')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var VALIDATE_CONFIG={};
    var AUTH_PREFIX="eam_category_ext";


</script>



<script th:src="'/business/eam/category_ext/category_ext_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/category_ext/category_ext_form.js?'+${cacheKey}"></script>

</body>
</html>