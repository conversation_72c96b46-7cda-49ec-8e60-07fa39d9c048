<!--
/**
 * 维修验收 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2024-03-25 10:15:02
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
	<title th:text="${lang.translate('维修验收')}">维修验收</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="opacity:0">

        <input name="id" id="id"  type="hidden"/>

         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-3184-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >

                <!-- radio_box : 验收结果 ,  acceptResult  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('验收结果')}">验收结果</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input input-type="radio" type="radio" name="acceptResult" lay-filter="acceptResult" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.eam.RepairOrderActAcceptStatusEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==0)}">
                    </div>
                </div>
            
                <!-- select_box : 维修结果 ,  resultType  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('维修结果')}">维修结果</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="resultType" input-type="select" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_repair_result_type'}" extraParam="{}"></div>
                    </div>
                </div>
            
                <!-- select_box : 实际故障 ,  categoryTplId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('实际故障')}">实际故障</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="categoryTplId" input-type="select" th:data="${'/service-eam/eam-repair-category-tpl/query-list'}" extraParam="{}"></div>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >

                <!-- button : 验收人员 ,  accepterId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('验收人员')}">验收人员</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="accepterId" id="accepterId" name="accepterId"  type="hidden" class="layui-input"    lay-verify="|required"   />
                        <button id="accepterId-button" type="button" action-type="emp-dialog" class="layui-btn   " style="width: 100%" default-width="100%" auto-width="false"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择人员')}" th:default-label="${lang.translate('请选择人员')}">按钮文本</span></button>
                    </div>
                </div>
            
                <!-- date_input : 完成时间 ,  finishTime  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('完成时间')}">完成时间</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input input-type="date" lay-filter="finishTime" id="finishTime" name="finishTime"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择'+'完成时间') }" type="text" class="layui-input"    lay-verify="|required"   />
                    </div>
                </div>
            
                <!-- number_input : 实际花费 ,  actualCost  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('实际花费')}">实际花费</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="actualCost" id="actualCost" name="actualCost" th:placeholder="${ lang.translate('请输入'+'实际花费') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="0"  value="0.0" />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-9900-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column"  style="padding-top: 0px" >

                <!-- text_area : 验收备注 ,  notes  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('验收备注')}">验收备注</div></div>
                    <div class="layui-input-block ">
                        <textarea lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入'+'验收备注') }" class="layui-textarea" style="height: 135px" ></textarea>
                    </div>
                </div>
                            <!-- upload : 图片 ,  pictureId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('图片')}">图片</div></div>
                    <div class="layui-upload layui-input-block ">
                        <input input-type="upload" id="pictureId"  name="pictureId" lay-filter="pictureId" style="display: none">
                        <button type="button" class="layui-btn" id="pictureId-button" th:text="${lang.translate('图片')}">图片</button>
                        <div class="layui-upload-list" id="pictureId-file-list"></div>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 200px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消','','form.button')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('eam_repair_order_acceptance:create','eam_repair_order_acceptance:update','eam_repair_order_acceptance:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存','','form.button')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var RADIO_ACCEPTRESULT_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.RepairOrderActAcceptStatusEnum')}]];
    var VALIDATE_CONFIG={"finishTime":{"date":true,"labelInForm":"完成时间","inputType":"date_input","required":true},"acceptResult":{"labelInForm":"验收结果","inputType":"radio_box","required":true},"accepterId":{"labelInForm":"验收人员","inputType":"button","required":true},"categoryTplId":{"labelInForm":"实际故障","inputType":"select_box","required":true},"resultType":{"labelInForm":"维修结果","inputType":"select_box","required":true}};
    var AUTH_PREFIX="eam_repair_order_acceptance";

    // curEmpId
    var CUR_EMP_ID = [[${curEmpId}]] ;
    // curUserName
    var CUR_USER_NAME = [[${curUserName}]] ;
    // 工单
    var ORDER_ACT_ID = [[${orderActId}]] ;

</script>



<script th:src="'/business/eam/repair_order_acceptance/repair_order_acceptance_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/repair_order_acceptance/repair_order_acceptance_form.js?'+${cacheKey}"></script>

</body>
</html>