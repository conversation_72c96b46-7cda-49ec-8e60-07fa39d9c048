<!--
/**
 * 产品管理 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2024-04-08 07:38:39
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
    <title th:text="${lang.translate('产品管理')}">产品管理</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 产品名称 , name ,typeName=text_input, isHideInSearch=true -->
                    <!-- 品类 , categoryId ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('品类')}" class="search-label categoryId-label">品类</span><span class="search-colon">:</span></div>
                        <div id="categoryId" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_iot_product_category'}" style="width:180px" extraParam="{}"></div>
                    </div>
                    <!-- 透传设备 , productTransparent ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 定位方式 , locationMethod ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 图片 , imageId ,typeName=upload, isHideInSearch=true -->
                    <!-- 备注 , notes ,typeName=text_input, isHideInSearch=true -->
                    <!-- 修改人ID , updateBy ,typeName=text_input, isHideInSearch=true -->
                    <!-- 节点类型 , productType ,typeName=radio_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('节点类型')}" class="search-label productType-label">节点类型</span><span class="search-colon">:</span></div>


                        <div id="productType" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.IotNodeTypeEnum')}" style="width:180px"></div>
                    </div>
                    <!-- 设备定位 , locationStatus ,typeName=radio_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('设备定位')}" class="search-label locationStatus-label">设备定位</span><span class="search-colon">:</span></div>


                        <div id="locationStatus" th:data="${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}" style="width:180px"></div>
                    </div>
                    <!-- 产品Key , productKey ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('产品Key')}" class="search-label productKey-label">产品Key</span><span class="search-colon">:</span></div>
                        <input id="productKey" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>


                </div>
            </div>


            <!-- 按钮区域 -->
            <div id="search-area" class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索','','cmp:table.search')}">搜索</span></button>
            </div>
        </div>

        <div id="table-area" style="margin-top: 42px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
        <button th:if="${perm.checkAuth('iot_product:create')}" id="add-button" class="layui-btn icon-btn layui-btn-sm create-new-button " lay-event="create"><i class="layui-icon">&#xe654;</i><span th:text="${lang.translate('新建','','cmp:table.button')}">新建</span></button>
        <button th:if="${perm.checkAuth('iot_product:delete-by-ids')}" id="delete-button" class="layui-btn icon-btn layui-btn-danger layui-btn-sm batch-delete-button " lay-event="batch-del"><i class="layui-icon">&#xe67e;</i><span th:text="${lang.translate('删除','','cmp:table.button')}">删除</span></button>
    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">

    <button th:if="${perm.checkAuth('iot_product:view-form')}" class="layui-btn layui-btn-primary layui-btn-xs ops-view-button " lay-event="view"  data-id="{{d.id}}"> <span th:text="${lang.translate('查看','','cmp:table.ops')}">查看</span></button>
    <button th:if="${perm.checkAnyAuth('iot_product:update','iot_product:save')}" class="layui-btn layui-btn-primary layui-btn-xs ops-edit-button " lay-event="edit"data-id="{{d.id}}"><span th:text="${lang.translate('修改','','cmp:table.ops')}">修改</span></button>


    <button th:if="${perm.checkAuth('iot_product:delete')}" class="layui-btn layui-btn-xs layui-btn-danger ops-delete-button " lay-event="del" data-id="{{d.id}}"><span th:text="${lang.translate('删除','','cmp:table.ops')}">删除</span></button>

    <button class="layui-btn layui-btn-xs  obj-model " lay-event="obj-model" data-id="{{d.id}}"><span th:text="${lang.translate('物模型','','cmp:table.ops')}">物模型</span></button>

</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
    var RADIO_PRODUCTTYPE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.IotNodeTypeEnum')}]];
    var RADIO_PRODUCTTRANSPARENT_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.YesNoEnum')}]];
    var RADIO_LOCATIONSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var RADIO_LOCATIONMETHOD_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.IotLocationMethodEnum')}]];
    var AUTH_PREFIX="iot_product";


</script>

<script th:src="'/business/eam/product/product_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/product/product_list.js?'+${cacheKey}"></script>

</body>
</html>