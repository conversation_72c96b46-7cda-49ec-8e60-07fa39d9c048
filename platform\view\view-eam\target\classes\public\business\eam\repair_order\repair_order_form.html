<!--
/**
 * 故障申请单 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2024-03-25 13:24:22
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
	<title th:text="${lang.translate('故障申请单')}">故障申请单</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    .form-column {
        padding-top: 0px;
        padding-right: 0px;
        padding-left: 4px;
        display: inline-block;
    }
    .model-form {
        padding-top: 0px;
        margin-right: 0px;
        margin-left: 4px;
    }
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="opacity:0">

        <input name="id" id="id"  type="hidden"/>

         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-5468-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                <!-- button : 报修部门 ,  reportOrgId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('报修部门')}">报修部门</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="reportOrgId" id="reportOrgId" name="reportOrgId"  type="hidden" class="layui-input"   />
                        <button id="reportOrgId-button" type="button" action-type="org-dialog" class="layui-btn   " style="width: 100%" default-width="100%" auto-width="false"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择组织节点')}" th:default-label="${lang.translate('请选择组织节点')}">按钮文本</span></button>
                    </div>
                </div>
            
                <!-- button : 报修人员 ,  reportUserId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('报修人员')}">报修人员</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="reportUserId" id="reportUserId" name="reportUserId"  type="hidden" class="layui-input"    lay-verify="|required"   />
                        <button id="reportUserId-button" type="button" action-type="emp-dialog" class="layui-btn   " style="width: 100%" default-width="100%" auto-width="false"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择人员')}" th:default-label="${lang.translate('请选择人员')}">按钮文本</span></button>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                <!-- radio_box : 维修类型 ,  repairType  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('维修类型')}">维修类型</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input input-type="radio" type="radio" name="repairType" lay-filter="repairType" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.eam.RepairOrderTypeEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==0)}">
                    </div>
                </div>
            
                <!-- select_box : 故障类型 ,  categoryTplId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('故障类型')}">故障类型</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="categoryTplId" input-type="select" th:data="${'/service-eam/eam-repair-category-tpl/query-paged-list'}" extraParam="{}"></div>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs4 form-column" >

                <!-- select_box : 紧急程度 ,  urgencyId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('紧急程度')}">紧急程度</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="urgencyId" input-type="select" th:data="${'/service-eam/eam-repair-urgency/query-paged-list'}" extraParam="{}"></div>
                    </div>
                </div>
            
                <!-- date_input : 计划完成日期 ,  planFinishDate  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('计划完成日期')}">计划完成日期</div></div>
                    <div class="layui-input-block ">
                        <input input-type="date" lay-filter="planFinishDate" id="planFinishDate" name="planFinishDate"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择'+'计划完成日期') }" type="text" class="layui-input"    lay-verify=""   />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-3287-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column"  style="padding-top: 0px" >

                <!-- text_area : 报修内容 ,  content  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('报修内容')}">报修内容</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <textarea lay-filter="content" id="content" name="content" th:placeholder="${ lang.translate('请输入'+'报修内容') }" class="layui-textarea" style="height: 120px" ></textarea>
                    </div>
                </div>
                            <!-- upload : 图片 ,  pictureId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('图片')}">图片</div></div>
                    <div class="layui-upload layui-input-block ">
                        <input input-type="upload" id="pictureId"  name="pictureId" lay-filter="pictureId" style="display: none">
                        <button type="button" class="layui-btn" id="pictureId-button" th:text="${lang.translate('选择图片')}">选择图片</button>
                        <div class="layui-upload-list" id="pictureId-file-list"></div>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->
        <fieldset class="layui-elem-field layui-field-title form-group-title" id="random-4953-fieldset">
        <legend>设备信息</legend>
        </fieldset>
        <div class="layui-row form-row" style="text-align: center;" id="random-4953-content">
            <div style="display: inline-block;padding-right: 8px;padding-left: 8px" class="layui-col-xs12">
            <iframe id="random-4953-iframe" js-fn="assetSelectList" class="form-iframe" frameborder="0" style="width: 100%"></iframe>
            </div>
        </div>
        <!--结束：group循环-->



    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消','','form.button')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('eam_repair_order:create','eam_repair_order:update','eam_repair_order:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存','','form.button')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetHandleStatusEnum')}]];
    var SELECT_REPAIRSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.RepairOrderStatusEnum')}]];
    var RADIO_REPAIRTYPE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.RepairOrderTypeEnum')}]];
    var VALIDATE_CONFIG={"urgencyId":{"labelInForm":"紧急程度","inputType":"select_box","required":true},"businessDate":{"date":true,"labelInForm":"业务日期","inputType":"date_input"},"reportUserId":{"labelInForm":"报修人员","inputType":"button","required":true},"categoryTplId":{"labelInForm":"故障类型","inputType":"select_box","required":true},"repairType":{"labelInForm":"维修类型","inputType":"radio_box","required":true},"planFinishDate":{"date":true,"labelInForm":"计划完成日期","inputType":"date_input"},"content":{"labelInForm":"报修内容","inputType":"text_area","required":true}};
    var AUTH_PREFIX="eam_repair_order";

    // 单据ID
    var BILL_ID = [[${billId}]] ;
    // 单据类型
    var BILL_TYPE = [[${billType}]] ;
    // curEmpId
    var CUR_EMP_ID = [[${curEmpId}]] ;
    // curUserName
    var CUR_USER_NAME = [[${curUserName}]] ;
    // instanceData
    var INSTANCE_DATA = [[${instanceData}]] ;

</script>



<script th:src="'/business/eam/repair_order/repair_order_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/repair_order/repair_order_form.js?'+${cacheKey}"></script>

</body>
</html>