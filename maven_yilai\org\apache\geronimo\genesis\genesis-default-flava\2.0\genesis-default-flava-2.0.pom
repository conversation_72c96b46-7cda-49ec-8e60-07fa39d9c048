<?xml version="1.0" encoding="UTF-8"?>
<!--
    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at
    
     http://www.apache.org/licenses/LICENSE-2.0
    
    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.
-->

<!-- $Rev: 782833 $ $Date: 2009-06-08 17:12:50 -0700 (Mon, 08 Jun 2009) $ -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <modelVersion>4.0.0</modelVersion>
    
    <parent>
        <groupId>org.apache.geronimo.genesis</groupId>
        <artifactId>genesis</artifactId>
        <version>2.0</version>
    </parent>
    
    <artifactId>genesis-default-flava</artifactId>
    <name>Genesis Flava :: Default</name>
    <packaging>pom</packaging>

    <build>
        <!--
        NOTE: This causes problems while building Genesis, projects which need the additional
              packaging bits, should define this locally until Maven can handle installing
              this extention in the reactor that also builds the artifact.

        <extensions>
            <extension>
                <groupId>org.apache.geronimo.genesis</groupId>
                <artifactId>genesis-packaging</artifactId>
                <version>2.0-SNAPSHOT</version>
            </extension>
        </extensions>
        -->

        <resources>
            <resource>
                <directory>${project.basedir}/src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/*</include>
                </includes>
            </resource>

            <resource>
                <directory>${project.basedir}/src/main/filtered-resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*</include>
                </includes>
            </resource>
        </resources>

        <testResources>
            <testResource>
                <directory>${project.basedir}/src/test/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/*</include>
                </includes>
            </testResource>

            <testResource>
                <directory>${project.basedir}/src/test/filtered-resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*</include>
                </includes>
            </testResource>
        </testResources>

        <pluginManagement>
            <plugins>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <configuration>
                        <silent>true</silent>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-ear-plugin</artifactId>
                    <configuration>
                        <archive>
                            <addMavenDescriptor>false</addMavenDescriptor>
                        </archive>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-rar-plugin</artifactId>
                    <configuration>
                        <archive>
                            <addMavenDescriptor>false</addMavenDescriptor>
                        </archive>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <configuration>
                        <encoding>UTF-8</encoding>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <configuration>
                        <redirectTestOutputToFile>true</redirectTestOutputToFile>
                        <forkMode>once</forkMode>
                        <argLine>-enableassertions</argLine>
                        <failIfNoTests>false</failIfNoTests>
                        <workingDirectory>${project.build.directory}</workingDirectory>
                        <excludes>
                            <exclude>**/Abstract*.java</exclude>
                            <exclude>**/Test*.java</exclude>
                        </excludes>
                        <includes>
                            <include>**/*Test.java</include>
                        </includes>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-war-plugin</artifactId>
                    <configuration>
                        <archive>
                            <addMavenDescriptor>false</addMavenDescriptor>
                        </archive>
                    </configuration>
                    <dependencies>
                        <!-- maven-archiver 2.1 is broken... won't allow custom manifestEntires, need to override until war plugin is updated. -->
                        <dependency>
                            <groupId>org.apache.maven</groupId>
                            <artifactId>maven-archiver</artifactId>
                            <version>2.2</version>
                        </dependency>
                    </dependencies>
                </plugin>

                <plugin>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <configuration>
                        <instructions>
                            <Implementation-Title>${project.name}</Implementation-Title>
                            <Implementation-Version>${project.version}</Implementation-Version>
                            <Bundle-DocURL>${project.url}</Bundle-DocURL>
                            <!-- private-package is useful to make sure you listed all the packages you meant to -->
                            <!--<Private-Package />-->
                        </instructions>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>com.google.code.maven-license-plugin</groupId>
                    <artifactId>maven-license-plugin</artifactId>
                    <executions>
                        <execution>
                            <goals>
                                <goal>check</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <aggregate>true</aggregate>
                        <header>${project.basedir}/src/etc/license-header.txt</header>
                        <encoding>UTF-8</encoding>
                        <useDefaultExcludes>true</useDefaultExcludes>
                        <excludes>
                            <exclude>**/README.txt</exclude>
                            <exclude>**/LICENSE.txt</exclude>
                            <exclude>**/NOTICE.txt</exclude>
                            <exclude>**/*.psd</exclude>
                            <exclude>**/*.mdxml</exclude>
                        </excludes>
                        <useDefaultMapping>true</useDefaultMapping>
                        <mapping>
                            <jjt>JAVADOC_STYLE</jjt>
                        </mapping>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.apache.geronimo.genesis</groupId>
                <artifactId>genesis-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>validate-configuration</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <optimize>true</optimize>
                    <debug>true</debug>
                    <showDeprecation>true</showDeprecation>
                    <showWarnings>true</showWarnings>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireMavenVersion>
                                    <version>[2.0.10,)</version>
                                </requireMavenVersion>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-remote-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>process</goal>
                        </goals>
                        <configuration>
                            <resourceBundles>
                                <resourceBundle>org.apache:apache-jar-resource-bundle:1.4</resourceBundle>
                            </resourceBundles>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>ianal-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>verify-legal-files</goal>
                        </goals>
                        <configuration>
                            <strict>true</strict>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <!-- TODO the versions duplicate those in plugin management in parent pom.  Are they independent?
    if so, versions should be moved to parent pom <reporting> section and removed here.  Otherwise just remove them here -->
    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.5</version>

                <reportSets>
                    <reportSet>
                        <reports>
                            <report>javadoc</report>
                            <report>test-javadoc</report>
                        </reports>
                    </reportSet>
                </reportSets>

                <configuration>
                    <quiet>true</quiet>
                    <tags>
                        <tag>
                            <name>goal</name>
                            <placement>Xt</placement>
                        </tag>
                        <tag>
                            <name>phase</name>
                            <placement>Xt</placement>
                        </tag>
                        <tag>
                            <name>execute</name>
                            <placement>Xt</placement>
                        </tag>
                        <tag>
                            <name>requiresDependencyResolution</name>
                            <placement>Xt</placement>
                        </tag>
                        <tag>
                            <name>parameter</name>
                            <placement>Xf</placement>
                        </tag>
                        <tag>
                            <name>required</name>
                            <placement>Xf</placement>
                        </tag>
                        <tag>
                            <name>readonly</name>
                            <placement>Xf</placement>
                        </tag>
                        <tag>
                            <name>component</name>
                            <placement>Xf</placement>
                        </tag>
                        <tag>
                            <name>plexus.component</name>
                            <placement>Xf</placement>
                        </tag>
                        <tag>
                            <name>plexus.requirement</name>
                            <placement>Xf</placement>
                        </tag>
                    </tags>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jxr-plugin</artifactId>
                <version>2.1</version>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-pmd-plugin</artifactId>
                <version>2.4</version>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-project-info-reports-plugin</artifactId>
                <version>2.1</version>
                <configuration>
                    <dependencyLocationsEnabled>false</dependencyLocationsEnabled>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-report-plugin</artifactId>
                <version>2.4.3</version>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>cobertura-maven-plugin</artifactId>
                <version>2.2</version>
                <configuration>
                    <quiet>true</quiet>
                </configuration>
            </plugin>
        </plugins>
    </reporting>

    <profiles>
        <profile>
            <id>apache-release</id>
            <activation>
                <property>
                    <name>apache-release</name>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.geronimo.genesis</groupId>
                        <artifactId>genesis-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>validate-release-configuration</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>license-check</id>

            <pluginRepositories>
                <pluginRepository>
                    <id>mc-release</id>
                    <url>http://mc-repo.googlecode.com/svn/maven2/releases</url>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                </pluginRepository>
            </pluginRepositories>

            <build>
                <defaultGoal>license:check</defaultGoal>
                <plugins>
                    <plugin>
                        <groupId>com.google.code.maven-license-plugin</groupId>
                        <artifactId>maven-license-plugin</artifactId>
                        <version>1.4.0</version>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>check</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <modules>
        <module>genesis-java6-flava</module>
        <module>genesis-java5-flava</module>
        <module>genesis-java1.4-flava</module>
    </modules>
    
</project>

