
<!DOCTYPE html>
<html style="background-color: #FFFFFF">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <title th:text="${lang.translate('分类查询资产')}">分类查询资产</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/zTree/css/zTreeStyle/zTreeStyle.css" type="text/css">
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" th:href="@{/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css}" rel="stylesheet"/>


    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon"> <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">

    <style>
        .top-container {
            display: -webkit-flex; /* Safari */
            display: flex;
            width: 100%;
            align-items:top;
        }
        .left-tree {
            width: 260px;
            background-color: #ffffff;
            border-right: 1px #e6e6e6 solid;
        }
        .right-tab {

            flex:1 ;
        }
    </style>
</head>

<body>


<div class="top-container">
    <div class="left-tree">
        <div class="layui-form toolbar" style="padding:6px; border-bottom:1px #e6e6e6 solid; " id="toolbar">
            <table style="width: 100%"><tr>
                <td>
                    <input id="search-input" class="layui-input search-input" type="text" placeholder="请输入关键字" style="width: 100%"/>
                </td>

            </tr></table>
        </div>
        <div id="tree-container" style="overflow:auto;height:800px">
            <ul id="menu-tree" class="ztree"></ul>
        </div>
    </div>
    <div class="right-tab" style="margin-left: 8px;margin-right: 8px" >
        <div style="margin-top:6px;">

            <iframe id="assetList" th:src="'/business/eam/goods_stock/goods_stock_select_list.html?pageType='+${pageType}+'&operType='+${operType}+'&ownerTmpId='+${ownerTmpId}+'&ownerCode='+${ownerCode}+'&ownerType='+${ownerType}+'&selectedCode='+${selectedCode}" frameborder=0 width="100%"  height="760px"></iframe>
        </div>
    </div>
</div>

<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消')}" >取消</button>
    <button class="layui-btn" style="margin-right: 15px"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('确定')}">确定</button>
</div>


<script th:inline="javascript">

    // var CATEGORY_PARENT_ID =  [[${categoryParentId}]];
    var CATEGORY_PARENT_ID =  "0"
    var ATTRIBUTE_LIST_DATA =  [[${attributeListData}]];

    var AUTH_PREFIX="eam_asset";

    var OWNER_CODE = [[${ownerCode}]] ;
    var OWNER_TMP_ID = [[${ownerTmpId}]] ;
    var OPER_TYPE = [[${operType}]] ;
    var OWNER_TYPE = [[${ownerType}]] ;
    var PAGE_TYPE = [[${pageType}]] ;
    var SELECTED_CODE = [[${selectedCode}]] ;


</script>





<script type="text/javascript" src="/module/global.js"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js"></script>
<script type="text/javascript" src="/assets/libs/zTree/js/jquery.ztree.all-3.5.min.js"></script>

<!--<script src="/business/eam/asset/asset_select_list.js"></script>-->
<script src="/business/eam/goods_stock/goods_stock_select_tree.js"></script>
<script src="/business/eam/goods_stock/goods_stock_select_tree_ext.js"></script>
</body>
</html>
