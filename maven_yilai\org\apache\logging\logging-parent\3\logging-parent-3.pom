<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one
  ~ or more contributor license agreements.  See the NOTICE file
  ~ distributed with this work for additional information
  ~ regarding copyright ownership.  The ASF licenses this file
  ~ to you under the Apache License, Version 2.0 (the
  ~ "License"); you may not use this file except in compliance
  ~ with the License.  You may obtain a copy of the License at
  ~
  ~   https://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing,
  ~ software distributed under the License is distributed on an
  ~ "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  ~ KIND, either express or implied.  See the License for the
  ~ specific language governing permissions and limitations
  ~ under the License.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache</groupId>
    <artifactId>apache</artifactId>
    <version>23</version>
  </parent>

  <groupId>org.apache.logging</groupId>
  <artifactId>logging-parent</artifactId>
  <packaging>pom</packaging>
  <version>3</version>

  <name>Apache Logging Services</name>
  <description>
    Parent pom for Apache Logging Services projects.
  </description>
  <url>https://logging.apache.org/</url>
  <inceptionYear>1999</inceptionYear>

  <properties>
    <!-- All Apache Logging projects currently have a baseline JDK version of 1.8 -->
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
  </properties>

  <scm>
    <connection>scm:git:https://gitbox.apache.org/repos/asf/logging-parent.git</connection>
    <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/logging-parent.git</developerConnection>
    <url>https://gitbox.apache.org/repos/asf?p=logging-parent.git</url>
    <tag>logging-parent-3</tag>
  </scm>

  <mailingLists>
    <mailingList>
      <name>log4j-user</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>https://lists.apache.org/list.html?<EMAIL></archive>
    </mailingList>
    <mailingList>
      <name>dev</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>https://lists.apache.org/list.html?<EMAIL></archive>
    </mailingList>
  </mailingLists>

  <issueManagement>
    <system>JIRA</system>
    <url>https://issues.apache.org/jira/browse/LOG4J2</url>
  </issueManagement>

  <!-- ASF parent provides the following top level metadata that we don't need to override:
  * license
  * organization
  * repositories
  * distributionManagement
  -->

</project>
