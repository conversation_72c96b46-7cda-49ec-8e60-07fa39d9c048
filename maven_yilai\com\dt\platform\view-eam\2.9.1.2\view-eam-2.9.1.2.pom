<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dt.platform</groupId>
        <artifactId>parent-eam</artifactId>
        <version>*******</version>
        <relativePath>../../pom.xml</relativePath>
        <!-- lookup parent from repository -->
    </parent>
    <artifactId>view-eam</artifactId>
    <name>view-eam</name>
    <description>EAM View</description>
    <dependencies>
        <dependency>
            <groupId>com.foxnicweb.web</groupId>
            <artifactId>framework-view</artifactId>
            <version>${foxnic-web.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>domain-eam</artifactId>
            <version>${platform.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>proxy-eam</artifactId>
            <version>${platform.version}</version>
        </dependency>

    </dependencies>
    <build>
        <defaultGoal>compile</defaultGoal>
        <resources>
            <resource>
                <directory>${basedir}/src/main/resources</directory>
                <includes>
                    <include>**/**</include>
                </includes>
            </resource>
        </resources>
    </build>
</project>
