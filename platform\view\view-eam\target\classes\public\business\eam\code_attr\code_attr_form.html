<!-- 
/**
 * 编码分配属性 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2021-08-07 21:18:50
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
	<title th:text="${lang.translate('编码分配属性')}">编码分配属性</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/assets/css/admin.css"/>
    <link rel="stylesheet" href="/assets/css/foxnic-web.css">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" th:href="@{/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css}" rel="stylesheet"/>
</head>

<body style="overflow-y: hidden">

<form id="data-form" lay-filter="data-form" class="layui-form model-form" style="display:none">
    <input name="id" id="id"  type="hidden"/>
    
    <div class="layui-form-item" >
        <label class="layui-form-label" th:text="${lang.translate('占位符')}">占位符</label>
        <div class="layui-input-block">
            <input lay-filter="code" id="code" name="code" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('占位符') }" type="text" class="layui-input"   />
        </div>
    </div>
    
	
    
    <div class="layui-form-item" >
        <label class="layui-form-label" th:text="${lang.translate('编码名称')}">编码名称</label>
        <div class="layui-input-block">
            <input lay-filter="name" id="name" name="name" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('编码名称') }" type="text" class="layui-input"   />
        </div>
    </div>
    
	
	
    <div class="layui-form-item" >
        <label class="layui-form-label" th:text="${lang.translate('属性分类')}">属性分类</label>
        <div class="layui-input-block">
            <input type="radio" name="type" lay-filter="type" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.common.CodeAttrTypeEnum')}" th:value="${e.code}" th:title="${e.text}">
        </div>
    </div>

	
    
    <div class="layui-form-item" >
        <label class="layui-form-label" th:text="${lang.translate('排序')}">排序</label>
        <div class="layui-input-block">
            <input lay-filter="sort" id="sort" name="sort" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('排序') }" type="text" class="layui-input"   />
        </div>
    </div>
    
	
    
    <div class="layui-form-item" >
        <label class="layui-form-label" th:text="${lang.translate('备注')}">备注</label>
        <div class="layui-input-block">
            <input lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('备注') }" type="text" class="layui-input"   />
        </div>
    </div>
    
	


    <div class="layui-form-item model-form-footer">
        <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消')}">取消</button>
        <button th:if="${perm.checkAnyAuth('eam_code_attr:create','eam_code_attr:update','eam_code_attr:save')}" class="layui-btn"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存')}">保存</button>
    </div>
</form>

<script type="text/javascript" src="/module/global.js"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js"></script>

<script src="/business/eam/code_attr/code_attr_form.js"></script>

</body>
</html>