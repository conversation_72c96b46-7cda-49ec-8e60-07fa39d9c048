<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <artifactId>camunda-engine-rest-core</artifactId>
  <name>Camunda Platform - engine - REST</name>
  <packaging>jar</packaging>

  <parent>
    <groupId>org.camunda.bpm</groupId>
    <artifactId>camunda-engine-rest-root</artifactId>
    <relativePath>../</relativePath>
    <version>7.17.0</version>
  </parent>

  <properties>
    <!-- pin tomcat version for engine-rest -->
    <version.tomcat>7.0.50</version.tomcat>
    <rest.http.port>38080</rest.http.port>
    <javax.activation.version>1.1.1</javax.activation.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>commons-fileupload</groupId>
      <artifactId>commons-fileupload</artifactId>
    </dependency>
    <!-- override managed version from commons-fileupload -->
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
    </dependency>

    <dependency>
      <groupId>com.fasterxml.jackson.jaxrs</groupId>
      <artifactId>jackson-jaxrs-json-provider</artifactId>
      <version>${version.jackson}</version>
      <!--
      * Since version 2.10.0 this dependency comes with transitive Jakarta dependencies
      * These trans-deps are directly included in JDKs <= Java 8 * For Java >= 9 compatible containers, we make sure to include these dependencies
      -->
      <exclusions>
        <exclusion>
          <groupId>jakarta.activation</groupId>
          <artifactId>jakarta.activation-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>jakarta.xml.bind</groupId>
          <artifactId>jakarta.xml.bind-api</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>${version.jackson}</version>
    </dependency>

    <dependency>
      <groupId>com.fasterxml.jackson.datatype</groupId>
      <artifactId>jackson-datatype-jsr310</artifactId>
      <version>${version.jackson}</version>
    </dependency>

    <!-- provided deps -->
    <dependency>
      <groupId>org.jboss.spec.javax.servlet</groupId>
      <artifactId>jboss-servlet-api_3.0_spec</artifactId>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>org.camunda.bpm</groupId>
      <artifactId>camunda-engine</artifactId>
      <scope>provided</scope>
      <exclusions>
        <exclusion>
          <groupId>org.springframework</groupId>
          <artifactId>spring-jcl</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.camunda.commons</groupId>
      <artifactId>camunda-commons-logging</artifactId>
      <scope>provided</scope>
    </dependency>
    
    <dependency>
      <groupId>javax.activation</groupId>
      <artifactId>activation</artifactId>
      <version>${javax.activation.version}</version>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>com.sun.xml.bind</groupId>
      <artifactId>jaxb-impl</artifactId>
      <scope>test</scope>
    </dependency>

    <!--
      needed for offline shrinkwrap (in particular CustomJacksonDateFormatTest), 
      BOMs must be installed before this module is built
    -->
    <dependency>
      <groupId>org.camunda.bpm</groupId>
      <artifactId>camunda-bom</artifactId>
      <version>${project.version}</version>
      <type>pom</type>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.camunda.bpm</groupId>
      <artifactId>camunda-core-internal-dependencies</artifactId>
      <version>${project.version}</version>
      <type>pom</type>
      <scope>test</scope>
    </dependency>

    <!-- test -->
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>

    <!-- only included to prevent java.lang.ClassNotFoundException: org.apache.jasper.servlet.JspServlet -->
    <!-- removing this dep. will not break the tests, but makes them "ugly"-->
    <dependency>
      <groupId>org.apache.tomcat.embed</groupId>
      <artifactId>tomcat-embed-jasper</artifactId>
      <version>${version.tomcat}</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.jboss.shrinkwrap.resolver</groupId>
      <artifactId>shrinkwrap-resolver-depchain</artifactId>
      <version>${version.shrinkwrap.resolvers}</version>
      <scope>test</scope>
      <type>pom</type>
    </dependency>

    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>org.hamcrest</groupId>
          <artifactId>hamcrest-core</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>io.rest-assured</groupId>
      <artifactId>rest-assured</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpcore</artifactId>
      <version>4.4.5</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>commons-codec</groupId>
      <artifactId>commons-codec</artifactId>
      <!-- override version -->
      <version>1.15</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.h2database</groupId>
      <artifactId>h2</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-test</artifactId>
      <version>${version.spring.framework}</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>org.springframework</groupId>
          <artifactId>spring-jcl</artifactId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- Tomcat for resteasy and wink -->
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
      <scope>test</scope>
    </dependency>

   <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>jcl-over-slf4j</artifactId>
      <scope>test</scope>
   </dependency>

   <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>jul-to-slf4j</artifactId>
      <scope>test</scope>
   </dependency>

    <dependency>
      <groupId>org.camunda.commons</groupId>
      <artifactId>camunda-commons-testing</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.apache.tomcat.embed</groupId>
      <artifactId>tomcat-embed-core</artifactId>
      <version>${version.tomcat}</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.apache.tomcat.embed</groupId>
      <artifactId>tomcat-embed-logging-juli</artifactId>
      <version>${version.tomcat}</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <scope>test</scope>
    </dependency>

  </dependencies>

  <build>
    <testResources>
      <testResource>
        <directory>src/test/resources</directory>
        <filtering>true</filtering>
      </testResource>
    </testResources>

    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <executions>
          <execution>
            <!-- This id must match the -Prelease-profile id value or else sources will be "uploaded" twice, which causes Nexus to fail -->
            <id>attach-sources</id>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>test-jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-war-plugin</artifactId>
        <configuration>
          <failOnMissingWebXml>false</failOnMissingWebXml>
        </configuration>
      </plugin>

      <!-- the test runtimes each bring the JAX-RS classes themselves -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <classpathDependencyExcludes>
            <classpathDependencyExclude>org.jboss.spec.javax.ws.rs:jboss-jaxrs-api_1.1_spec</classpathDependencyExclude>
          </classpathDependencyExcludes>
          <systemPropertyVariables>
            <restEasyVersion>${version.resteasy}</restEasyVersion>
          </systemPropertyVariables>
        </configuration>
      </plugin>
      
      <plugin>
        <groupId>org.jboss.shrinkwrap.resolver</groupId>
        <artifactId>shrinkwrap-resolver-maven-plugin</artifactId>
        <version>${version.shrinkwrap.resolvers}</version>
        <executions>
          <execution>
            <goals>
              <goal>propagate-execution-context</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>distro</id>
      <dependencies>
        <dependency>
          <groupId>org.jboss.spec.javax.ws.rs</groupId>
          <artifactId>jboss-jaxrs-api_2.1_spec</artifactId>
          <scope>provided</scope>
        </dependency>
      </dependencies>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <skipTests>true</skipTests>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>resteasy</id>
      <properties>
        <version.netty>4.1.42.Final</version.netty>
      </properties>
      <dependencies>
        <dependency>
          <groupId>org.jboss.spec.javax.ws.rs</groupId>
          <artifactId>jboss-jaxrs-api_2.1_spec</artifactId>
          <scope>provided</scope>
        </dependency>
        <dependency>
          <groupId>org.jboss.resteasy</groupId>
          <artifactId>resteasy-jaxrs</artifactId>
          <version>${version.resteasy}</version>
          <scope>test</scope>
          <exclusions>
            <exclusion>
              <artifactId>httpclient</artifactId>
              <groupId>org.apache.httpcomponents</groupId>
            </exclusion>
          </exclusions>
        </dependency>
        
        <dependency>
          <groupId>io.netty</groupId>
          <artifactId>netty-all</artifactId>
          <version>${version.netty}</version>
          <scope>test</scope>
        </dependency>

        <dependency>
          <groupId>org.jboss.resteasy</groupId>
          <artifactId>resteasy-netty4</artifactId>
          <version>${version.resteasy}</version>
          <scope>test</scope>
        </dependency>
      </dependencies>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <excludes>
                <!-- This test is only needed for the Resteasy 2 profile; with
                  Resteasy 3 we can run the regular test -->
                <exclude>**/ResteasyTaskRestServiceInteractionTest.java</exclude>
              </excludes>
              <redirectTestOutputToFile>true</redirectTestOutputToFile>
            </configuration>
            <executions>
              <execution>
                <id>encoding-test</id>
                <phase>test</phase>
                <goals>
                  <goal>test</goal>
                </goals>
                <configuration>
                  <argLine>-Dfile.encoding=ISO-8859-1</argLine>
                  <includes>
                    <include>**/ProcessDefinitionRestServiceInteractionTest.java</include>
                    <include>**/TaskRestServiceInteractionTest.java</include>
                  </includes>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>build-helper-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>add-test-source</id>
                <phase>generate-test-sources</phase>
                <goals>
                  <goal>add-test-source</goal>
                </goals>
                <configuration>
                  <sources>
                    <source>src/test/java-resteasy</source>
                  </sources>
                </configuration>
              </execution>
              <execution>
                <id>add-test-resource</id>
                <phase>generate-test-resources</phase>
                <goals>
                  <goal>add-test-resource</goal>
                </goals>
                <configuration>
                  <resources>
                    <resource>
                      <directory>
                        src/test/resources-resteasy
                      </directory>
                    </resource>
                  </resources>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>jersey2</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <dependencies>
        <dependency>
          <groupId>javax.ws.rs</groupId>
          <artifactId>javax.ws.rs-api</artifactId>
          <version>2.0.1</version>
          <scope>provided</scope>
        </dependency>
        <dependency>
          <groupId>org.glassfish.jersey.containers</groupId>
          <artifactId>jersey-container-grizzly2-http</artifactId>
          <version>${version.jersey2}</version>
          <scope>test</scope>
        </dependency>
        <dependency>
          <groupId>org.glassfish.jersey.containers</groupId>
          <artifactId>jersey-container-servlet</artifactId>
          <version>${version.jersey2}</version>
          <scope>test</scope>
        </dependency>
        <dependency>
          <groupId>org.glassfish.jersey.inject</groupId>
          <artifactId>jersey-hk2</artifactId>
          <version>${version.jersey2}</version>
          <scope>test</scope>
        </dependency>
      </dependencies>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <excludes>
                <exclude>**/rest/standalone/**</exclude>
              </excludes>
              <redirectTestOutputToFile>true</redirectTestOutputToFile>
            </configuration>
            <executions>
              <execution>
                <id>encoding-test</id>
                <phase>test</phase>
                <goals>
                  <goal>test</goal>
                </goals>
                <configuration>
                  <argLine>-Dfile.encoding=ISO-8859-1</argLine>
                  <includes>
                    <include>**/ProcessDefinitionRestServiceInteractionTest.java</include>
                    <include>**/TaskRestServiceInteractionTest.java</include>
                  </includes>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>build-helper-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>add-test-source</id>
                <phase>generate-test-sources</phase>
                <goals>
                  <goal>add-test-source</goal>
                </goals>
                <configuration>
                  <sources>
                    <source>src/test/java-jersey2</source>
                  </sources>
                </configuration>
              </execution>
              <execution>
                <id>add-test-resource</id>
                <phase>generate-test-resources</phase>
                <goals>
                  <goal>add-test-resource</goal>
                </goals>
                <configuration>
                  <resources>
                    <resource>
                      <directory>src/test/resources-jersey2</directory>
                    </resource>
                  </resources>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>cxf</id>

      <properties>
        <version.slf4j>1.7.7</version.slf4j>
        <version.logback>1.1.3</version.logback>
      </properties>

      <dependencies>
        <dependency>
          <groupId>javax.ws.rs</groupId>
          <artifactId>javax.ws.rs-api</artifactId>
          <version>2.0.1</version>
          <scope>provided</scope>
        </dependency>
        
        <dependency>
          <groupId>org.apache.cxf</groupId>
          <artifactId>cxf-rt-frontend-jaxrs</artifactId>
          <version>${version.cxf}</version>
          <scope>test</scope>
        </dependency>

        <!-- Jetty is needed if you're are not using the CXFServlet -->
          <dependency>
          <groupId>org.apache.cxf</groupId>
          <artifactId>cxf-rt-transports-http-jetty</artifactId>
          <version>${version.cxf}</version>
          <scope>test</scope>
        </dependency>
      </dependencies>

     <build>
       <plugins>

          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <excludes>
                <exclude>**/rest/standalone/**</exclude>
              </excludes>
              <redirectTestOutputToFile>true</redirectTestOutputToFile>
            </configuration>
            <executions>
              <execution>
                <id>encoding-test</id>
                <phase>test</phase>
                <goals>
                  <goal>test</goal>
                </goals>
                <configuration>
                  <argLine>-Dfile.encoding=ISO-8859-1</argLine>
                  <includes>
                    <include>**/ProcessDefinitionRestServiceInteractionTest.java</include>
                    <include>**/TaskRestServiceInteractionTest.java</include>
                  </includes>
                </configuration>
              </execution>
            </executions>
          </plugin>

          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>build-helper-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>add-test-source</id>
                <phase>generate-test-sources</phase>
                <goals>
                  <goal>add-test-source</goal>
                </goals>
                <configuration>
                  <sources>
                    <source>src/test/java-cxf</source>
                  </sources>
                </configuration>
              </execution>
              <execution>
                <id>add-test-resource</id>
                <phase>generate-test-resources</phase>
                <goals>
                  <goal>add-test-resource</goal>
                </goals>
                <configuration>
                  <resources>
                    <resource>
                      <directory>
                        src/test/resources-cxf
                      </directory>
                    </resource>
                  </resources>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>wls-compatibility</id>
      <dependencies>
        <dependency>
          <groupId>joda-time</groupId>
          <artifactId>joda-time</artifactId>
          <version>1.2.1</version>
        </dependency>
      </dependencies>
    </profile>

    <profile>
      <id>wildfly-compatibility</id>
      <properties>
        <!-- Test with Resteasy version used by the oldest
             Camunda-supported Wildfly/JBoss EAP version.
             Currently this is JBoss EAP 7.0 (3.0.16.Final) and 7.1 (3.0.24.Final). -->
        <version.resteasy>3.0.16.Final</version.resteasy>
      </properties>
      <!-- Resteasy 3.0.X is more similar to Resteasy 2.x, so we need to define separate
           dependencies for testing compatibility with older Wildfly/JBoss versions.
           We can drop the 'dependencies' and 'build' sections below once we drop support for
           Resteasy 3.0.X -->
      <dependencies>
        <dependency>
          <groupId>org.jboss.spec.javax.ws.rs</groupId>
          <artifactId>jboss-jaxrs-api_1.1_spec</artifactId>
          <scope>provided</scope>
        </dependency>
        <dependency>
          <groupId>org.jboss.resteasy</groupId>
          <artifactId>resteasy-jaxrs</artifactId>
          <version>${version.resteasy}</version>
          <scope>test</scope>
          <exclusions>
            <exclusion>
              <artifactId>httpclient</artifactId>
              <groupId>org.apache.httpcomponents</groupId>
            </exclusion>
          </exclusions>
        </dependency>

        <dependency>
          <groupId>org.jboss.resteasy</groupId>
          <artifactId>resteasy-netty</artifactId>
          <version>${version.resteasy}</version>
          <scope>test</scope>
        </dependency>
      </dependencies>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <excludes>
                <exclude>**/TaskRestServiceInteractionTest.java</exclude>
                <exclude>**/NoServletEmptyBodyFilterTest.java</exclude>
                <exclude>**/ServletEmptyBodyFilterTest.java</exclude>
              </excludes>
              <redirectTestOutputToFile>true</redirectTestOutputToFile>
            </configuration>
            <executions>
              <execution>
                <id>encoding-test</id>
                <phase>test</phase>
                <goals>
                  <goal>test</goal>
                </goals>
                <configuration>
                  <argLine>-Dfile.encoding=ISO-8859-1</argLine>
                  <includes>
                    <include>**/ProcessDefinitionRestServiceInteractionTest.java</include>
                    <include>**/ResteasyTaskRestServiceInteractionTest.java</include>
                  </includes>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>build-helper-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>add-test-source</id>
                <phase>generate-test-sources</phase>
                <goals>
                  <goal>add-test-source</goal>
                </goals>
                <configuration>
                  <sources>
                    <source>src/test/java-resteasy</source>
                  </sources>
                </configuration>
              </execution>
              <execution>
                <id>add-test-resource</id>
                <phase>generate-test-resources</phase>
                <goals>
                  <goal>add-test-resource</goal>
                </goals>
                <configuration>
                  <resources>
                    <resource>
                      <directory>
                        src/test/resources-resteasy
                      </directory>
                    </resource>
                  </resources>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

  </profiles>
</project>
