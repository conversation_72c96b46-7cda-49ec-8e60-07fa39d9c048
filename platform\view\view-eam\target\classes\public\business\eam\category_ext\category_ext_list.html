<!--
/**
 * 资产分类扩展 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2025-02-15 17:11:11
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
    <title th:text="${lang.translate('资产分类扩展')}">资产分类扩展</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 标签 , label ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('资产价值')}" class="search-label costType-label">资产价值</span><span class="search-colon">:</span></div>
                        <div id="costType" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_goods_cost_type'}" style="width:180px" extraParam="{}"></div>
                    </div>

                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('标签')}" class="search-label label-label">标签</span><span class="search-colon">:</span></div>
                        <input id="label" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 使用周期 , lifeCycle ,typeName=number_input, isHideInSearch=true -->
                    <!-- 资产分类 , pcmCategoryId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 残值率 , residualsRate ,typeName=number_input, isHideInSearch=true -->
                    <!-- 计量单位 , unit ,typeName=text_input, isHideInSearch=true -->
                    <!-- 修改人ID , updateBy ,typeName=text_input, isHideInSearch=true -->
                    <!-- pcmCatalogFullName , pcmCatalogFullName ,typeName=text_input, isHideInSearch=true -->
                    <!-- pcmCatalogName , pcmCatalogName ,typeName=text_input, isHideInSearch=true -->
                    <!-- pcmCatalogCode , pcmCatalogCode ,typeName=text_input, isHideInSearch=true -->
                    <!-- 备注 , notes ,typeName=text_area, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('备注')}" class="search-label notes-label">备注</span><span class="search-colon">:</span></div>
                        <input id="notes" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>


                </div>
            </div>

            <!-- 按钮区域 -->
            <div id="search-area" class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索','','cmp:table.search')}">搜索</span></button>
            </div>
        </div>

        <div id="table-area" style="margin-top: 42px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
        <button id="export" class="layui-btn icon-btn layui-btn-sm export" lay-event="export"><span>导出</span></button>
        <button id="bathUpdate" class="layui-btn icon-btn layui-btn-sm bathUpdate" lay-event="bathUpdate"><span>批量更新</span></button>

    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">



</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
    var AUTH_PREFIX="eam_category_ext";


</script>

<script th:src="'/business/eam/category_ext/category_ext_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/category_ext/category_ext_list.js?'+${cacheKey}"></script>

</body>
</html>