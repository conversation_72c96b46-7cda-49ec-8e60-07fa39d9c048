<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.ow2</groupId>
    <artifactId>ow2</artifactId>
    <version>1.5</version>
  </parent>
  <groupId>org.ow2.asm</groupId>
  <artifactId>asm-commons</artifactId>
  <version>7.1</version>
  <name>asm-commons</name>
  <description>Usefull class adapters based on ASM, a very small and fast Java bytecode manipulation framework</description>
  <url>http://asm.ow2.org/</url>
  <inceptionYear>2000</inceptionYear>
  <organization>
    <name>OW2</name>
    <url>http://www.ow2.org/</url>
  </organization>
  <licenses>
    <license>
      <name>BSD</name>
      <url>http://asm.ow2.org/license.html</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>ebruneton</id>
      <name>Eric Bruneton</name>
      <email><EMAIL></email>
      <roles>
        <role>Creator</role>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <id>eu</id>
      <name>Eugene Kuleshov</name>
      <email><EMAIL></email>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <id>forax</id>
      <name>Remi Forax</name>
      <email><EMAIL></email>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
  </developers>
  <mailingLists>
    <mailingList>
      <name>ASM Users List</name>
      <subscribe>https://mail.ow2.org/wws/subscribe/asm</subscribe>
      <post><EMAIL></post>
      <archive>https://mail.ow2.org/wws/arc/asm/</archive>
    </mailingList>
    <mailingList>
      <name>ASM Team List</name>
      <subscribe>https://mail.ow2.org/wws/subscribe/asm-team</subscribe>
      <post><EMAIL></post>
      <archive>https://mail.ow2.org/wws/arc/asm-team/</archive>
    </mailingList>
  </mailingLists>
  <scm>
    <connection>scm:git:https://gitlab.ow2.org/asm/asm/</connection>
    <developerConnection>scm:git:https://gitlab.ow2.org/asm/asm/</developerConnection>
    <url>https://gitlab.ow2.org/asm/asm/</url>
  </scm>
  <issueManagement>
    <url>https://gitlab.ow2.org/asm/asm/issues</url>
  </issueManagement>
  <dependencies>
    <dependency>
      <groupId>org.ow2.asm</groupId>
      <artifactId>asm</artifactId>
      <version>7.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.ow2.asm</groupId>
      <artifactId>asm-tree</artifactId>
      <version>7.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.ow2.asm</groupId>
      <artifactId>asm-analysis</artifactId>
      <version>7.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.ow2.asm</groupId>
      <artifactId>asm-util</artifactId>
      <version>7.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.ow2.asm</groupId>
      <artifactId>asm-test</artifactId>
      <version>7.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <version>5.3.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-params</artifactId>
      <version>5.3.2</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
