<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion> 
  <parent>
    <groupId>com.fasterxml.jackson</groupId>
    <artifactId>jackson-base</artifactId>
    <version>2.11.4</version>
  </parent>
  <groupId>com.fasterxml.jackson.module</groupId>
  <artifactId>jackson-modules-base</artifactId>
  <name>Jackson modules: Base</name>
  <version>2.11.4</version>
  <packaging>pom</packaging>
  <description>Parent pom for Jackson "base" modules: modules that build directly on databind, and are
not datatype, data format, or JAX-RS provider modules.
  </description>

  <modules>
    <module>afterburner</module>
    <module>guice</module>
    <module>jaxb</module>
    <module>mrbean</module>
    <module>osgi</module>
    <module>paranamer</module>
  </modules>

  <url>https://github.com/FasterXML/jackson-modules-base</url>
  <scm>
    <connection>scm:git:**************:FasterXML/jackson-modules-base.git</connection>
    <developerConnection>scm:git:**************:FasterXML/jackson-modules-base.git</developerConnection>
    <url>http://github.com/FasterXML/jackson-modules-base</url>    
    <tag>jackson-modules-base-2.11.4</tag>
  </scm>
  <issueManagement>
    <url>https://github.com/FasterXML/jackson-modules-base/issues</url>
  </issueManagement>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <!-- 27-Feb-2019, tatu: Upgrade 5.2 -> 7.0 to support Java 11 -->
    <!-- 21-Jul-2020, tatu: 7.0 -> 7.3.1 -->
    <version.asm>7.3.1</version.asm>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.ow2.asm</groupId>
        <artifactId>asm</artifactId>
        <version>${version.asm}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <dependencies>
    <dependency> <!-- all modules use junit for testing -->
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>


  <!-- Alas, need to include snapshot reference since otherwise can not find
       snapshot of parent... -->
  <repositories>
    <repository>
      <id>sonatype-nexus-snapshots</id>
      <name>Sonatype Nexus Snapshots</name>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
      <releases><enabled>false</enabled></releases>
      <snapshots><enabled>true</enabled></snapshots>
    </repository>
  </repositories>

  <build>
    <pluginManagement>
      <plugins>
	<plugin>
	  <!-- Inherited from oss-base. Generate PackageVersion.java.-->
          <groupId>com.google.code.maven-replacer-plugin</groupId>
          <artifactId>replacer</artifactId>
	  <executions>
            <execution>
              <id>process-packageVersion</id>
              <phase>generate-sources</phase>
            </execution>
          </executions>
	</plugin>

	<plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <configuration>
            <excludes>
              <exclude>com/fasterxml/jackson/**/failing/*.java</exclude>
            </excludes>
          </configuration>
	</plugin>
      </plugins>
    </pluginManagement>
  </build>

</project>
