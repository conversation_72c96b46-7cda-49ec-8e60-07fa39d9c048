
<!DOCTYPE html>
<html>

<head>
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta charset="utf-8"/>
	<title th:text="${lang.translate('资产列表')}">资产列表</title>
	<link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
	<link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
	<link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
	<link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
	<link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
	<link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
	<link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
	<link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
	<script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
	<style>
	</style>
	<link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

	<div class="layui-card-body" style="">

		<div class="search-bar" style="">

			<div class="search-input-rows" style="opacity: 0">
				<!-- 搜索输入区域 -->
				<div class="layui-form toolbar search-inputs">
					<div class="search-unit">
						<div class="search-label-div" style="width:70px"><span th:text="${lang.translate('名称')}" class="search-label name-label">名称</span><span class="search-colon">:</span></div>
						<input id="name" class="layui-input search-input" style="width: 150px" type="text" />
					</div>
					<!-- 规格型号 , model ,typeName=text_input, isHideInSearch=false -->
					<div class="search-unit">
						<div class="search-label-div" style="width:70px"><span th:text="${lang.translate('规格型号')}" class="search-label model-label">规格型号</span><span class="search-colon">:</span></div>
						<input id="model" class="layui-input search-input" style="width: 150px" type="text" />
					</div>
					<!-- 序列号 , serialNumber ,typeName=text_input, isHideInSearch=false -->
					<div class="search-unit">
						<div class="search-label-div" style="width:70px"><span th:text="${lang.translate('序列号')}" class="search-label serialNumber-label">序列号</span><span class="search-colon">:</span></div>
						<input id="serialNumber" class="layui-input search-input" style="width: 150px" type="text" />
					</div>
				</div>
			</div>


			<!-- 按钮区域 -->
			<div id="search-area" class="layui-form toolbar search-buttons" style="opacity: 0">
				<button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索')}">搜索</span></button>
			</div>
		</div>

		<div id="table-area" style="margin-top: 42px ">
			<table class="layui-table" id="data-table" lay-filter="data-table"></table>
		</div>

	</div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
	<div class="layui-btn-container">
	</div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">
 <button class="layui-btn layui-btn-primary layui-btn-xs ops-edit-button " lay-event="edit"data-id="{{d.id}}"><span th:text="${lang.translate('修改')}">修改</span></button>
 <button class="layui-btn layui-btn-xs layui-btn-danger ops-delete-button " lay-event="del"data-id="{{d.id}}"><span th:text="${lang.translate('删除')}">删除</span></button>
</script>


<script th:inline="javascript">
	var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
	var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetHandleStatusEnum')}]];
	var SELECT_ASSETSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetStatusEnum')}]];
	var AUTH_PREFIX="eam_import_asset2";
	var IMPORT_ID = [[${importId}]];
	var PAGE_TYPE = [[${pageType}]];
</script>

<script th:src="'/business/eam/purchase_import/import_asset_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/purchase_import/import_asset_list.js?1'+${cacheKey}"></script>

</body>
</html>
