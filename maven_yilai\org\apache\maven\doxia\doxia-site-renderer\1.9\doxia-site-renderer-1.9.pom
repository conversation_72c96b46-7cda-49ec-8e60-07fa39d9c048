<?xml version="1.0" encoding="UTF-8"?>

<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.maven.doxia</groupId>
    <artifactId>doxia-sitetools</artifactId>
    <version>1.9</version>
    <relativePath>../pom.xml</relativePath>
  </parent>

  <artifactId>doxia-site-renderer</artifactId>

  <name>Doxia Sitetools :: Site Renderer</name>
  <description>The Site Renderer handles the rendering of sites, merging site decoration with document content.</description>


  <dependencies>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-artifact</artifactId>
      <version>3.0</version>
    </dependency>

    <!-- doxia -->
    <dependency>
      <groupId>org.apache.maven.doxia</groupId>
      <artifactId>doxia-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.doxia</groupId>
      <artifactId>doxia-logging-api</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.doxia</groupId>
      <artifactId>doxia-sink-api</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.doxia</groupId>
      <artifactId>doxia-decoration-model</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.doxia</groupId>
      <artifactId>doxia-skin-model</artifactId>
    </dependency>
    <!-- The modules are not in doxia core anymore so you have to state a dependency so that
         the component descriptor gets picked up. -->
    <dependency>
      <groupId>org.apache.maven.doxia</groupId>
      <artifactId>doxia-module-apt</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.doxia</groupId>
      <artifactId>doxia-module-confluence</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.doxia</groupId>
      <artifactId>doxia-module-docbook-simple</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.doxia</groupId>
      <artifactId>doxia-module-xdoc</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.doxia</groupId>
      <artifactId>doxia-module-xhtml</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.doxia</groupId>
      <artifactId>doxia-module-xhtml5</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.doxia</groupId>
      <artifactId>doxia-module-fml</artifactId>
      <scope>test</scope>
    </dependency>

    <!-- plexus -->
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-component-annotations</artifactId>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-i18n</artifactId>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-container-default</artifactId>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-velocity</artifactId>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-utils</artifactId>
    </dependency>

    <!-- misc -->
    <dependency>
      <groupId>org.apache.velocity</groupId>
      <artifactId>velocity</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.velocity</groupId>
      <artifactId>velocity-tools</artifactId>
      <version>2.0</version>
    </dependency>
    <dependency>
      <groupId>commons-collections</groupId>
      <artifactId>commons-collections</artifactId>
      <version>3.2.2</version>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.8.1</version>
    </dependency>

    <!-- test -->
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.doxia</groupId>
      <artifactId>doxia-core</artifactId>
      <version>${doxiaVersion}</version>
      <type>test-jar</type>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>net.sourceforge.htmlunit</groupId>
      <artifactId>htmlunit</artifactId>
      <version>2.24</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>2.28.2</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <profiles>
    <profile>
      <id>reporting</id>
      <reporting>
        <plugins>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>l10n-maven-plugin</artifactId>
            <version>1.0-alpha-2</version>
            <configuration>
              <locales>
                <locale>de</locale>
                <locale>en</locale>
                <locale>es</locale>
                <locale>fr</locale>
                <locale>it</locale>
                <locale>ja</locale>
                <locale>nl</locale>
                <locale>pl</locale>
                <locale>pt_BR</locale>
                <locale>sv</locale>
                <locale>zh_CN</locale>
              </locales>
            </configuration>
          </plugin>
        </plugins>
      </reporting>
    </profile>
  </profiles>
</project>
