<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.codehaus.plexus</groupId>
    <artifactId>plexus</artifactId>
    <version>3.0.1</version>
  </parent>

  <groupId>org.codehaus.plexus</groupId>
  <artifactId>plexus-interpolation</artifactId>
  <version>1.15</version>

  <name>Plexus Interpolation API</name>

  <scm>
    <connection>scm:git:**************:sonatype/plexus-interpolation.git</connection>
    <developerConnection>scm:git:**************:sonatype/plexus-interpolation.git</developerConnection>
    <url>http://github.com/sonatype/plexus-interpolation</url>
  </scm>
  
  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>3.8.2</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
      
</project>
