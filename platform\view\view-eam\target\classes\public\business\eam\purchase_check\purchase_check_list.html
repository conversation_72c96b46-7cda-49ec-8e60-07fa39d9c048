<!--
/**
 * 采购验收 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2024-04-23 15:48:38
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
    <title th:text="${lang.translate('采购验收')}">采购验收</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 流程 , procId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 办理状态 , status ,typeName=text_input, isHideInSearch=true -->
                    <!-- 业务编号 , businessCode ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('业务编号')}" class="search-label businessCode-label">业务编号</span><span class="search-colon">:</span></div>
                        <input id="businessCode" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 名称 , name ,typeName=text_input, isHideInSearch=true -->
                    <!-- 采购申请 , applyId ,typeName=select_box, isHideInSearch=true -->
                    <!-- 供应商 , supplierId ,typeName=select_box, isHideInSearch=true -->
                    <!-- 验收人 , checkUserId ,typeName=button, isHideInSearch=true -->
                    <!-- 验收信息 , checkInformation ,typeName=text_area, isHideInSearch=true -->
                    <!-- 入库状态 , stockStatus ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 附件 , attach ,typeName=upload, isHideInSearch=true -->
                    <!-- 备注 , notes ,typeName=text_area, isHideInSearch=true -->
                    <!-- 制单人 , originatorId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 修改人ID , updateBy ,typeName=text_input, isHideInSearch=true -->
                    <!-- 选择数据 , selectedCode ,typeName=text_input, isHideInSearch=true -->
                    <!-- 采购单编号 , applyCode ,typeName=text_input, isHideInSearch=true -->
                    <!-- 采购单名称 , applyName ,typeName=text_input, isHideInSearch=true -->
                    <!-- 验收人 , checkUserName ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('验收人')}" class="search-label checkUserName-label">验收人</span><span class="search-colon">:</span></div>
                        <input id="checkUserName" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 仓库库位 , positionId ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:130px"><span th:text="${lang.translate('仓库库位')}" class="search-label positionId-label">仓库库位</span><span class="search-colon">:</span></div>
                        <div id="positionId" th:data="${'/service-eam/eam-warehouse-position/query-paged-list'}" style="width:180px" extraParam="{}"></div>
                    </div>
                    <!-- 是否入库 , insertPosition ,typeName=radio_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:130px"><span th:text="${lang.translate('是否入库')}" class="search-label insertPosition-label">是否入库</span><span class="search-colon">:</span></div>


                        <div id="insertPosition" th:data="${enum.toArray('com.dt.platform.constants.enums.common.YesNoEnum')}" style="width:180px"></div>
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 到货日期 , receiveDate ,typeName=date_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('到货日期')}" class="search-label receiveDate-label">到货日期</span><span class="search-colon">:</span></div>
                            <input type="text" id="receiveDate-begin" style="width: 180px" lay-verify="date" th:placeholder="${lang.translate('开始日期')}" autocomplete="off" class="layui-input search-input search-date-input"  readonly >
                            <span class="search-dash">-</span>
                            <input type="text" id="receiveDate-end"  style="width: 180px"  lay-verify="date" th:placeholder="${lang.translate('结束日期')}" autocomplete="off" class="layui-input search-input search-date-input" readonly>
                    </div>
                    <!-- 验收时间 , checkDate ,typeName=date_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('验收时间')}" class="search-label checkDate-label">验收时间</span><span class="search-colon">:</span></div>
                            <input type="text" id="checkDate-begin" style="width: 180px" lay-verify="date" th:placeholder="${lang.translate('开始日期')}" autocomplete="off" class="layui-input search-input search-date-input"  readonly >
                            <span class="search-dash">-</span>
                            <input type="text" id="checkDate-end"  style="width: 180px"  lay-verify="date" th:placeholder="${lang.translate('结束日期')}" autocomplete="off" class="layui-input search-input search-date-input" readonly>
                    </div>


                </div>
            </div>


            <!-- 按钮区域 -->
            <div id="search-area" class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索','','cmp:table.search')}">搜索</span></button>
            </div>
        </div>

        <div id="table-area" style="margin-top: 84px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
        <button th:if="${perm.checkAuth('eam_purchase_check:create')}" id="add-button" class="layui-btn icon-btn layui-btn-sm create-new-button " lay-event="create"><i class="layui-icon">&#xe654;</i><span th:text="${lang.translate('新建','','cmp:table.button')}">新建</span></button>
    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">

    <button th:if="${perm.checkAuth('eam_purchase_check:view-form')}" class="layui-btn layui-btn-primary layui-btn-xs ops-view-button " lay-event="view"  data-id="{{d.id}}"> <span th:text="${lang.translate('查看','','cmp:table.ops')}">查看</span></button>
    <button th:if="${perm.checkAnyAuth('eam_purchase_check:update','eam_purchase_check:save')}" class="layui-btn layui-btn-primary layui-btn-xs ops-edit-button " lay-event="edit"data-id="{{d.id}}"><span th:text="${lang.translate('修改','','cmp:table.ops')}">修改</span></button>


    <button th:if="${perm.checkAuth('eam_purchase_check:delete')}" class="layui-btn layui-btn-xs layui-btn-danger ops-delete-button " lay-event="del" data-id="{{d.id}}"><span th:text="${lang.translate('删除','','cmp:table.ops')}">删除</span></button>

    <button th:if="${perm.checkAuth('eam_asset_purchase_check:confirm')}"class="layui-btn layui-btn-xs  confirm-data-button " lay-event="confirm-data" data-id="{{d.id}}"><span th:text="${lang.translate('确认','','cmp:table.ops')}">确认</span></button>
    <button th:if="${perm.checkAuth('eam_asset_purchase_check:bill')}"class="layui-btn layui-btn-xs  download-bill-button " lay-event="download-bill" data-id="{{d.id}}"><span th:text="${lang.translate('单据','','cmp:table.ops')}">单据</span></button>

</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
    var RADIO_INSERTPOSITION_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.YesNoEnum')}]];
    var RADIO_STOCKSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.StockStatusSuccessFailedEnum')}]];
    var AUTH_PREFIX="eam_purchase_check";

    // pageType
    var PAGE_TYPE = [[${pageType}]] ;

</script>

<script th:src="'/business/eam/purchase_check/purchase_check_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/purchase_check/purchase_check_list.js?'+${cacheKey}"></script>

</body>
</html>