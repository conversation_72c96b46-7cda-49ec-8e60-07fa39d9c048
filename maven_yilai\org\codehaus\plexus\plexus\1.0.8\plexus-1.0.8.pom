<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.codehaus.plexus</groupId>
  <artifactId>plexus</artifactId>
  <packaging>pom</packaging>
  <name>Plexus</name>
  <version>1.0.8</version>
  <ciManagement>
    <notifiers>
      <notifier>
        <type>mail</type>
        <configuration>
          <address><EMAIL></address>
        </configuration>
      </notifier>
      <notifier>
        <type>irc</type>
        <configuration>
          <host>irc.codehaus.org</host>
          <port>6667</port>
          <channel>#plexus</channel>
        </configuration>
      </notifier>      
    </notifiers>
  </ciManagement>
  <inceptionYear>2001</inceptionYear>
  <mailingLists>
    <mailingList>
      <name>Plexus Developer List</name>
      <subscribe>http://lists.codehaus.org/mailman/listinfo/plexus-dev</subscribe>
      <unsubscribe>http://lists.codehaus.org/mailman/listinfo/plexus-dev</unsubscribe>
      <archive>http://lists.codehaus.org/pipermail/plexus-dev/</archive>
    </mailingList>
  </mailingLists>
  <issueManagement>
    <system>JIRA</system>
    <url>http://jira.codehaus.org/browse/PLX</url>
  </issueManagement>

  <distributionManagement>
    <repository>
      <id>codehaus.org</id>
      <name>Plexus Central Repository</name>
      <url>dav:https://dav.codehaus.org/repository/plexus</url>
    </repository>
    <snapshotRepository>
      <id>codehaus.org</id>
      <name>Plexus Central Development Repository</name>
      <url>dav:https://dav.codehaus.org/snapshots.repository/plexus</url>
    </snapshotRepository>
    <site>
      <id>codehaus.org</id>
      <url>dav:https://dav.codehaus.org/plexus</url>
    </site>
  </distributionManagement>
  <repositories>
    <repository>
      <id>apache-snapshots</id>
      <name>Snapshot repository</name>
      <url>http://people.apache.org/maven-snapshot-repository</url>
      <releases>
        <enabled>false</enabled>
      </releases>
    </repository>
    <repository>
      <id>codehaus-snapshots</id>
      <name>Codehaus Snapshot Development Repository</name>
      <url>http://snapshots.repository.codehaus.org</url>
      <releases>
        <enabled>false</enabled>
      </releases>
    </repository>
  </repositories>
  <pluginRepositories>
    <pluginRepository>
      <id>codehaus-snapshots</id>
      <name>Codehaus Snapshot Development Repository</name>
      <url>http://snapshots.repository.codehaus.org</url>
      <releases>
        <enabled>false</enabled>
      </releases>
    </pluginRepository>
  </pluginRepositories>

  <developers>
    <developer>
      <id>jvanzyl</id>
      <name>Jason van Zyl</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
        <role>Release Manager</role>
      </roles>
    </developer>
    <developer>
      <id>kaz</id>
      <name>Pete Kazmier</name>
      <email></email>
      <organization></organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>jtaylor</id>
      <name>James Taylor</name>
      <email><EMAIL></email>
      <organization></organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>dandiep</id>
      <name>Dan Diephouse</name>
      <email><EMAIL></email>
      <organization>Envoi solutions</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>kasper</id>
      <name>Kasper Nielsen</name>
      <email><EMAIL></email>
      <organization></organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>bwalding</id>
      <name>Ben Walding</name>
      <email><EMAIL></email>
      <organization>Walding Consulting Services</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>mhw</id>
      <name>Mark Wilkinson</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>michal</id>
      <name>Michal Maczka</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>evenisse</id>
      <name>Emmanuel Venisse</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Trygve Laugstol</name>
      <id>trygvis</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Kenney Westerhof</name>
      <id>kenney</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Carlos Sanchez</name>
      <id>carlos</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Brett Porter</name>
      <id>brett</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <name>John Casey</name>
      <id>jdcasey</id>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
  </developers>
  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>3.8.1</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <scm>
    <connection>scm:svn:http://svn.codehaus.org/plexus/trunk/</connection>
    <developerConnection>scm:svn:https://svn.codehaus.org/plexus/trunk</developerConnection>
  </scm>
  <organization>
    <name>Codehaus</name>
    <url>http://www.codehaus.org/</url>
  </organization>
  <modules>
    <!--
    
    Until the bug is fixed in Maven which is pulling in the trunk to the
    appserver build.
    <module>plexus-appserver</module>
    -->
    <module>plexus-archetypes</module>
    <module>plexus-examples</module>
    <module>plexus-components</module>
    <module>plexus-component-factories</module>
    <module>plexus-containers</module>
    <module>plexus-logging</module>
    <module>plexus-maven-plugin</module>
    <module>plexus-tools</module>
    <module>plexus-utils</module>
  </modules>
  <build>
    <extensions>
      <extension>
        <groupId>org.apache.maven.wagon</groupId>
        <artifactId>wagon-webdav</artifactId>
        <version>1.0-beta-1</version>
      </extension>
    </extensions>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-release-plugin</artifactId>
        <configuration>
          <tagBase>https://svn.codehaus.org/plexus/tags</tagBase>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
