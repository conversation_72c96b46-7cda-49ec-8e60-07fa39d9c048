<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <artifactId>plexus-components</artifactId>
    <groupId>org.codehaus.plexus</groupId>
    <version>1.1.19</version>
  </parent>

  <artifactId>plexus-io</artifactId>
  <version>2.0.2</version>

  <name>Plexus IO Components</name>

  <scm>
    <connection>scm:git:**************:sonatype/plexus-io.git</connection>
    <developerConnection>scm:git:**************:sonatype/plexus-io.git</developerConnection>
    <url>http://github.com/sonatype/plexus-io</url>
  </scm>
  <issueManagement>
    <system>jira</system>
    <url>http://jira.codehaus.org/browse/PLXCOMP/component/14319</url>
  </issueManagement>

  <dependencies>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-utils</artifactId>
      <version>3.0</version>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-container-default</artifactId>
      <scope>provided</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>2.3.1</version>
        <configuration>
          <source>1.5</source>
          <target>1.5</target>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-release-plugin</artifactId>
        <version>2.2.1</version>
      </plugin>
    </plugins>
  </build>
</project>
