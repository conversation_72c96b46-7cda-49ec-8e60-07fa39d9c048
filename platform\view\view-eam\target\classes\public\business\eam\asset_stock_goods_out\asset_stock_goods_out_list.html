<!--
/**
 * 库存出库 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2025-04-17 20:41:06
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
    <title th:text="${lang.translate('库存出库')}">库存出库</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 审批意见 , approvalOpinion ,typeName=text_input, isHideInSearch=true -->
                    <!-- 附件 , attachId ,typeName=upload, isHideInSearch=true -->
                    <!-- 变更ID , changeInstanceId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 变更状态 , chsStatus ,typeName=text_input, isHideInSearch=true -->
                    <!-- 变更类型 , chsType ,typeName=text_input, isHideInSearch=true -->
                    <!-- 变更版本号 , chsVersion ,typeName=text_input, isHideInSearch=true -->
                    <!-- 出库日期 , collectionDate ,typeName=date_input, isHideInSearch=true -->
                    <!-- 出库说明 , content ,typeName=text_area, isHideInSearch=true -->
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 最后审批人账户ID , latestApproverId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 最后审批人姓名 , latestApproverName ,typeName=text_input, isHideInSearch=true -->
                    <!-- 下一节点审批人 , nextApproverIds ,typeName=text_input, isHideInSearch=true -->
                    <!-- 下一个审批节点审批人姓名 , nextApproverNames ,typeName=text_input, isHideInSearch=true -->
                    <!-- 申请人 , originatorId ,typeName=button, isHideInSearch=true -->
                    <!-- 库存所属 , ownerType ,typeName=text_input, isHideInSearch=true -->
                    <!-- 出库位置 , positionDetail ,typeName=text_input, isHideInSearch=true -->
                    <!-- 流程 , procId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 选择数据 , selectedCode ,typeName=text_input, isHideInSearch=true -->
                    <!-- 办理状态 , status ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('办理状态')}" class="search-label status-label">办理状态</span><span class="search-colon">:</span></div>
                        <div id="status" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.AssetHandleStatusEnum')}" style="width:180px" extraParam="{}"></div>
                    </div>
                    <!-- 流程概要 , summary ,typeName=text_input, isHideInSearch=true -->
                    <!-- 清单台账 , toBook ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 修改人ID , updateBy ,typeName=text_input, isHideInSearch=true -->
                    <!-- 领用部门 , useOrganizationId ,typeName=button, isHideInSearch=true -->
                    <!-- 领用公司 , useOwnCompanyId ,typeName=button, isHideInSearch=true -->
                    <!-- 领用人员 , useUserId ,typeName=button, isHideInSearch=true -->
                    <!-- 仓库 , warehouseId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 业务编号 , businessCode ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('业务编号')}" class="search-label businessCode-label">业务编号</span><span class="search-colon">:</span></div>
                        <input id="businessCode" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 单据名称 , name ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('单据名称')}" class="search-label name-label">单据名称</span><span class="search-colon">:</span></div>
                        <input id="name" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 单据类型 , stockType ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('单据类型')}" class="search-label stockType-label">单据类型</span><span class="search-colon">:</span></div>
                        <div id="stockType" th:data="${'/service-system/sys-dict-item/query-paged-list?dictCode=eam_stock_out_type'}" style="width:180px" extraParam="{}"></div>
                    </div>
                    <!-- 业务日期 , businessDate ,typeName=date_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('业务日期')}" class="search-label businessDate-label">业务日期</span><span class="search-colon">:</span></div>
                            <input type="text" id="businessDate-begin" style="width: 180px" lay-verify="date" th:placeholder="${lang.translate('开始日期')}" autocomplete="off" class="layui-input search-input search-date-input"  readonly >
                            <span class="search-dash">-</span>
                            <input type="text" id="businessDate-end"  style="width: 180px"  lay-verify="date" th:placeholder="${lang.translate('结束日期')}" autocomplete="off" class="layui-input search-input search-date-input" readonly>
                    </div>


                </div>
            </div>


            <!-- 按钮区域 -->
            <div id="search-area" class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索','','cmp:table.search')}">搜索</span></button>
            </div>
        </div>

        <div id="table-area" style="margin-top: 84px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
        <button th:if="${perm.checkAuth('eam_asset_stock_goods_out:create')}" id="add-button" class="layui-btn icon-btn layui-btn-sm create-new-button " lay-event="create"><i class="layui-icon">&#xe654;</i><span th:text="${lang.translate('新建','','cmp:table.button')}">新建</span></button>
        <button id="action-import"  th:if="${perm.checkAuth('stock_goods_out:import')}"class="layui-btn icon-btn layui-btn-sm  for-import-button " lay-event="tool-action-import"><span th:text="${lang.translate('批量导入','','cmp:table.button')}">批量导入</span></button>
        <button id="action-detail"  th:if="${perm.checkAuth('eam_asset_stock_goods_out:detail')}"class="layui-btn icon-btn layui-btn-sm  for-detail-button " lay-event="tool-action-detail"><span th:text="${lang.translate('出库明细','','cmp:table.button')}">出库明细</span></button>
    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">

    <button th:if="${perm.checkAuth('eam_asset_stock_goods_out:view-form')}" class="layui-btn layui-btn-primary layui-btn-xs ops-view-button " lay-event="view"  data-id="{{d.id}}"> <span th:text="${lang.translate('查看','','cmp:table.ops')}">查看</span></button>
    <button th:if="${perm.checkAnyAuth('eam_asset_stock_goods_out:update','eam_asset_stock_goods_out:save')}" class="layui-btn layui-btn-primary layui-btn-xs ops-edit-button " lay-event="edit"data-id="{{d.id}}"><span th:text="${lang.translate('修改','','cmp:table.ops')}">修改</span></button>


    <button th:if="${perm.checkAuth('eam_asset_stock_goods_out:delete')}" class="layui-btn layui-btn-xs layui-btn-danger ops-delete-button " lay-event="del" data-id="{{d.id}}"><span th:text="${lang.translate('删除','','cmp:table.ops')}">删除</span></button>

    <button th:if="${perm.checkAuth('eam_asset_stock_goods_out:confirm')}"class="layui-btn layui-btn-xs  confirm-data-button " lay-event="confirm-data" data-id="{{d.id}}"><span th:text="${lang.translate('确认','','cmp:table.ops')}">确认</span></button>
    <button th:if="${perm.checkAuth('eam_asset_stock_goods_out:bill')}"class="layui-btn layui-btn-xs  download-bill-button " lay-event="download-bill" data-id="{{d.id}}"><span th:text="${lang.translate('单据','','cmp:table.ops')}">单据</span></button>
    <button th:if="${perm.checkAuth('eam_asset_stock_goods_out:spBook')}"class="layui-btn layui-btn-xs  sp-book-button " lay-event="to-sp-book" data-id="{{d.id}}"><span th:text="${lang.translate('转备件清单','','cmp:table.ops')}">转备件清单</span></button>

</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetHandleStatusEnum')}]];
    var RADIO_TOBOOK_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.YesNoEnum')}]];
    var AUTH_PREFIX="eam_asset_stock_goods_out";

    // OPER_TYPE
    var OPER_TYPE = [[${operType}]] ;
    // OWNER_TYPE
    var OWNER_TYPE = [[${ownerType}]] ;
    // APPROVAL_REQUIRED
    var APPROVAL_REQUIRED = [[${approvalRequired}]] ;

</script>

<script th:src="'/business/eam/asset_stock_goods_out/asset_stock_goods_out_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/asset_stock_goods_out/asset_stock_goods_out_list.js?'+${cacheKey}"></script>

</body>
</html>