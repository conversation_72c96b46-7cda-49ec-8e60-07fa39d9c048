<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.camunda</groupId>
    <artifactId>camunda-bpm-release-parent</artifactId>
    <version>2.0.0</version>
    <!-- do not remove empty tag - http://jira.codehaus.org/browse/MNG-4687 -->
    <relativePath />
  </parent>

  <groupId>org.camunda.commons</groupId>
  <artifactId>camunda-commons-bom</artifactId>
  <version>1.9.0</version>
  <name>camunda Commons - Bom</name>
  <packaging>pom</packaging>

  <dependencyManagement>
    <dependencies>

      <dependency>
        <groupId>org.camunda.commons</groupId>
        <artifactId>camunda-commons-logging</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>org.camunda.commons</groupId>
        <artifactId>camunda-commons-utils</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>org.camunda.commons</groupId>
        <artifactId>camunda-commons-testing</artifactId>
        <version>${project.version}</version>
      </dependency>

    </dependencies>
  </dependencyManagement>

  <scm>
    <url>https://github.com/camunda/camunda-commons</url>
    <connection>scm:git:**************:camunda/camunda-commons.git</connection>
    <developerConnection>scm:git:**************:camunda/camunda-commons.git</developerConnection>
    <tag>1.9.0</tag>
  </scm>

</project>
