<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 2010, 2020 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Public License v. 2.0, which is available at
    http://www.eclipse.org/legal/epl-2.0.

    This Source Code may also be made available under the following Secondary
    Licenses when the conditions for such availability set forth in the
    Eclipse Public License v. 2.0 are satisfied: GNU General Public License,
    version 2 with the GNU Classpath Exception, which is available at
    https://www.gnu.org/software/classpath/license.html.

    SPDX-License-Identifier: EPL-2.0 OR GPL-2.0 WITH Classpath-exception-2.0

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.eclipse.ee4j</groupId>
        <artifactId>project</artifactId>
        <version>1.0.5</version>
    </parent>

    <groupId>org.glassfish.jersey</groupId>
    <artifactId>project</artifactId>
    <packaging>pom</packaging>
    <version>2.30.1</version>
    <name>jersey</name>
    <description>
        Eclipse Jersey is the open source (under dual EPL+GPL license) JAX-RS 2.1 (JSR 370)
        production quality Reference Implementation for building RESTful Web services.
    </description>

    <url>https://projects.eclipse.org/projects/ee4j.jersey</url>

    <!-- prerequisites -->

    <issueManagement>
        <system>JIRA</system>
        <url>https://github.com/eclipse-ee4j/jersey/issues</url>
    </issueManagement>

    <ciManagement>
        <system>Hudson</system>
        <url>http://hudson.glassfish.org/job/Jersey-trunk-multiplatform/</url>
    </ciManagement>

    <inceptionYear>2010</inceptionYear>

    <mailingLists>
        <mailingList>
            <name>Users List</name>
            <post><EMAIL></post>
        </mailingList>
    </mailingLists>

    <developers>
        <developer>
            <name>Roman Grigoriadi</name>
            <organization>Oracle Corporation</organization>
            <organizationUrl>http://www.oracle.com/</organizationUrl>
        </developer>
        <developer>
            <name>Lukas Jungmann</name>
            <organization>Oracle Corporation</organization>
            <organizationUrl>http://www.oracle.com/</organizationUrl>
        </developer>
        <developer>
            <name>Dmitry Kornilov</name>
            <organization>Oracle Corporation</organization>
            <organizationUrl>http://www.oracle.com/</organizationUrl>
        </developer>
        <developer>
            <name>Tomas Kraus</name>
            <organization>Oracle Corporation</organization>
            <organizationUrl>http://www.oracle.com/</organizationUrl>
        </developer>
        <developer>
            <name>Tomas Langer</name>
            <organization>Oracle Corporation</organization>
            <organizationUrl>http://www.oracle.com/</organizationUrl>
        </developer>
        <developer>
            <name>Maxim Nesen</name>
            <organization>Oracle Corporation</organization>
            <organizationUrl>http://www.oracle.com/</organizationUrl>
        </developer>
        <developer>
            <name>Santiago Pericas-Geertsen</name>
            <organization>Oracle Corporation</organization>
            <organizationUrl>http://www.oracle.com/</organizationUrl>
        </developer>
        <developer>
            <name>Jan Supol</name>
            <organization>Oracle Corporation</organization>
            <organizationUrl>http://www.oracle.com/</organizationUrl>
            <url>http://blog.supol.info</url>
        </developer>
    </developers>

    <contributors>
        <contributor>
            <name>Petr Bouda</name>
        </contributor>
        <contributor>
            <name>Pavel Bucek</name>
            <organization>Oracle Corporation</organization>
            <url>http://u-modreho-kralika.net/</url>
        </contributor>
        <contributor>
            <name>Michal Gajdos</name>
            <url>http://blog.dejavu.sk</url>
        </contributor>
        <contributor>
            <name>Petr Janouch</name>
        </contributor>
        <contributor>
            <name>Libor Kramolis</name>
        </contributor>
        <contributor>
            <name>Adam Lindenthal</name>
        </contributor>
        <contributor>
            <name>Jakub Podlesak</name>
            <organization>Oracle Corporation</organization>
            <organizationUrl>http://www.oracle.com/</organizationUrl>
        </contributor>
        <contributor>
            <name>Marek Potociar</name>
        </contributor>
        <contributor>
            <name>Stepan Vavra</name>
        </contributor>
    </contributors>

    <licenses>
        <license>
            <name>EPL 2.0</name>
            <url>http://www.eclipse.org/legal/epl-2.0</url>
            <distribution>repo</distribution>
            <comments>Except for 3rd content and examples.
                      See also https://github.com/eclipse-ee4j/jersey/blob/master/NOTICE.md</comments>
        </license>
        <license>
            <name>GPL2 w/ CPE</name>
            <url>https://www.gnu.org/software/classpath/license.html</url>
            <distribution>repo</distribution>
            <comments>Except for 3rd content and examples.
                      See also https://github.com/eclipse-ee4j/jersey/blob/master/NOTICE.md</comments>
        </license>
        <license>
            <name>EDL 1.0</name>
            <url>http://www.eclipse.org/org/documents/edl-v10.php</url>
            <distribution>repo</distribution>
            <comments>The examples except bookstore-webapp example</comments>
        </license>
        <license>
            <name>BSD 2-Clause</name>
            <url>https://opensource.org/licenses/BSD-2-Clause</url>
            <distribution>repo</distribution>
            <comments>The bookstore-webapp example</comments>
        </license>
        <license>
            <name>Apache License, 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.html</url>
            <distribution>repo</distribution>
            <comments>Google Guava @ org.glassfish.jersey.internal.guava,
                      Dropwizard Monitoring inspired classes @ org.glassfish.jersey.server.internal.monitoring.core and
                      Jackson JAX-RS Providers @ org.glassfish.jersey.jackson.internal.jackson.jaxrs</comments>
        </license>
        <license>
            <name>Public Domain</name>
            <url>https://creativecommons.org/publicdomain/zero/1.0/</url>
            <distribution>repo</distribution>
            <comments>JSR-166 Extension to JEP 266 @ org.glassfish.jersey.internal.jsr166</comments>
        </license>
        <license>
            <name>Modified BSD</name>
            <url>http://asm.objectweb.org/license.html</url>
            <distribution>repo</distribution>
            <comments>ASM @ jersey.repackaged.org.objectweb.asm</comments>
        </license>
        <license>
            <name>jQuery license</name>
            <url>jquery.org/license</url>
            <distribution>repo</distribution>
            <comments>jQuery v1.12.4</comments>
        </license>
        <license>
            <name>MIT license</name>
            <url>http://www.opensource.org/licenses/mit-license.php</url>
            <distribution>repo</distribution>
            <comments>AngularJS, Bootstrap v3.3.7,
                      jQuery Barcode plugin 0.3, KineticJS v4.7.1</comments>
        </license>
        <license>
            <name>W3C license</name>
            <url>https://www.w3.org/Consortium/Legal/copyright-documents-19990405</url>
            <distribution>repo</distribution>
            <comments>Content of core-server/etc</comments>
        </license>
    </licenses>

    <scm>    
        <connection>scm:git:**************:jersey/jersey.git</connection>
        <developerConnection>scm:git:**************:eclipse-ee4j/jersey.git</developerConnection>
        <url>https://github.com/eclipse-ee4j/jersey</url>
        <tag>HEAD</tag>
    </scm>

    <organization>
        <name>Eclipse Foundation</name>
        <url>https://www.eclipse.org/org/foundation/</url>
    </organization>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.glassfish.jersey.tools.plugins</groupId>
                    <artifactId>jersey-doc-modulelist-maven-plugin</artifactId>
                    <version>1.0.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>3.0.0-M2</version>
                    <executions>
                        <execution>
                            <id>enforce-versions</id>
                            <goals>
                                <goal>enforce</goal>
                            </goals>
                            <configuration>
                                <rules>
                                    <requireJavaVersion>
                                        <version>${java.version}</version>
                                    </requireJavaVersion>
                                </rules>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>3.0.0</version>
                    <executions>
                        <execution>
                            <phase>generate-sources</phase>
                            <goals>
                                <goal>add-source</goal>
                            </goals>
                            <configuration>
                                <sources>
                                    <source>${project.build.directory}/generated-sources/rsrc-gen</source>
                                </sources>
                            </configuration>
                        </execution>
                        <execution>
                            <phase>initialize</phase>
                            <id>parse-version</id>
                            <goals>
                                <goal>parse-version</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>com.sun.istack</groupId>
                    <artifactId>istack-commons-maven-plugin</artifactId>
                    <version>3.0.8</version>
                    <executions>
                        <execution>
                            <phase>generate-sources</phase>
                            <goals>
                                <goal>rs-gen</goal>
                            </goals>
                            <configuration>
                                <resources>
                                    <directory>${basedir}/src/main/resources</directory>
                                    <includes>
                                        <include>**/localization.properties</include>
                                    </includes>
                                </resources>
                                <destDir>${project.build.directory}/generated-sources/rsrc-gen</destDir>
                                <localizationUtilitiesPkgName>org.glassfish.jersey.internal.l10n</localizationUtilitiesPkgName>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>2.5</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.0</version>
                    <inherited>true</inherited>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <compilerArguments>
                            <!--<Werror />-->
                            <!-- TODO work towards eliminating all warnings in order to be able to enable the -Xlint option -->
                            <!--Xlint/-->
                        </compilerArguments>
                        <showWarnings>false</showWarnings>
                        <fork>false</fork>
                        <excludes>
                            <exclude>module-info.java</exclude>
                        </excludes>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>2.4</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>2.5.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>2.6</version>
                    <extensions>true</extensions>
                    <!-- Add legal information, NOTICE.md and LINCENSE.md to jars -->
                    <executions>
                        <execution>
                            <!-- copy the files to classes folder for maven-jar/war-plugin to grab it -->
                            <id>copy-legaldocs</id>
                            <goals>
                                <goal>copy-resources</goal>
                            </goals>
                            <phase>process-sources</phase>
                            <configuration>
                                <outputDirectory>${project.build.outputDirectory}</outputDirectory>
                                <resources>
                                    <resource>
                                        <directory>${legal.source.folder}</directory>
                                        <targetPath>META-INF/</targetPath>
                                        <includes>
                                            <include>NOTICE.md</include>
                                            <include>LICENSE.md</include>
                                        </includes>
                                    </resource>
                                </resources>
                            </configuration>
                        </execution>
                        <execution>
                            <!-- copy the files to source folder for maven-source-plugin to grab it -->
                            <id>copy-legaldocs-to-sources</id>
                            <goals>
                                <goal>copy-resources</goal>
                            </goals>
                            <phase>process-sources</phase>
                            <configuration>
                                <outputDirectory>${project.build.directory}/generated-sources/rsrc-gen</outputDirectory>
                                <resources>
                                    <resource>
                                        <directory>${legal.source.folder}</directory>
                                        <targetPath>META-INF/</targetPath>
                                        <includes>
                                            <include>NOTICE.md</include>
                                            <include>LICENSE.md</include>
                                        </includes>
                                    </resource>
                                </resources>
                            </configuration>
                        </execution>
                        <execution>
                            <!-- copy the files to legal folder for felix plugin to grab it -->
                            <id>copy-legaldocs-to-osgi-bundles</id>
                            <goals>
                                <goal>copy-resources</goal>
                            </goals>
                            <phase>process-sources</phase>
                            <configuration>
                                <outputDirectory>${project.build.directory}/legal</outputDirectory>
                                <resources>
                                    <resource>
                                        <directory>${legal.source.folder}</directory>
                                        <targetPath>META-INF/</targetPath>
                                        <includes>
                                            <include>NOTICE.md</include>
                                            <include>LICENSE.md</include>
                                        </includes>
                                    </resource>
                                </resources>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${surefire.version}</version>
                    <configuration>
                        <!-- for convenience reasons, 'argLine' should not be overridden in child poms. if needed, a property should be declared and used here -->
                        <argLine>
                            -Xmx${surefire.maxmem.argline}m -Dfile.encoding=UTF8 ${surefire.security.argline} ${surefire.coverage.argline}
                        </argLine>
                        <skipTests>${skip.tests}</skipTests>
                    </configuration>
                    <dependencies>
                        <dependency>
                            <groupId>org.apache.maven.surefire</groupId>
                            <artifactId>surefire-logger-api</artifactId>
                            <version>3.0.0-M3</version>
                            <!-- to get around bug https://github.com/junit-team/junit5/issues/1367 -->
                            <optional>true</optional>
                        </dependency>
                        <dependency>
                            <groupId>org.apache.maven.surefire</groupId>
                            <artifactId>surefire-api</artifactId>
                            <version>3.0.0-M3</version>
                            <optional>true</optional>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>2.4</version>
                    <configuration>
                        <tarLongFileMode>gnu</tarLongFileMode>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>2.8</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>2.10.4</version>
                    <configuration>
                        <doctitle>Jersey ${jersey.version} API Documentation</doctitle>
                        <windowtitle>Jersey ${jersey.version} API</windowtitle>
                        <bottom>
                            <![CDATA[Copyright &#169; 2007-2019,
                                <a href="http://www.oracle.com">Oracle</a>
                                and/or its affiliates.
                                All Rights Reserved. Use is subject to license terms.]]>
                        </bottom>
                        <links>
                            <link>https://jax-rs.github.io/apidocs/2.1/</link>
                            <link>http://hk2.java.net/nonav/hk2-api/apidocs</link>
                        </links>
                        <excludePackageNames>
                            *.internal.*:*.tests.*
                        </excludePackageNames>
                        <sourceFileExcludes>
                            <exclude>bundles/**</exclude>
                            <fileExclude>module-info.java</fileExclude>
                        </sourceFileExcludes>
                        <verbose />
                        <additionalparam>-Xdoclint:none</additionalparam>
                        <maxmemory>256m</maxmemory>
                    </configuration>
                    <executions>
                        <execution>
                            <id>attach-javadocs</id>
                            <phase>package</phase>
                            <goals>
                                <goal>jar</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.0.1</version>
                    <executions>
                        <execution>
                            <id>attach-sources</id>
                            <phase>package</phase>
                            <goals>
                                <goal>jar-no-fork</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>2.8.1</version>
                    <configuration>
                        <retryFailedDeploymentCount>10</retryFailedDeploymentCount>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.ops4j.pax.exam</groupId>
                    <artifactId>maven-paxexam-plugin</artifactId>
                    <version>${paxexam.mvn.plugin.version}</version>
                    <executions>
                        <execution>
                            <id>generate-config</id>
                            <goals>
                                <goal>generate-depends-file</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <options>
                            <platform>felix</platform>
                        </options>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-site-plugin</artifactId>
                    <version>3.7.1</version>
                    <configuration>
                        <generateProjectInfo>false</generateProjectInfo>
                        <generateReports>true</generateReports>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>exec-maven-plugin</artifactId>
                    <version>1.2.1</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>java</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jxr-plugin</artifactId>
                    <version>2.3</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>jxr</goal>
                            </goals>
                            <phase>validate</phase>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-checkstyle-plugin</artifactId>
                    <version>${checkstyle.mvn.plugin.version}</version>
                    <configuration>
                        <configLocation>etc/config/checkstyle.xml</configLocation>
                        <suppressionsLocation>etc/config/checkstyle-suppressions.xml</suppressionsLocation>
                        <outputFile>${project.build.directory}/checkstyle/checkstyle-result.xml</outputFile>
                    </configuration>
                    <dependencies>
                        <dependency>
                            <groupId>com.puppycrawl.tools</groupId>
                            <artifactId>checkstyle</artifactId>
                            <version>${checkstyle.version}</version>
                            <exclusions>
                                <!-- MCHECKSTYLE-156 -->
                                <exclusion>
                                    <groupId>com.sun</groupId>
                                    <artifactId>tools</artifactId>
                                </exclusion>
                            </exclusions>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>findbugs-maven-plugin</artifactId>
                    <version>${findbugs.version}</version>
                    <configuration>
                        <skip>${findbugs.skip}</skip>
                        <threshold>${findbugs.threshold}</threshold>
                        <excludeFilterFile>${findbugs.exclude}</excludeFilterFile>
                        <xmlOutput>true</xmlOutput>
                        <findbugsXmlOutput>true</findbugsXmlOutput>
                        <!-- findbugs detector configuration -->
                        <jvmArgs>-Dfindbugs.glassfish.logging.validLoggerPrefixes=${findbugs.glassfish.logging.validLoggerPrefixes}</jvmArgs>
                        <plugins>
                            <plugin>
                                <groupId>org.glassfish.findbugs</groupId>
                                <artifactId>findbugs-logging-detectors</artifactId>
                                <version>${findbugs.glassfish.version}</version>
                            </plugin>
                        </plugins>
                    </configuration>
                    <dependencies>
                        <dependency>
                            <groupId>org.glassfish.findbugs</groupId>
                            <artifactId>findbugs</artifactId>
                            <version>${findbugs.glassfish.version}</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-failsafe-plugin</artifactId>
                    <version>3.0.0-M3</version>
                    <configuration>
                        <skipTests>${skip.tests}</skipTests>
                        <skipITs>${skip.tests}</skipITs>
                        <argLine>${failsafe.coverage.argline}</argLine>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>integration-test</goal>
                                <goal>verify</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-war-plugin</artifactId>
                    <version>2.4</version>
                    <configuration>
                        <failOnMissingWebXml>false</failOnMissingWebXml>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-ear-plugin</artifactId>
                    <version>2.8</version>
                </plugin>
                <plugin>
                    <groupId>org.glassfish.embedded</groupId>
                    <artifactId>maven-embedded-glassfish-plugin</artifactId>
                    <version>3.1.2.2</version>
                </plugin>
                <plugin>
                    <groupId>org.glassfish.copyright</groupId>
                    <artifactId>glassfish-copyright-maven-plugin</artifactId>
                    <version>2.0</version>
                    <configuration>
                        <excludeFile>etc/config/copyright-exclude</excludeFile>
                        <!--svn|mercurial|git - defaults to svn-->
                        <scm>git</scm>
                        <!-- turn on/off debugging -->
                        <debug>false</debug>
                        <!-- skip files not under SCM-->
                        <scmOnly>true</scmOnly>
                        <!-- turn off warnings -->
                        <warn>true</warn>
                        <!-- for use with repair -->
                        <update>false</update>
                        <!-- check that year is correct -->
                        <ignoreYear>false</ignoreYear>
                        <templateFile>etc/config/copyright.txt</templateFile>
                        <bsdTemplateFile>etc/config/edl-copyright.txt</bsdTemplateFile>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <version>3.5.0</version>
                    <extensions>true</extensions>
                    <configuration>
                        <instructions>
                            <_versionpolicy>[$(version;==;$(@)),$(version;+;$(@)))</_versionpolicy>
                            <_nodefaultversion>false</_nodefaultversion>
                            <Include-Resource>{maven-resources},${project.build.directory}/legal</Include-Resource>
                        </instructions>
                    </configuration>
                    <executions>
                        <execution>
                            <id>osgi-bundle</id>
                            <phase>package</phase>
                            <goals>
                                <goal>bundle</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>xml-maven-plugin</artifactId>
                    <version>1.0</version>
                </plugin>
                <plugin>
                    <groupId>com.sun.tools.xjc.maven2</groupId>
                    <artifactId>maven-jaxb-plugin</artifactId>
                    <version>1.1.1</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>buildnumber-maven-plugin</artifactId>
                    <version>1.1</version>
                </plugin>
                <!-- TODO: remove the old jetty plugin dependencies -->
                <plugin>
                    <groupId>org.mortbay.jetty</groupId>
                    <artifactId>maven-jetty-plugin</artifactId>
                    <version>${jetty.plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.mortbay.jetty</groupId>
                    <artifactId>jetty-maven-plugin</artifactId>
                    <version>8.1.8.v20121106</version>
                </plugin>
                <plugin>
                    <groupId>org.eclipse.jetty</groupId>
                    <artifactId>jetty-maven-plugin</artifactId>
                    <version>${jetty.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.glassfish.build</groupId>
                    <artifactId>gfnexus-maven-plugin</artifactId>
                    <version>0.16</version>
                    <configuration>
                        <stagingRepos>
                            <stagingRepo>
                                <!--
                                    The reference artifact used to identify the right staging repository
                                -->
                                <ref>org.glassfish.jersey:project:${project.version}:pom</ref>
                                <profile>com.sun.jersey</profile>
                            </stagingRepo>
                        </stagingRepos>
                        <!--
                            Temporary till there is a jersey promotion profile
                        -->
                        <promotionProfile>glassfish-integration</promotionProfile>
                        <message>JERSEY-${project.version}</message>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>2.4.3</version>
                    <executions>
                        <execution>
                            <id>shade-archive</id>
                            <goals>
                                <goal>shade</goal>
                            </goals>
                            <configuration>
                                <minimizeJar>false</minimizeJar>
                                <filters>
                                    <filter>
                                        <artifact>*:*</artifact>
                                        <excludes>
                                            <exclude>module-info.*</exclude>
                                        </excludes>
                                    </filter>
                                </filters>
                            </configuration>
                        </execution>
                    </executions>
                    <configuration>
                        <shadeTestJar>false</shadeTestJar>
                        <minimizeJar>true</minimizeJar>
                        <promoteTransitiveDependencies>true</promoteTransitiveDependencies>
                        <!-- Do not create reduced pom - jaxrs-ri cannot be built when set to true -->
                        <createDependencyReducedPom>false</createDependencyReducedPom>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <version>1.8</version>
                    <dependencies>
                        <dependency>
                            <groupId>org.apache.ant</groupId>
                            <artifactId>ant</artifactId>
                            <version>1.10.7</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.fortasoft</groupId>
                    <artifactId>gradle-maven-plugin</artifactId>
                    <version>1.0.5</version>
                </plugin>
                <plugin>
                    <groupId>com.github.wvengen</groupId>
                    <artifactId>proguard-maven-plugin</artifactId>
                    <version>2.0.8</version>
                    <dependencies>
                        <dependency>
                            <groupId>net.sf.proguard</groupId>
                            <artifactId>proguard-base</artifactId>
                            <version>5.1</version><!-- transitive dependency version increased from 5.0 -->
                            <scope>runtime</scope>
                        </dependency>
                    </dependencies>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.commonjava.maven.plugins</groupId>
                <artifactId>directory-maven-plugin</artifactId>
                <version>0.3.1</version>
                <executions>
                    <execution>
                        <id>directories</id>
                        <goals>
                            <goal>highest-basedir</goal>
                        </goals>
                        <phase>initialize</phase>
                        <configuration>
                            <property>legal.source.folder</property>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.glassfish.jersey.tools.plugins</groupId>
                <artifactId>jersey-doc-modulelist-maven-plugin</artifactId>
                <inherited>false</inherited>
                <configuration>
                    <outputFileName>docs/src/main/docbook/modules.xml</outputFileName>
                    <templateFileName>docs/src/main/docbook/inc/modules.src</templateFileName>
                    <tableHeaderFileName>docs/src/main/docbook/inc/modules_table_header.src</tableHeaderFileName>
                    <tableFooterFileName>docs/src/main/docbook/inc/modules_table_footer.src</tableFooterFileName>
                    <tableRowFileName>docs/src/main/docbook/inc/modules_table_row.src</tableRowFileName>
                    <outputUnmatched>false</outputUnmatched>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
            </plugin>
        </plugins>
        <extensions>
            <extension>
                <groupId>org.glassfish</groupId>
                <artifactId>findbugs</artifactId>
                <version>3.2-b06</version>
            </extension>
        </extensions>
    </build>

    <profiles>
        <profile>
            <id>jdk8</id>
            <activation>
                <jdk>1.8</jdk>
            </activation>
            <build>
                <pluginManagement>
                    <plugins>
                        <plugin>
                            <groupId>org.apache.maven.plugins</groupId>
                            <artifactId>maven-compiler-plugin</artifactId>
                            <inherited>true</inherited>
                            <configuration>
                                <source>${java.version}</source>
                                <target>${java.version}</target>
                                <excludes>
                                    <exclude>module-info.java</exclude>
                                </excludes>
                            </configuration>
                        </plugin>
                    </plugins>
                </pluginManagement>
            </build>
        </profile>
        <profile>
            <id>jdk11+</id>
            <!--
                JDK 9 & 10 is unsupported (as well as <release>9</release>)
                module-info for java.xml.bind is taken from JDK (lib/ct.sym/9-modules)
                and it depends on java.activation which clashes with javax.activation
            -->
            <activation>
                <jdk>[11,)</jdk>
            </activation>
            <dependencyManagement>
                <dependencies>
                    <dependency>
                        <groupId>com.sun.activation</groupId>
                        <artifactId>jakarta.activation</artifactId>
                        <version>${jakarta.activation.version}</version>
                    </dependency>
                </dependencies>
            </dependencyManagement>
            <build>
                <pluginManagement>
                    <plugins>
                        <plugin>
                            <groupId>org.apache.maven.plugins</groupId>
                            <artifactId>maven-compiler-plugin</artifactId>
                            <inherited>true</inherited>
                            <executions>
<!-- when module.info
                                <execution>
                                    <id>default-compile</id>
                                    <configuration>
                                        compile everything to ensure module-info contains right entries
                                        <release>11</release>
                                    </configuration>
                                </execution>
-->
                                <execution>
                                    <id>base-compile</id>
                                    <goals>
                                        <goal>compile</goal>
                                    </goals>
                                    <!-- recompile everything for target VM except the module-info.java -->
                                    <configuration>
                                        <excludes>
                                            <exclude>module-info.java</exclude>
                                        </excludes>
                                        <source>1.8</source>
                                        <target>1.8</target>
                                    </configuration>
                                </execution>
                            </executions>
                        </plugin>
                    </plugins>
                </pluginManagement>
            </build>
        </profile>
        <profile>
            <!-- Use it with release-perform goal to skip another test run. -->
            <id>testsSkip</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <!-- this is how to align back with maven standards where property 'skipTests' makes maven-surefire-plugin
                (and also maven-failsafe-plugin) skip tests execution -->
                <property>
                    <name>skipTests</name>
                    <value>true</value>
                </property>
            </activation>
            <properties>
                <release.tests.args>-Dskip.tests=true</release.tests.args>
                <skip.tests>true</skip.tests>
                <skip.e2e>true</skip.e2e>
            </properties>
        </profile>
        <profile>
            <id>checkstyleSkip</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <checkstyle.skip>true</checkstyle.skip>
            </properties>
        </profile>
        <profile>
            <id>findbugsSkip</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <findbugs.skip>true</findbugs.skip>
            </properties>
        </profile>
        <profile> <!-- tests module will be automatically included unless tests.excluded env variable is set -->
            <id>testsIncluded</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <property>
                  <name>!tests.excluded</name>
                </property>
            </activation>
            <modules>
                <module>tests</module>
            </modules>
        </profile>
        <profile> <!-- examples module will be automatically included unless examples.excluded env variable is set -->
            <id>examplesIncluded</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <property>
                    <name>!examples.excluded</name>
                </property>
            </activation>
            <modules>
                <module>examples</module>
            </modules>
        </profile>
        <profile> <!-- bundles module will be automatically included unless bundles.excluded env variable is set -->
            <id>bundlesIncluded</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <property>
                    <name>!bundles.excluded</name>
                </property>
            </activation>
            <modules>
                <module>bundles</module>
            </modules>
        </profile>
        <profile> <!-- test-framework module will be automatically included unless test-framework.excluded env variable is set -->
            <id>testFrameworkIncluded</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <property>
                    <name>!test-framework.excluded</name>
                </property>
            </activation>
            <modules>
                <module>test-framework</module>
            </modules>
        </profile>
        <profile>
            <id>pre-release</id>
            <modules>
                <module>docs</module>
            </modules>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-deploy-plugin</artifactId>
                        <configuration>
                            <skip>true</skip>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>xdk</id>
            <activation>
                <property>
                    <name>xdk</name>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <!-- add xdk sax parser to the classpath and exclude xerces -->
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <inherited>true</inherited>
                        <configuration>
                            <additionalClasspathElements>
                                <additionalClasspathElement>${xdk.absolute.path}</additionalClasspathElement>
                            </additionalClasspathElements>
                            <classpathDependencyExcludes>
                                <classpathDependencyExcludes>xerces:xercesImpl</classpathDependencyExcludes>
                            </classpathDependencyExcludes>
                        </configuration>
                    </plugin>
                    <plugin>
                        <!-- ensure, that the path to the xdk sax parser has been set -->
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-enforcer-plugin</artifactId>
                        <executions>
                            <execution>
                               <id>enforce-property</id>
                               <goals>
                                   <goal>enforce</goal>
                               </goals>
                                <configuration>
                                    <rules>
                                        <requireProperty>
                                            <property>xdk.absolute.path</property>
                                            <message>Property 'xdk.absolute.path' has to be specified.</message>
                                            <regex>.*/xmlparserv2.jar$</regex>
                                            <regexMessage>
                                                Property 'xdk.absolute.path' has to point to the xdk parser jar (xmlparserv2.jar).
                                            </regexMessage>
                                        </requireProperty>
                                    </rules>
                                    <fail>true</fail>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>moxy</id>
            <activation>
                <property>
                    <name>moxy</name>
                </property>
            </activation>
            <dependencies>
                <dependency>
                    <groupId>org.eclipse.persistence</groupId>
                    <artifactId>org.eclipse.persistence.moxy</artifactId>
                    <version>${moxy.version}</version>
                </dependency>
            </dependencies>
            <repositories>
                <repository>
                    <id>eclipselink.repository</id>
                    <name>Eclipse Maven Repository</name>
                    <url>https://www.eclipse.org/downloads/download.php?r=1&amp;nf=1&amp;file=/rt/eclipselink/maven.repo</url>
                    <layout>default</layout>
                </repository>
            </repositories>
        </profile>
        <profile>
            <id>securityOff</id>
            <properties>
                <surefire.security.argline />
            </properties>
        </profile>
        <profile>
            <id>project-info</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <reporting>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-project-info-reports-plugin</artifactId>
                        <version>2.7</version>
                        <reportSets>
                            <reportSet>
                                <reports>
                                    <report>dependencies</report>
                                </reports>
                            </reportSet>
                        </reportSets>
                    </plugin>
                </plugins>
            </reporting>
            <!-- placeholder required for site:stage -->
            <distributionManagement>
                <site>
                    <id>localhost</id>
                    <url>http://localhost</url>
                </site>
            </distributionManagement>
        </profile>
        <profile>
            <id>jdk1.7+</id>
            <activation>
                <jdk>[1.7,)</jdk>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-checkstyle-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>verify</id>
                                <phase>validate</phase>
                                <goals>
                                    <!-- Fail the build if checkstyle rules for contributions are not met. -->
                                    <goal>check</goal>
                                </goals>
                                <configuration>
                                    <configLocation>etc/config/checkstyle-verify.xml</configLocation>
                                    <consoleOutput>true</consoleOutput>
                                    <failOnViolation>true</failOnViolation>
                                    <includeTestSourceDirectory>true</includeTestSourceDirectory>
                                    <excludes>**/module-info.java</excludes>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>sonar</id>
            <properties>
                <!-- Sonar/Reporting settings (heavily inspired at http://www.aheritier.net/maven-failsafe-sonar-and-jacoco-are-in-a-boat/ -->

                <!-- Tells Sonar to use jacoco for coverage results -->
                <sonar.java.coveragePlugin>jacoco</sonar.java.coveragePlugin>

                <!-- Don't let Sonar execute tests. We will ask it to Maven -->
                <sonar.dynamicAnalysis>reuseReports</sonar.dynamicAnalysis>
                <!-- The system property jacoco.outputDir is based on. This won't work for 'sonar:sonar' (see http://jira.codehaus.org/browse/SONAR-3427), as such a property 'jacoco.outputDir' has to be explicitly specified from a command line -->
                <jacoco.outputDir>${session.executionRootDirectory}/target</jacoco.outputDir>
                <!-- Tells Sonar where the Jacoco coverage result file is located for Unit Tests -->
                <sonar.jacoco.reportPath>${jacoco.outputDir}/jacoco.exec</sonar.jacoco.reportPath>
                <!-- Tells Sonar where the Jacoco coverage result file is located for Integration Tests -->
                <sonar.jacoco.itReportPath>${jacoco.outputDir}/jacoco-it.exec</sonar.jacoco.itReportPath>

                <!-- Force JaCoCo to show 0% coverage if the report is missing -->
                <sonar.jacoco.reportMissing.force.zero>true</sonar.jacoco.reportMissing.force.zero>

                <!-- jacoco.agent.*.arg properties are dynamically generated by jacoco:prepare-agent (see bellow) -->
                <surefire.coverage.argline>${jacoco.agent.ut.arg}</surefire.coverage.argline>
                <failsafe.coverage.argline>${jacoco.agent.it.arg}</failsafe.coverage.argline>

                <!-- Maven versions -->
                <jacoco.version>0.7.4.201502262128</jacoco.version>
                <sonar-jacoco-listeners.version>3.2</sonar-jacoco-listeners.version>
                <sonar.version>2.6</sonar.version>
            </properties>
            <dependencies>
                <dependency>
                    <groupId>org.codehaus.sonar-plugins.java</groupId>
                    <artifactId>sonar-jacoco-listeners</artifactId>
                    <version>${sonar-jacoco-listeners.version}</version>
                    <scope>test</scope>
                </dependency>
            </dependencies>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.jacoco</groupId>
                        <artifactId>jacoco-maven-plugin</artifactId>
                        <version>${jacoco.version}</version>
                        <executions>
                            <!-- Prepares a variable, jacoco.agent.ut.arg, that contains the info to be passed to the JVM hosting the code being tested. -->
                            <execution>
                                <id>prepare-ut-agent</id>
                                <phase>process-test-classes</phase>
                                <goals>
                                    <goal>prepare-agent</goal>
                                </goals>
                                <configuration>
                                    <destFile>${sonar.jacoco.reportPath}</destFile>
                                    <propertyName>jacoco.agent.ut.arg</propertyName>
                                    <append>true</append>
                                </configuration>
                            </execution>
                            <!-- Prepares a variable, jacoco.agent.it.arg, that contains the info to be passed to the JVM hosting the code being tested. -->
                            <execution>
                                <id>prepare-it-agent</id>
                                <phase>pre-integration-test</phase>
                                <goals>
                                    <goal>prepare-agent</goal>
                                </goals>
                                <configuration>
                                    <destFile>${sonar.jacoco.itReportPath}</destFile>
                                    <propertyName>jacoco.agent.it.arg</propertyName>
                                    <append>true</append>
                                </configuration>
                            </execution>

                            <!-- Generate reports which may not be complete as the execution won't consider tests of projects that weren't executed during the build yet --><!-- The good thing is that with every module built, we're ensured that integrity of both jacoco .exec files was preserved -->
                            <execution>
                                <id>jacoco-report-unit-tests</id>
                                <phase>test</phase>
                                <goals>
                                    <goal>report</goal>
                                </goals>
                                <configuration>
                                    <!-- Sets the path to the file which contains the execution data. -->
                                    <dataFile>${sonar.jacoco.reportPath}</dataFile>
                                    <!-- Sets the output directory for the code coverage report. -->
                                    <outputDirectory>${project.build.directory}/jacoco</outputDirectory>
                                </configuration>
                            </execution>
                            <execution>
                                <id>jacoco-report-integration-tests</id>
                                <phase>post-integration-test</phase>
                                <goals>
                                    <goal>report-integration</goal>
                                </goals>
                                <configuration>
                                    <!-- Sets the path to the file which contains the execution data. -->
                                    <dataFile>${sonar.jacoco.itReportPath}</dataFile>
                                    <!-- Sets the output directory for the code coverage report. -->
                                    <outputDirectory>${project.build.directory}/jacoco-it</outputDirectory>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
                <pluginManagement>
                    <plugins>
                        <plugin>
                            <groupId>org.codehaus.mojo</groupId>
                            <artifactId>sonar-maven-plugin</artifactId>
                            <version>${sonar.version}</version>
                        </plugin>
                        <plugin>
                            <groupId>org.apache.maven.plugins</groupId>
                            <artifactId>maven-surefire-plugin</artifactId>
                            <configuration>
                                <!-- If possible (in some modules, this has to be disabled due to https://jira.sonarsource.com/browse/SONARJAVA-728), enable coverage details per JUnit test -->
                                <properties>
                                    <property>
                                        <name>listener</name>
                                        <value>org.sonar.java.jacoco.JUnitListener</value>
                                    </property>
                                </properties>
                            </configuration>
                        </plugin>
                        <plugin>
                            <groupId>org.apache.maven.plugins</groupId>
                            <artifactId>maven-failsafe-plugin</artifactId>
                            <configuration>
                                <!-- Enable coverage details per JUnit test -->
                                <properties>
                                    <property>
                                        <name>listener</name>
                                        <value>org.sonar.java.jacoco.JUnitListener</value>
                                    </property>
                                </properties>
                                <!-- Let's put failsafe reports with surefire to have access to tests failures/success reports in Sonar -->
                                <reportsDirectory>${project.build.directory}/surefire-reports</reportsDirectory>
                            </configuration>
                        </plugin>
                    </plugins>
                </pluginManagement>
            </build>
        </profile>
        <profile>
            <!--
            Profile is aimed to run the build on travis
            due to travis limitations for output (max 4MB) this profile is used along with grep which reduces
            the output.
            However some e2e tests produce output which is not grepped (thus is not visible) and run longer than
            10 minutes which results in the whole build is being murdered by Travis because of death suspection

            the whole build is run as clean install but excludes several e2e tests because of the not grepped output
            -->
            <id>travis_e2e_skip</id>
            <properties>
                <skip.e2e>true</skip.e2e>
            </properties>
        </profile>
        <profile>
            <!--
             Profile is aimed to run the build on travis
            due to travis limitations for output (max 4MB) this profile is used to run e2e tests only.

            the only thing which is happen using profile is run of e2e tests (with additional build)
            everything is already build using travis_e2e_skip profile

            the whole build is run as test -Ptravis_e2e
            -->
            <id>travis_e2e</id>
            <properties>
                <skip.e2e>false</skip.e2e>
                <skip.tests>true</skip.tests>
            </properties>
        </profile>
    </profiles>

    <reporting>
        <excludeDefaults>true</excludeDefaults>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>findbugs-maven-plugin</artifactId>
                <version>${findbugs.version}</version>
                <reportSets>
                    <reportSet>
                        <configuration>
                            <skip>${findbugs.skip}</skip>
                            <threshold>${findbugs.threshold}</threshold>
                            <excludeFilterFile>${findbugs.exclude}</excludeFilterFile>
                            <findbugsXmlWithMessages>true</findbugsXmlWithMessages>
                            <!-- findbugs detector configuration -->
                            <jvmArgs>-Dfindbugs.glassfish.logging.validLoggerPrefixes=${findbugs.glassfish.logging.validLoggerPrefixes}</jvmArgs>
                            <plugins>
                                <plugin>
                                    <groupId>org.glassfish.findbugs</groupId>
                                    <artifactId>findbugs-logging-detectors</artifactId>
                                    <version>${findbugs.glassfish.version}</version>
                                </plugin>
                            </plugins>
                        </configuration>
                        <reports>
                            <report>findbugs</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.10.4</version>
                <!-- Run this plugin report sets only in the main Jersey pom -->
                <inherited>false</inherited>
                <configuration>
                    <doctitle>Jersey ${jersey.version} API Documentation</doctitle>
                    <windowtitle>Jersey ${jersey.version} API</windowtitle>
                    <bottom>
                        <![CDATA[Copyright &#169; 2007-2017,
                            <a href="http://www.oracle.com">Oracle</a>
                            and/or its affiliates.
                            All Rights Reserved. Use is subject to license terms.]]>
                    </bottom>
                    <excludePackageNames>
                        com.sun.ws.rs.ext:*.examples.*:*.internal.*:*.tests.*
                    </excludePackageNames>
                    <links>
                        <link>https://jax-rs.github.io/apidocs/2.1</link>
                        <link>https://javaee.github.io/hk2/apidocs/</link>
                    </links>
                    <additionalparam>-Xdoclint:none</additionalparam>
                    <sourceFileExcludes>
                        <fileExclude>module-info.java</fileExclude>
                    </sourceFileExcludes>
                </configuration>
                <reportSets>
                    <reportSet>
                        <reports>
                            <report>aggregate</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>

            <!--plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-report-plugin</artifactId>
                <version>2.12</version>
            </plugin-->
            <!--plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>cobertura-maven-plugin</artifactId>
                <version>2.4</version>
                <configuration>
                    <formats>
                        <format>xml</format>
                        <format>html</format>
                    </formats>
                </configuration>
            </plugin-->

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jxr-plugin</artifactId>
                <version>2.3</version>
                <reportSets>
                    <reportSet>
                        <reports>
                            <report>jxr</report>
                        </reports>
                    </reportSet>
                    <reportSet>
                        <!-- Run this report set only in the main Jersey pom -->
                        <inherited>false</inherited>
                        <reports>
                            <report>aggregate</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>${checkstyle.mvn.plugin.version}</version>
                <configuration>
                    <configLocation>etc/config/checkstyle.xml</configLocation>
                    <suppressionsLocation>etc/config/checkstyle-suppressions.xml</suppressionsLocation>
                </configuration>
                <reportSets>
                    <reportSet>
                        <reports>
                            <report>checkstyle</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
        </plugins>
    </reporting>

    <modules>
        <module>archetypes</module>
        <module>bom</module>
        <module>connectors</module>
        <module>containers</module>

        <module>core-common</module>
        <module>core-server</module>
        <module>core-client</module>

        <module>ext</module>
        <module>incubator</module>
        <module>inject</module>
        <module>media</module>
        <module>security</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>jakarta.ws.rs</groupId>
                <artifactId>jakarta.ws.rs-api</artifactId>
                <version>${jaxrs.api.impl.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.annotation</groupId>
                <artifactId>jakarta.annotation-api</artifactId>
                <version>${javax.annotation.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.enterprise</groupId>
                <artifactId>cdi-api</artifactId>
                <version>${cdi.api.version}</version>
            </dependency>

            <dependency>
                <groupId>jakarta.transaction</groupId>
                <artifactId>jakarta.transaction-api</artifactId>
                <version>${jta.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>org.glassfish.hk2</groupId>
                <artifactId>hk2-locator</artifactId>
                <version>${hk2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.hk2</groupId>
                <artifactId>hk2-utils</artifactId>
                <version>${hk2.version}</version>
            </dependency>

            <dependency>
                <groupId>org.glassfish.hk2</groupId>
                <artifactId>hk2-api</artifactId>
                <version>${hk2.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.inject</groupId>
                        <artifactId>javax.inject</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.glassfish.hk2</groupId>
                <artifactId>osgi-resource-locator</artifactId>
                <version>1.0.3</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.main.hk2</groupId>
                <artifactId>hk2-config</artifactId>
                <version>${hk2.config.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.hk2.external</groupId>
                <artifactId>jakarta.inject</artifactId>
                <version>${hk2.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.inject</groupId>
                        <artifactId>javax.inject</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.glassfish.hk2.external</groupId>
                <artifactId>aopalliance-repackaged</artifactId>
                <version>${hk2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>${javassist.version}</version>
            </dependency>

            <dependency>
                <groupId>org.glassfish.grizzly</groupId>
                <artifactId>grizzly-http-server</artifactId>
                <version>${grizzly2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.grizzly</groupId>
                <artifactId>grizzly-http-servlet</artifactId>
                <version>${grizzly2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.grizzly</groupId>
                <artifactId>grizzly-websockets</artifactId>
                <version>${grizzly2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.grizzly</groupId>
                <artifactId>connection-pool</artifactId>
                <version>${grizzly2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.grizzly</groupId>
                <artifactId>grizzly-http-client</artifactId>
                <version>${grizzly.client.version}</version>
            </dependency>

            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>${netty.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
            </dependency>

            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-util</artifactId>
                <version>${jetty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-client</artifactId>
                <version>${jetty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-server</artifactId>
                <version>${jetty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-webapp</artifactId>
                <version>${jetty.version}</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-continuation</artifactId>
                <version>${jetty.version}</version>
            </dependency>

            <dependency>
                <groupId>org.simpleframework</groupId>
                <artifactId>simple-http</artifactId>
                <version>${simple.version}</version>
             </dependency>

            <dependency>
                <groupId>org.simpleframework</groupId>
                <artifactId>simple-transport</artifactId>
                <version>${simple.version}</version>
            </dependency>

            <dependency>
                <groupId>org.simpleframework</groupId>
                <artifactId>simple-common</artifactId>
                <version>${simple.version}</version>
            </dependency>

            <dependency>
                <groupId>org.codehaus.jettison</groupId>
                <artifactId>jettison</artifactId>
                <version>${jettison.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>stax</groupId>
                        <artifactId>stax-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>jakarta.xml.bind</groupId>
                <artifactId>jakarta.xml.bind-api</artifactId>
                <version>${jaxb.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-impl</artifactId>
                <version>${jaxb.ri.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-osgi</artifactId>
                <version>${jaxb.ri.version}</version>
            </dependency>

            <dependency>
                <groupId>org.eclipse.persistence</groupId>
                <artifactId>org.eclipse.persistence.moxy</artifactId>
                <version>${moxy.version}</version>
            </dependency>

            <dependency>
                <groupId>jakarta.persistence</groupId>
                <artifactId>jakarta.persistence-api</artifactId>
                <version>${javax.persistence.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>jakarta.ejb</groupId>
                <artifactId>jakarta.ejb-api</artifactId>
                <version>${ejb.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.jaxrs</groupId>
                <artifactId>jackson-jaxrs-json-provider</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.jaxrs</groupId>
                <artifactId>jackson-jaxrs-base</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.module</groupId>
                <artifactId>jackson-module-jaxb-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.codehaus.jackson</groupId>
                <artifactId>jackson-core-asl</artifactId>
                <version>${jackson1.version}</version>
            </dependency>

            <dependency>
                <groupId>org.codehaus.jackson</groupId>
                <artifactId>jackson-mapper-asl</artifactId>
                <version>${jackson1.version}</version>
            </dependency>

            <dependency>
                <groupId>org.codehaus.jackson</groupId>
                <artifactId>jackson-jaxrs</artifactId>
                <version>${jackson1.version}</version>
            </dependency>

            <dependency>
                <groupId>org.codehaus.jackson</groupId>
                <artifactId>jackson-xc</artifactId>
                <version>${jackson1.version}</version>
            </dependency>

            <dependency>
                <groupId>xerces</groupId>
                <artifactId>xercesImpl</artifactId>
                <version>${xerces.version}</version>
            </dependency>

            <dependency>
                <groupId>org.osgi</groupId>
                <artifactId>org.osgi.core</artifactId>
                <version>${osgi.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>org.osgi</groupId>
                <artifactId>org.osgi.compendium</artifactId>
                <version>${osgi.compendium.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>org.glassfish.main.ejb</groupId>
                <artifactId>ejb-container</artifactId>
                <version>${gf.impl.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.main.common</groupId>
                <artifactId>container-common</artifactId>
                <version>${gf.impl.version}</version>
            </dependency>

            <!-- HV OSGi dependencies. -->
            <dependency>
                <groupId>org.jboss.logging</groupId>
                <artifactId>jboss-logging</artifactId>
                <version>${jboss.logging.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml</groupId>
                <artifactId>classmate</artifactId>
                <version>${fasterxml.classmate.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.el</groupId>
                <artifactId>jakarta.el-api</artifactId>
                <version>${javax.el.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish</groupId>
                <artifactId>jakarta.el</artifactId>
                <version>${javax.el.impl.version}</version>
            </dependency>

            <dependency>
                <groupId>org.glassfish</groupId>
                <artifactId>jakarta.json</artifactId>
                <version>${jsonp.ri.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish</groupId>
                <artifactId>jsonp-jaxrs</artifactId>
                <version>${jsonp.jaxrs.version}</version>
            </dependency>

            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${validation.impl.version}</version>
            </dependency>

            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator-cdi</artifactId>
                <version>${validation.impl.version}</version>
            </dependency>

            <dependency>
                <groupId>org.ops4j.pax.web</groupId>
                <artifactId>pax-web-jetty-bundle</artifactId>
                <version>${pax.web.version}</version>
            </dependency>
            <dependency>
                <groupId>org.ops4j.pax.web</groupId>
                <artifactId>pax-web-extender-war</artifactId>
                <version>${pax.web.version}</version>
            </dependency>
            <dependency>
                <groupId>org.openjdk.jmh</groupId>
                <artifactId>jmh-core</artifactId>
                <version>${jmh.version}</version>
            </dependency>
            <dependency>
                <groupId>org.openjdk.jmh</groupId>
                <artifactId>jmh-generator-annprocess</artifactId>
                <version>${jmh.version}</version>
            </dependency>
            <dependency>
                <groupId>com.esotericsoftware</groupId>
                <artifactId>kryo</artifactId>
                <version>${kryo.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>1.2</version>
            </dependency>

            <!-- Weld -->
            <dependency>
                <groupId>org.jboss.weld.se</groupId>
                <artifactId>weld-se-core</artifactId>
                <version>${weld.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.weld.servlet</groupId>
                <artifactId>weld-servlet</artifactId>
                <version>${weld.version}</version>
            </dependency>

            <dependency>
                <groupId>jakarta.validation</groupId>
                <artifactId>jakarta.validation-api</artifactId>
                <version>${javax.validation.api.version}</version>
            </dependency>

            <!-- Test scope -->

            <dependency>
                <groupId>org.ops4j.pax.exam</groupId>
                <artifactId>pax-exam</artifactId>
                <version>${pax.exam.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.ops4j.pax.exam</groupId>
                <artifactId>pax-exam-junit4</artifactId>
                <version>${pax.exam.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.ops4j.pax.exam</groupId>
                <artifactId>pax-exam-container-forked</artifactId>
                <version>${pax.exam.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.ops4j.pax.exam</groupId>
                <artifactId>pax-exam-container-native</artifactId>
                <version>${pax.exam.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.ops4j.pax.exam</groupId>
                <artifactId>pax-exam-junit-extender-impl</artifactId>
                <version>1.2.4</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.ops4j.pax.exam</groupId>
                <artifactId>pax-exam-link-mvn</artifactId>
                <version>${pax.exam.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.mortbay.jetty</groupId>
                <artifactId>jetty</artifactId>
                <version>${jetty.plugin.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.mortbay.jetty</groupId>
                <artifactId>jetty-util</artifactId>
                <version>${jetty.plugin.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.mortbay.jetty</groupId>
                <artifactId>servlet-api-2.5</artifactId>
                <version>${jetty.servlet.api.25.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.12</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.testng</groupId>
                <artifactId>testng</artifactId>
                <version>6.9.6</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.hamcrest</groupId>
                <artifactId>hamcrest-library</artifactId>
                <version>${hamcrest.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.jmockit</groupId>
                <artifactId>jmockit</artifactId>
                <version>${jmockit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-all</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>xmlunit</groupId>
                <artifactId>xmlunit</artifactId>
                <version>${xmlunit.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.apache.felix</groupId>
                <artifactId>org.apache.felix.framework</artifactId>
                <!--5.2.0+ does not work with moxy osgi functional tests-->
                <version>5.0.1</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.apache.felix</groupId>
                <artifactId>org.apache.felix.eventadmin</artifactId>
                <version>1.2.2</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.apache.felix</groupId>
                <artifactId>org.apache.felix.framework.security</artifactId>
                <version>2.2.0</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>jakarta.json.bind</groupId>
                <artifactId>jakarta.json.bind-api</artifactId>
                <version>${jsonb.api.version}</version>
            </dependency>

            <dependency>
                <groupId>org.eclipse</groupId>
                <artifactId>yasson</artifactId>
                <version>${yasson.version}</version>
            </dependency>

            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-api</artifactId>
                <version>${opentracing.version}</version>
            </dependency>

            <dependency>
                <groupId>io.opentracing</groupId>
                <artifactId>opentracing-util</artifactId>
                <version>${opentracing.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <properties>
        <archetype.mvn.plugin.version>2.4</archetype.mvn.plugin.version>

        <findbugs.skip>false</findbugs.skip>
        <findbugs.threshold>Low</findbugs.threshold>
        <!-- the exclude file cannot be here directly, as FindBugs would interpret the path as relative to
        each module; the default exclude filter file is at etc/config/findbugs-exclude.xml -->
        <findbugs.exclude />
        <findbugs.glassfish.logging.validLoggerPrefixes>
            javax.enterprise
        </findbugs.glassfish.logging.validLoggerPrefixes>
        <java.version>1.8</java.version>
        <jersey.repackaged.prefix>jersey.repackaged</jersey.repackaged.prefix>
        <netbeans.hint.license>gf-cddl-gpl</netbeans.hint.license>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <release.tests.args>-Dmaven.test.skip=false</release.tests.args>
        <release.preparationGoals>clean install</release.preparationGoals>
        <skip.tests>false</skip.tests>
        <xdk.absolute.path />
        <surefire.security.argline />
        <surefire.coverage.argline />
        <surefire.maxmem.argline>1024</surefire.maxmem.argline>
        <failsafe.coverage.argline />
        <server.coverage.argline>${failsafe.coverage.argline}</server.coverage.argline>


        <!-- Dependency versions -->
        <jersey.version>${project.version}</jersey.version>
        <!-- asm is now source integrated - keeping this property to see the version -->
        <!-- see core-server/src/main/java/jersey/repackaged/asm/.. -->
        <asm.version>7.2</asm.version>
        <bnd.plugin.version>2.3.6</bnd.plugin.version>
        <cdi.api.version>1.1</cdi.api.version>
        <commons-lang3.version>3.3.2</commons-lang3.version>
        <config.version>1.2.1</config.version>
        <checkstyle.mvn.plugin.version>3.1.0</checkstyle.mvn.plugin.version>
        <checkstyle.version>8.28</checkstyle.version>
        <easymock.version>3.3</easymock.version>
        <ejb.version>3.2.5</ejb.version>
        <gf.impl.version>5.1.0-RC2</gf.impl.version>
        <fasterxml.classmate.version>1.3.3</fasterxml.classmate.version>
        <findbugs.glassfish.version>1.7</findbugs.glassfish.version>
        <findbugs.version>3.0.4</findbugs.version>
        <freemarker.version>2.3.27-incubating</freemarker.version>
        <gae.version>1.9.59</gae.version>
        <grizzly.client.version>1.16</grizzly.client.version>
        <grizzly2.version>2.4.4</grizzly2.version>
        <guava.version>18.0</guava.version>
        <hamcrest.version>1.3</hamcrest.version>
        <helidon.version>1.0.3</helidon.version>
        <xmlunit.version>1.6</xmlunit.version>
        <hk2.version>2.6.1</hk2.version>
        <hk2.osgi.version>org.glassfish.hk2.*;version="[2.5,4)"</hk2.osgi.version>
        <hk2.jvnet.osgi.version>org.jvnet.hk2.*;version="[2.5,4)"</hk2.jvnet.osgi.version>
        <hk2.config.version>5.1.0</hk2.config.version>
        <httpclient.version>4.5.9</httpclient.version>
        <istack.commons.runtime.version>3.0.8</istack.commons.runtime.version>
        <jackson.version>2.10.1</jackson.version>
        <jackson1.version>1.9.13</jackson1.version>
        <jakarta.activation.version>1.2.1</jakarta.activation.version>
        <javassist.version>3.25.0-GA</javassist.version>
        <javax.annotation.osgi.version>javax.annotation.*;version="[1.2,3)"</javax.annotation.osgi.version>
        <javax.annotation.version>1.3.5</javax.annotation.version>
        <javax.el.version>3.0.3</javax.el.version>
        <javax.el.impl.version>3.0.2</javax.el.impl.version>
        <javax.interceptor.version>1.2.5</javax.interceptor.version>
        <javax.persistence.version>2.2.3</javax.persistence.version>
        <javax.validation.api.version>2.0.2</javax.validation.api.version>
        <jaxb.api.version>2.3.2</jaxb.api.version>
        <jaxb.ri.version>2.3.2</jaxb.ri.version>
        <jsonb.api.version>1.0.2</jsonb.api.version>
        <jaxrs.api.spec.version>2.1</jaxrs.api.spec.version>
        <jaxrs.api.impl.version>2.1.6</jaxrs.api.impl.version>
        <jboss.logging.version>3.3.0.Final</jboss.logging.version>
        <jersey1.version>1.19.3</jersey1.version>
        <jersey1.last.final.version>${jersey1.version}</jersey1.last.final.version>
        <jettison.version>1.3.7</jettison.version> <!-- TODO: 1.3.8 doesn't work; AbstractJsonTest complexBeanWithAttributes -->
        <jetty.plugin.version>6.1.26</jetty.plugin.version>
        <jetty.version>9.4.17.v20190418</jetty.version>
        <jetty.servlet.api.25.version>6.1.14</jetty.servlet.api.25.version>
        <jmh.version>1.10.2</jmh.version>
        <jmockit.version>1.44</jmockit.version>
        <jsonp.ri.version>1.1.5</jsonp.ri.version>
        <jsonp.jaxrs.version>1.1.5</jsonp.jaxrs.version>
        <jsp.version>2.3.6</jsp.version>
        <jstl.version>1.2.7</jstl.version>
        <jta.api.version>1.3.3</jta.api.version>
        <kryo.version>4.0.1</kryo.version>
        <mimepull.version>1.9.11</mimepull.version>
        <mockito.version>1.10.19</mockito.version>
        <moxy.version>2.7.4</moxy.version>
        <mustache.version>0.8.17</mustache.version>
        <netty.version>4.1.43.Final</netty.version>
        <nexus-staging.mvn.plugin.version>1.6.7</nexus-staging.mvn.plugin.version>
        <opentracing.version>0.30.0</opentracing.version>
        <osgi.version>6.0.0</osgi.version>
        <osgi.compendium.version>5.0.0</osgi.compendium.version>
        <pax.exam.version>4.9.1</pax.exam.version>
        <pax.web.version>0.7.4</pax.web.version><!-- TODO: UPGRADE! -->
        <paxexam.mvn.plugin.version>1.2.4</paxexam.mvn.plugin.version>
        <rxjava.version>1.2.5</rxjava.version>
        <rxjava2.version>2.0.4</rxjava2.version>
        <servlet2.version>2.4</servlet2.version>
        <servlet3.version>3.0.1</servlet3.version>
        <servlet4.version>4.0.3</servlet4.version>
        <simple.version>6.0.1</simple.version>
        <skip.e2e>false</skip.e2e>
        <slf4j.version>1.7.21</slf4j.version>
        <spring4.version>4.3.20.RELEASE</spring4.version>
        <spring5.version>5.1.5.RELEASE</spring5.version>
        <surefire.version>3.0.0-M3</surefire.version>
        <validation.impl.version>6.0.17.Final</validation.impl.version>
        <weld.version>2.2.14.Final</weld.version> <!-- 2.4.1 doesn't work - bv tests -->
        <weld3.version>3.0.0.Final</weld3.version>
        <xerces.version>2.11.0</xerces.version>
        <yasson.version>1.0.3</yasson.version>
    </properties>
</project>
