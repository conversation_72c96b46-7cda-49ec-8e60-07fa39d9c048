<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache</groupId>
    <artifactId>apache</artifactId>
    <version>4</version>
    <relativePath>../asf/pom.xml</relativePath>
  </parent>

  <groupId>org.apache.maven</groupId>
  <artifactId>maven-parent</artifactId>
  <version>8</version>
  <packaging>pom</packaging>

  <name>Apache Maven</name>
  <description>Maven is a software project management and comprehension tool. Based on the concept of a project object model (POM), Maven can manage a project's build, reporting and documentation from a central piece of information.</description>
  <url>http://maven.apache.org/</url>

  <ciManagement>
    <system>continuum</system>
    <url>http://maven.zones.apache.org/continuum</url>
    <notifiers>
      <notifier>
        <type>mail</type>
        <configuration>
          <address><EMAIL></address>
        </configuration>
      </notifier>
    </notifiers>
  </ciManagement>

  <inceptionYear>2002</inceptionYear>

  <mailingLists>
    <mailingList>
      <name>Maven Announcements List</name>
      <post><EMAIL></post>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-announce/</archive>
      <otherArchives>
        <otherArchive>http://www.mail-archive.com/<EMAIL></otherArchive>
        <otherArchive>http://www.nabble.com/Maven-Announcements-f15617.html</otherArchive>
        <otherArchive>http://maven.announce.markmail.org/</otherArchive>
      </otherArchives>
    </mailingList>
    <mailingList>
      <name>Maven Notifications List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://mail-archives.apache.org/mod_mbox/maven-notifications/</archive>
      <otherArchives>
        <otherArchive>http://www.mail-archive.com/<EMAIL></otherArchive>
        <otherArchive>http://www.nabble.com/Maven---Notifications-f15574.html</otherArchive>
        <otherArchive>http://maven.notifications.markmail.org/</otherArchive>
      </otherArchives>
    </mailingList>
  </mailingLists>

  <distributionManagement>
    <repository>
      <id>maven.staging</id>
      <url>scp://people.apache.org/www/people.apache.org/builds/maven/${project.version}/staging-repo</url>
    </repository>
    <snapshotRepository>
      <id>apache.snapshots</id>
      <name>${distMgmtSnapshotsName}</name>
      <url>${distMgmtSnapshotsUrl}</url>
    </snapshotRepository>
    <site>
      <id>apache.website</id>
      <url>scp://people.apache.org/www/maven.apache.org</url>
    </site>
  </distributionManagement>

  <build>
    <plugins>
      <!-- We want to package up license resources in the JARs produced -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-remote-resources-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>process</goal>
            </goals>
            <configuration>
              <resourceBundles>
                <resourceBundle>org.apache:apache-jar-resource-bundle:1.3</resourceBundle>
              </resourceBundles>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
    <pluginManagement>
      <plugins>
        <!-- set versions of common plugins for reproducibility, ordered alphabetically -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>2.0.2</version>
          <configuration>
            <source>1.4</source>
            <target>1.4</target>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>2.3</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-install-plugin</artifactId>
          <version>2.2</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-gpg-plugin</artifactId>
          <version>1.0-alpha-4</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>2.2</version>
          <configuration>
            <archive>
              <manifest>
                <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
                <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
              </manifest>
            </archive>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>2.3</version>
        </plugin>
        <!-- START SNIPPET: release-plugin-configuration -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <version>2.0-beta-7</version>
          <configuration>
            <!-- This element will be overriden by children -->
            <tagBase>https://svn.apache.org/repos/asf/maven/pom/tags</tagBase>
            <useReleaseProfile>false</useReleaseProfile>
            <goals>deploy</goals>
            <arguments>-Prelease</arguments>
          </configuration>
        </plugin>
        <!-- END SNIPPET: release-plugin-configuration -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-remote-resources-plugin</artifactId>
          <version>1.0-beta-2</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <version>2.0-beta-6</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>2.0.4</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.4.2</version>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

  <profiles>
    <profile>
      <id>ci</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-pmd-plugin</artifactId>
            <executions>
              <execution>
                <goals>
                  <goal>cpd-check</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>reporting</id>
      <reporting>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-report-plugin</artifactId>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-checkstyle-plugin</artifactId>
            <configuration>
              <configLocation>http://svn.apache.org/repos/asf/maven/plugins/trunk/maven-checkstyle-plugin/src/main/resources/config/maven_checks.xml</configLocation>
              <headerLocation>http://svn.apache.org/repos/asf/maven/plugins/trunk/maven-checkstyle-plugin/src/main/resources/config/maven-header.txt</headerLocation>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-pmd-plugin</artifactId>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>cobertura-maven-plugin</artifactId>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>taglist-maven-plugin</artifactId>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-jxr-plugin</artifactId>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <configuration>
              <links>
                <link>http://java.sun.com/j2se/1.4.2/docs/api</link>
                <link>http://java.sun.com/j2ee/1.4/docs/api</link>
                <link>http://java.sun.com/j2se/1.5.0/docs/api</link>
                <link>http://commons.apache.org/collections/apidocs-COLLECTIONS_3_0/</link>
                <link>http://commons.apache.org/dbcp/apidocs/</link>
                <link>http://commons.apache.org/fileupload/apidocs/</link>
                <link>http://commons.apache.org/httpclient/apidocs/</link>
                <link>http://commons.apache.org/logging/apidocs/</link>
                <link>http://commons.apache.org/pool/apidocs/</link>
                <link>http://junit.sourceforge.net/javadoc/</link>
                <link>http://logging.apache.org/log4j/1.2/apidocs/</link>
                <link>http://jakarta.apache.org/regexp/apidocs/</link>
                <link>http://velocity.apache.org/engine/releases/velocity-1.5/apidocs/</link>
              </links>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>clirr-maven-plugin</artifactId>
          </plugin>
        </plugins>
      </reporting>
    </profile>
    <!-- START SNIPPET: release-profile -->
    <profile>
      <id>release</id>
      <build>
        <plugins>
          <!-- We want to sign the artifact, the POM, and all attached artifacts -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <configuration>
              <passphrase>${gpg.passphrase}</passphrase>
            </configuration>
            <executions>
              <execution>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <!-- We want to deploy the artifact to a staging location for perusal -->
          <plugin>
            <inherited>true</inherited>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-deploy-plugin</artifactId>
            <configuration>
              <altDeploymentRepository>${deploy.altRepository}</altDeploymentRepository>
              <updateReleaseInfo>true</updateReleaseInfo>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-sources</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <!-- END SNIPPET: release-profile -->
  </profiles>

  <scm>
    <connection>scm:svn:http://svn.apache.org/repos/asf/maven/pom/tags/maven-parent-8</connection>
    <developerConnection>scm:svn:https://svn.apache.org/repos/asf/maven/pom/tags/maven-parent-8</developerConnection>
    <url>http://svn.apache.org/viewvc/maven/pom/tags/maven-parent-8</url>
  </scm>

  <!-- Developers listed by PMC Chair, PMC, Committers, Contributers, all alphabetical-->
  <developers>
    <developer>
      <id>jvanzyl</id>
      <name>Jason van Zyl</name>
      <email><EMAIL></email>
      <organization>ASF</organization>
      <roles>
        <role>PMC Chair</role>
      </roles>
      <timezone>-5</timezone>
    </developer>
    <developer>
      <id>aheritier</id>
      <name>Arnaud Heritier</name>
      <email><EMAIL></email>
      <organization>OCTO Technology</organization>
      <organizationUrl>http://www.octo.com</organizationUrl>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <id>brett</id>
      <name>Brett Porter</name>
      <email><EMAIL></email>
      <organization>ASF</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>+10</timezone>
    </developer>
    <developer>
      <id>brianf</id>
      <name>Brian Fox</name>
      <email><EMAIL></email>
      <organization>ASF</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>-5</timezone>
    </developer>
    <developer>
      <id>carlos</id>
      <name>Carlos Sanchez</name>
      <email><EMAIL></email>
      <organization>ASF</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <id>dennisl</id>
      <name>Dennis Lundberg</name>
      <email><EMAIL></email>
      <organization>ASF</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <id>dfabulich</id>
      <name>Daniel Fabulich</name>
      <email><EMAIL></email>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>-8</timezone>
    </developer>
    <developer>
      <id>evenisse</id>
      <name>Emmanuel Venisse</name>
      <email><EMAIL></email>
      <organization>ASF</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <id>fgiust</id>
      <name>Fabrizio Giustina</name>
      <email><EMAIL></email>
      <organization>openmind</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <id>jdcasey</id>
      <name>John Casey</name>
      <email><EMAIL></email>
      <organization>ASF</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>-5</timezone>
    </developer>
    <developer>
      <id>joakime</id>
      <name>Joakim Erdfelt</name>
      <email><EMAIL></email>
      <organization>ASF</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>-5</timezone>
    </developer>
    <developer>
      <id>jstrachan</id>
      <name>James Strachan</name>
      <roles>
        <role>PMC Member</role>
      </roles>
    </developer>
    <developer>
      <id>jtolentino</id>
      <name>Ernesto Tolentino Jr.</name>
      <email><EMAIL></email>
      <organization>ASF</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>+8</timezone>
    </developer>
    <developer>
      <id>jmcconnell</id>
      <name>Jesse McConnell</name>
      <email><EMAIL></email>
      <organization>ASF</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>-6</timezone>
    </developer>
    <developer>
      <id>kenney</id>
      <name>Kenney Westerhof</name>
      <email><EMAIL></email>
      <organization>Neonics</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <id>ltheussl</id>
      <name>Lukas Theussl</name>
      <email><EMAIL></email>
      <organization>ASF</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <id>mperham</id>
      <name>Mike Perham</name>
      <email><EMAIL></email>
      <organization>IBM</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>-6</timezone>
    </developer>
    <developer>
      <id>olamy</id>
      <name>Olivier Lamy</name>
      <email><EMAIL></email>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>+1</timezone>
    </developer>    
    <developer>
      <id>snicoll</id>
      <name>Stephane Nicoll</name>
      <email><EMAIL></email>
      <organization>ASF</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <id>trygvis</id>
      <name>Trygve Laugstol</name>
      <email><EMAIL></email>
      <organization>ASF</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <id>vmassol</id>
      <name>Vincent Massol</name>
      <email><EMAIL></email>
      <organization>ASF</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <id>vsiveton</id>
      <name>Vincent Siveton</name>
      <email><EMAIL></email>
      <organization>ASF</organization>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>-5</timezone>
    </developer>
    <developer>
      <id>wsmoak</id>
      <name>Wendy Smoak</name>
      <email><EMAIL></email>
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>-7</timezone>
    </developer>
    <!--Committers-->
    <developer>
      <id>aramirez</id>
      <name>Allan Q. Ramirez</name>
      <roles>
        <role>Committer</role>
      </roles>
    </developer>
    <developer>
      <id>baerrach</id>
      <name>Barrie Treloar</name>
      <roles>
        <role>Committer</role>
      </roles>
    </developer>
    <developer>
      <id>bayard</id>
      <name>Henri Yandell</name>
      <roles>
        <role>Committer</role>
      </roles>
    </developer>
    <developer>
      <id>bellingard</id>
      <name>Fabrice Bellingard</name>
      <roles>
        <role>Committer</role>
      </roles>
    </developer>
    <developer>
      <id>chrisjs</id>
      <name>Chris Stevenson</name>
      <roles>
        <role>Committer</role>
      </roles>
    </developer>
    <developer>
      <id>dantran</id>
      <name>Dan Tran</name>
      <roles>
        <role>Committer</role>
      </roles>
    </developer>
    <developer>
      <id>dblevins</id>
      <name>David Blevins</name>
      <roles>
        <role>Committer</role>
      </roles>
    </developer>
    <developer>
      <id>dkulp</id>
      <name>Daniel Kulp</name>
      <email><EMAIL></email>
      <organization>IONA</organization>
      <roles>
        <role>Committer</role>
      </roles>
      <timezone>-5</timezone>
    </developer>
    <developer>
      <id>dlr</id>
      <name>Daniel Rall</name>
      <roles>
        <role>Committer</role>
      </roles>
    </developer>
    <developer>
      <id>epunzalan</id>
      <name>Edwin Punzalan</name>
      <email><EMAIL></email>
      <roles>
        <role>Committer</role>
      </roles>
      <timezone>-8</timezone>
    </developer>
    <developer>
      <id>felipeal</id>
      <name>Felipe Leme</name>
      <roles>
        <role>Committer</role>
      </roles>
    </developer>
    <developer>
      <id>handyande</id>
      <name>Andrew Williams</name>
      <email><EMAIL></email>
      <roles>
        <role>Committer</role>
      </roles>
      <timezone>0</timezone>
    </developer>
    <developer>
      <id>hboutemy</id>
      <name>Herve Boutemy</name>
      <roles>
        <role>Committer</role>
      </roles>
    </developer>
    <developer>
      <id>jjensen</id>
      <name>Jeff Jensen</name>
      <roles>
        <role>Committer</role>
      </roles>
    </developer>
    <developer>
      <id>mkleint</id>
      <name>Milos Kleint</name>
      <roles>
        <role>Committer</role>
      </roles>
    </developer>
    <developer>
      <id>nicolas</id>
      <name>Nicolas De Loof</name>
      <email><EMAIL></email>
      <organization>Capgemini</organization>
      <roles>
        <role>Committer</role>
      </roles>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <id>oching</id>
      <name>Maria Odea B. Ching</name>
      <roles>
        <role>Committer</role>
      </roles>
    </developer>
    <developer>
      <id>pschneider</id>
      <name>Patrick Schneider</name>
      <email><EMAIL></email>
      <roles>
        <role>Committer</role>
      </roles>
      <timezone>-6</timezone>
    </developer>
    <developer>
      <id>rafale</id>
      <name>Raphaël Piéroni</name>
      <email><EMAIL></email>
      <organization>Dexem</organization>
      <roles>
        <role>Committer</role>
      </roles>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <id>rgoers</id>
      <name>Ralph Goers</name>
      <roles>
        <role>Committer</role>
      </roles>
    </developer>
    <developer>
      <id>rinku</id>
      <name>Rahul Thakur</name>
      <roles>
        <role>Committer</role>
      </roles>
    </developer>
    <developer>
      <id>shinobu</id>
      <name>Shinobu Kuwai</name>
      <roles>
        <role>Committer</role>
      </roles>
    </developer>
    <developer>
      <id>smorgrav</id>
      <name>Torbjorn Eikli Smorgrav</name>
      <roles>
        <role>Committer</role>
      </roles>
    </developer>
    <!--End Committers-->
  </developers>

  <properties>
    <distMgmtSnapshotsName>Apache Development Snapshot Repository</distMgmtSnapshotsName>
    <distMgmtSnapshotsUrl>scp://people.apache.org/www/people.apache.org/repo/m2-snapshot-repository</distMgmtSnapshotsUrl>
  </properties>
</project>
