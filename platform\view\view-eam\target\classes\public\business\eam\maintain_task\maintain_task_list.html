<!--
/**
 * 保养任务 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2024-12-02 09:16:45
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
    <title th:text="${lang.translate('保养任务')}">保养任务</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 实际完成 , actFinishTime ,typeName=date_input, isHideInSearch=true -->
                    <!-- 实际工时(时) , actTotalCost ,typeName=number_input, isHideInSearch=true -->
                    <!-- 保养设备 , assetId ,typeName=select_box, isHideInSearch=true -->
                    <!-- 设备位置 , assetPos ,typeName=text_input, isHideInSearch=true -->
                    <!-- 设备状态 , assetStatus ,typeName=select_box, isHideInSearch=true -->
                    <!-- 保养结果 , content ,typeName=text_input, isHideInSearch=true -->
                    <!-- 费用 , cost ,typeName=number_input, isHideInSearch=true -->
                    <!-- 执行人 , executorId ,typeName=button, isHideInSearch=true -->
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 任务名称 , name ,typeName=text_input, isHideInSearch=true -->
                    <!-- 备注 , notes ,typeName=text_area, isHideInSearch=true -->
                    <!-- 制单人 , originatorId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 逾期 , overdue ,typeName=select_box, isHideInSearch=true -->
                    <!-- 方案 , planId ,typeName=text_input, isHideInSearch=true -->
                    <!-- 应开始时间 , planStartTime ,typeName=date_input, isHideInSearch=true -->
                    <!-- 结果 , result ,typeName=text_input, isHideInSearch=true -->
                    <!-- 任务状态 , status ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('任务状态')}" class="search-label status-label">任务状态</span><span class="search-colon">:</span></div>
                        <div id="status" th:data="${enum.toArray('com.dt.platform.constants.enums.eam.MaintainTaskStatusEnum')}" style="width:180px" extraParam="{}"></div>
                    </div>
                    <!-- 超时工时(分) , timeout ,typeName=number_input, isHideInSearch=true -->
                    <!-- 预计工时(时) , totalCost ,typeName=number_input, isHideInSearch=true -->
                    <!-- 修改人ID , updateBy ,typeName=text_input, isHideInSearch=true -->
                    <!-- 保养项目数 , itemCount ,typeName=text_input, isHideInSearch=true -->
                    <!-- 待保养项目数 , waitCount ,typeName=text_input, isHideInSearch=true -->
                    <!-- 保养班组 , groupId ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('保养班组')}" class="search-label groupId-label">保养班组</span><span class="search-colon">:</span></div>
                        <div id="groupId" th:data="${'/service-eam/eam-maintain-group/query-list'}" style="width:180px" extraParam="{}"></div>
                    </div>
                    <!-- 保养类型 , maintainType ,typeName=select_box, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('保养类型')}" class="search-label maintainType-label">保养类型</span><span class="search-colon">:</span></div>
                        <div id="maintainType" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_maintain_type'}" style="width:180px" extraParam="{}"></div>
                    </div>
                    <!-- 任务单据 , businessCode ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('任务单据')}" class="search-label businessCode-label">任务单据</span><span class="search-colon">:</span></div>
                        <input id="businessCode" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 设备编码 , assetCode ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('设备编码')}" class="search-label assetCode-label">设备编码</span><span class="search-colon">:</span></div>
                        <input id="assetCode" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 设备名称 , assetName ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('设备名称')}" class="search-label assetName-label">设备名称</span><span class="search-colon">:</span></div>
                        <input id="assetName" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 设备型号 , assetModel ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('设备型号')}" class="search-label assetModel-label">设备型号</span><span class="search-colon">:</span></div>
                        <input id="assetModel" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>
                    <!-- 设备序列 , assetSn ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('设备序列')}" class="search-label assetSn-label">设备序列</span><span class="search-colon">:</span></div>
                        <input id="assetSn" class="layui-input search-input" style="width: 180px" type="text" />
                    </div>


                </div>
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 实际开始 , actStartTime ,typeName=date_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style="width:100px"><span th:text="${lang.translate('实际开始')}" class="search-label actStartTime-label">实际开始</span><span class="search-colon">:</span></div>
                            <input type="text" id="actStartTime-begin" style="width: 180px" lay-verify="date" th:placeholder="${lang.translate('开始日期')}" autocomplete="off" class="layui-input search-input search-date-input"  readonly >
                            <span class="search-dash">-</span>
                            <input type="text" id="actStartTime-end"  style="width: 180px"  lay-verify="date" th:placeholder="${lang.translate('结束日期')}" autocomplete="off" class="layui-input search-input search-date-input" readonly>
                    </div>


                </div>
            </div>


            <!-- 按钮区域 -->
            <div id="search-area" class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索','','cmp:table.search')}">搜索</span></button>
                <button id="search-button-advance" class="layui-btn layui-btn-primary icon-btn search-button-advance"><i class="layui-icon">&#xe671;</i><span th:text="${lang.translate('更多','','cmp:table.search')}">更多</span></button>
            </div>
        </div>

        <div id="table-area" style="margin-top: 42px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
        <button th:if="${perm.checkAuth('eam_maintain_task:create')}" id="add-button" class="layui-btn icon-btn layui-btn-sm create-new-button " lay-event="create"><i class="layui-icon">&#xe654;</i><span th:text="${lang.translate('新建','','cmp:table.button')}">新建</span></button>
    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">

    <button th:if="${perm.checkAuth('eam_maintain_task:view-form')}" class="layui-btn layui-btn-primary layui-btn-xs ops-view-button " lay-event="view"  data-id="{{d.id}}"> <span th:text="${lang.translate('查看','','cmp:table.ops')}">查看</span></button>
    <button th:if="${perm.checkAnyAuth('eam_maintain_task:update','eam_maintain_task:save')}" class="layui-btn layui-btn-primary layui-btn-xs ops-edit-button " lay-event="edit"data-id="{{d.id}}"><span th:text="${lang.translate('修改','','cmp:table.ops')}">修改</span></button>



    <button th:if="${perm.checkAuth('eam_maintain_task:execute')}"class="layui-btn layui-btn-xs  execute-button " lay-event="maintain" data-id="{{d.id}}"><span th:text="${lang.translate('保养','','cmp:table.ops')}">保养</span></button>
    <button th:if="${perm.checkAuth('eam_maintain_task:finish')}"class="layui-btn layui-btn-xs  finish-button " lay-event="finish" data-id="{{d.id}}"><span th:text="${lang.translate('完成','','cmp:table.ops')}">完成</span></button>
    <button th:if="${perm.checkAuth('eam_maintain_task:cancel')}"class="layui-btn layui-btn-xs  cancel-button " lay-event="task-cancel" data-id="{{d.id}}"><span th:text="${lang.translate('取消','','cmp:table.ops')}">取消</span></button>

</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
    var SELECT_ASSETSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetStatusEnum')}]];
    var SELECT_OVERDUE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.MaintainTaskOverdueEnum')}]];
    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.MaintainTaskStatusEnum')}]];
    var AUTH_PREFIX="eam_maintain_task";

    // status
    var STATUS = [[${status}]] ;

</script>

<script th:src="'/business/eam/maintain_task/maintain_task_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/maintain_task/maintain_task_list.js?'+${cacheKey}"></script>

</body>
</html>