<!--
/**
 * 分单规则 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2022-06-02 09:38:41
 */
 -->
<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <title th:text="${lang.translate('巡检工单')}">巡检工单</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="background-color:white;">
<div class="layui-tab layui-tab-brief">
    <ul class="layui-tab-title">
        <li id="wait_count_tab" class="layui-this">待检<span class="layui-badge" id="wait_count">0</span></li>
        <li id="acting_count_tab">检中<span class="layui-badge" id="acting_count">0</span></li>
        <li id="all_data_tab">全部</li>
    </ul>
    <div class="layui-tab-content">

        <div class="layui-tab-item layui-show"  id="wait_count_tab-ct">
            <div>
                <div style="padding-right: 0px;padding-left: 0px" class="layui-col-xs12">
                    <iframe id="wait" js-fn="waitFunc" scrolling="no" class="form-iframe" frameborder="0" onload="this.height=100" style="width: 100%;"></iframe>
                </div>
            </div>
        </div>
        <div class="layui-tab-item" id="acting_count_tab-ct">
            <div>
                <div style="padding-right: 0px;padding-left: 0px" class="layui-col-xs12">
                    <iframe id="acting" js-fn="actingFunc" scrolling="no" class="form-iframe" frameborder="0" onload="this.height=100" style="width: 100%;"></iframe>
                </div>
            </div>
        </div>

        <div class="layui-tab-item" id="tab8-ct">
            <div>
                <div style="padding-right: 0px;padding-left: 0px" class="layui-col-xs12">
                    <iframe id="all" js-fn="allFunc" scrolling="no" class="form-iframe" frameborder="0" onload="this.height=100" style="width: 100%;"></iframe>
                </div>
            </div>
        </div>

    </div>
</div>
<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
</script>
<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">
</script>
<script th:inline="javascript">
</script>
<script th:src="'/business/eam/inspection_task/insepection_tab_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/inspection_task/insepection_tab_list.js?'+${cacheKey}"></script>
</body>
</html>
