<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dt.platform</groupId>
        <artifactId>parent-eam</artifactId>
        <version>*******</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <artifactId>wrapper-all</artifactId>
    <name>wrapper-all</name>
    <description>Wrapper-All</description>
    <dependencies>
        <dependency>
            <groupId>com.foxnicweb.web</groupId>
            <artifactId>service-storage</artifactId>
            <version>${foxnic-web.version}</version>
        </dependency>
        <dependency>
            <groupId>com.foxnicweb.web</groupId>
            <artifactId>service-system</artifactId>
            <version>${foxnic-web.version}</version>
        </dependency>
        <dependency>
            <groupId>com.foxnicweb.web</groupId>
            <artifactId>service-oauth</artifactId>
            <version>${foxnic-web.version}</version>
        </dependency>
        <dependency>
            <groupId>com.foxnicweb.web</groupId>
            <artifactId>service-hrm</artifactId>
            <version>${foxnic-web.version}</version>
        </dependency>
        <dependency>
            <groupId>com.foxnicweb.web</groupId>
            <artifactId>service-pcm</artifactId>
            <version>${foxnic-web.version}</version>
        </dependency>
        <dependency>
            <groupId>com.foxnicweb.web</groupId>
            <artifactId>service-changes</artifactId>
            <version>${foxnic-web.version}</version>
        </dependency>
        <dependency>
            <groupId>com.foxnicweb.web</groupId>
            <artifactId>service-bpm</artifactId>
            <version>${foxnic-web.version}</version>
        </dependency>
        <dependency>
            <groupId>com.foxnicweb.web</groupId>
            <artifactId>service-job</artifactId>
            <version>${foxnic-web.version}</version>
        </dependency>
        <dependency>
            <groupId>com.foxnicweb.web</groupId>
            <artifactId>service-dataperm</artifactId>
            <version>${foxnic-web.version}</version>
        </dependency>
        <dependency>
            <groupId>com.foxnicweb.web</groupId>
            <artifactId>service-docs</artifactId>
            <version>${foxnic-web.version}</version>
        </dependency>
        <dependency>
            <groupId>com.foxnicweb.web</groupId>
            <artifactId>view-docs</artifactId>
            <version>${foxnic-web.version}</version>
        </dependency>
        <dependency>
            <groupId>com.foxnicweb.web</groupId>
            <artifactId>view-dataperm</artifactId>
            <version>${foxnic-web.version}</version>
        </dependency>
        <dependency>
            <groupId>com.foxnicweb.web</groupId>
            <artifactId>view-pcm</artifactId>
            <version>${foxnic-web.version}</version>
        </dependency>
        <dependency>
            <groupId>com.foxnicweb.web</groupId>
            <artifactId>view-changes</artifactId>
            <version>${foxnic-web.version}</version>
        </dependency>
        <dependency>
            <groupId>com.foxnicweb.web</groupId>
            <artifactId>view-console</artifactId>
            <version>${foxnic-web.version}</version>
        </dependency>
        <dependency>
            <groupId>com.foxnicweb.web</groupId>
            <artifactId>view-bpm</artifactId>
            <version>${foxnic-web.version}</version>
        </dependency>


        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>domain-eam</artifactId>
            <version>${platform.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>framework-eam</artifactId>
            <version>${platform.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>framework-oa</artifactId>
            <version>1.1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>framework-web</artifactId>
            <version>2.1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>proxy-eam</artifactId>
            <version>${platform.version}</version>
        </dependency>



        <!--公共-->
        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>service-job-web</artifactId>
            <version>${foxnic-web-plus.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>service-common</artifactId>
            <version>${foxnic-web-plus.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>ooxml-schemas</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>service-mobile</artifactId>
            <version>${foxnic-web-plus.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>view-console</artifactId>
            <version>${foxnic-web-plus.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>view-common</artifactId>
            <version>${foxnic-web-plus.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>view-mobile</artifactId>
            <version>${foxnic-web-plus.version}</version>
        </dependency>

        <!-- OA -->
        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>service-oa</artifactId>
            <version>${foxnic-oa.version}</version>
        </dependency>

        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>view-oa</artifactId>
            <version>${foxnic-oa.version}</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.dt.platform</groupId>-->
<!--            <artifactId>service-knowledgebase</artifactId>-->
<!--            <version>${foxnic-oa.version}</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.dt.platform</groupId>-->
<!--            <artifactId>view-knowledgebase</artifactId>-->
<!--            <version>${foxnic-oa.version}</version>-->
<!--        </dependency>-->


        <!--EAM-->
        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>service-job-eam</artifactId>
            <version>${platform.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>view-eam</artifactId>
            <version>${platform.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>service-eam</artifactId>
            <version>${platform.version}</version>
        </dependency>

        <!-- Magic API - 接口快速开发框架 -->
        <dependency>
            <groupId>org.ssssssss</groupId>
            <artifactId>magic-api-spring-boot-starter</artifactId>
            <version>1.6.0</version>
        </dependency>

        <!-- HR -->
<!--        <dependency>-->
<!--            <groupId>com.dt.platform</groupId>-->
<!--            <artifactId>service-hr</artifactId>-->
<!--            <version>${foxnic-hr.version}</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.dt.platform</groupId>-->
<!--            <artifactId>view-hr</artifactId>-->
<!--            <version>${foxnic-hr.version}</version>-->
<!--        </dependency>-->



        <!-- 运维-->
<!--        <dependency>-->
<!--            <groupId>com.dt.platform</groupId>-->
<!--            <artifactId>service-ops</artifactId>-->
<!--            <version>${foxnic-ops.version}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.dt.platform</groupId>-->
<!--            <artifactId>service-datacenter</artifactId>-->
<!--            <version>${foxnic-ops.version}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.dt.platform</groupId>-->
<!--            <artifactId>view-datacenter</artifactId>-->
<!--            <version>${foxnic-ops.version}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.dt.platform</groupId>-->
<!--            <artifactId>view-ops</artifactId>-->
<!--            <version>${foxnic-ops.version}</version>-->
<!--        </dependency>-->


        <!-- 合同 -->
<!--        <dependency>-->
<!--            <groupId>com.foxnicweb.web</groupId>-->
<!--            <artifactId>service-contract</artifactId>-->
<!--            <version>${foxnic-contract.version}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.foxnicweb.web</groupId>-->
<!--            <artifactId>view-contract</artifactId>-->
<!--            <version>${foxnic-contract.version}</version>-->
<!--        </dependency>-->

        <!-- 客户模块 -->
<!--        <dependency>-->
<!--            <groupId>com.foxnicweb.web</groupId>-->
<!--            <artifactId>service-customer</artifactId>-->
<!--            <version>${foxnic-customer.version}</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.foxnicweb.web</groupId>-->
<!--            <artifactId>view-customer</artifactId>-->
<!--            <version>${foxnic-customer.version}</version>-->
<!--        </dependency>-->


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <!-- 移除掉默认支持的 Tomcat -->
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-to-slf4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 添加 Undertow 容器 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.session</groupId>
            <artifactId>spring-session-data-redis</artifactId>
        </dependency>
    </dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>1.2.14</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <defaultGoal>compile</defaultGoal>
        <resources>
            <resource>
                <directory>${basedir}/src/main/java</directory>
                <includes>
                    <include>**/**</include>
                </includes>
            </resource>
            <resource>
                <directory>${basedir}/src/main/resources</directory>
                <includes>
                    <include>**/**</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <fork>true</fork>
                    <includes>
                        <include>
                            <groupId>nothing</groupId>
                            <artifactId>nothing</artifactId>
                        </include>
                    </includes>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-dependencies-runtime</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                            <includeScope>runtime</includeScope>
                            <!--依赖传递 -->
                            <excludeTransitive>false</excludeTransitive>
                        </configuration>
                    </execution>
                    <execution>
                        <id>copy-dependencies-system</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                            <includeScope>system</includeScope>
                        </configuration>
                    </execution>
                    <execution>
                        <id>copy-dependencies-compile</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                            <includeScope>compile</includeScope>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <repositories>
        <repository>
            <id>releases</id>
            <name>Releases</name>
            <url>http://foxnicweb.com:9091/repository/maven-releases/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
        <repository>
            <id>snapshots</id>
            <name>Snapshots</name>
            <url>http://foxnicweb.com:9091/repository/maven-snapshots/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>releases</id>
            <name>Releases</name>
            <url>http://foxnicweb.com:9091/repository/maven-releases/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
</project>
