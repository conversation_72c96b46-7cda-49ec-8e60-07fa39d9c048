<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dt.platform</groupId>
        <artifactId>parent-eam</artifactId>
        <version>*******</version>
        <relativePath>../../pom.xml</relativePath>
        <!-- lookup parent from repository -->
    </parent>
    <artifactId>service-job-eam</artifactId>
    <name>service-job-eam</name>
    <description>Job Service</description>
    <properties>
        <skipTests>true</skipTests>
        <java.version>1.8</java.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>framework-eam</artifactId>
            <version>${platform.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>domain-eam</artifactId>
            <version>${platform.version}</version>
        </dependency>
        <dependency>
            <groupId>com.dt.platform</groupId>
            <artifactId>proxy-eam</artifactId>
            <version>${platform.version}</version>
        </dependency>
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
            <version>2.3.2</version>
        </dependency>

    </dependencies>
    <build>
        <defaultGoal>compile</defaultGoal>
        <resources>
            <resource>
                <directory>${basedir}/src/main/java</directory>
                <includes>
                    <include>**/*.tql</include>
                </includes>
            </resource>
            <resource>
                <directory>${basedir}/src/main/resources</directory>
                <includes>
                    <include>**/**</include>
                </includes>
            </resource>
        </resources>
    </build>
</project>
