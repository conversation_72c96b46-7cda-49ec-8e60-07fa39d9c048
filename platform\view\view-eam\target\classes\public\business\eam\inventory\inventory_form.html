<!--
/**
 * 资产盘点 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2022-01-04 12:33:29
 */
 -->
<!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <title th:text="${lang.translate('资产盘点')}">资产盘点</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="display:none">

        <input name="id" id="id"  type="hidden"/>

        <!--开始：group 循环-->



        <div class="layui-row form-row" id="random-6995-content"  style="padding-top: 15px">

            <!--开始：group 循环-->
            <div class="layui-col-xs4 form-column" style="padding-top: 0px" >

                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('盘点名称')}">盘点名称</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="name" id="name" name="name" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('盘点名称') }" type="text" class="layui-input"    lay-verify="|required"  />
                    </div>
                </div>

                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('盘点人')}">盘点人</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="inventoryUserIds" id="inventoryUserIds" name="inventoryUserIds"  type="hidden" class="layui-input"   />
                        <button id="inventoryUserIds-button" type="button" action-type="emp-dialog" class="layui-btn   " style="width: 100%"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择人员')}" th:default-label="${lang.translate('请选择人员')}">按钮文本</span></button>
                    </div>
                </div>

                <!--结束：栏次内字段循环-->
            </div>
            <!--开始：group 循环-->
            <div class="layui-col-xs4 form-column"  style="padding-top: 0px" >

                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('负责人')}">负责人</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="inventoryDirectorIds" id="inventoryDirectorIds" name="inventoryDirectorIds"  type="hidden" class="layui-input"   />
                        <button id="inventoryDirectorIds-button" type="button" action-type="emp-dialog" class="layui-btn   " style="width: 100%"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择人员')}" th:default-label="${lang.translate('请选择人员')}">按钮文本</span></button>
                    </div>
                </div>

                <!--结束：栏次内字段循环-->
            </div>
            <!--开始：group 循环-->
            <div class="layui-col-xs4 form-column"  style="padding-top: 0px" >


                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('全员盘点')}">全员盘点</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="allEmployee" input-type="select" th:data="${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}" extraParam="{}"></div>
                    </div>
                </div>


                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--开始：group 循环-->



        <div class="layui-row form-row" id="random-5713-content">

            <!--开始：group 循环-->
            <div class="layui-col-xs12 form-column" >


                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('备注')}">备注</div></div>
                    <div class="layui-input-block ">
                        <textarea lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('备注') }" class="layui-textarea" style="height: 40px" ></textarea>
                    </div>
                </div>


                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--开始：group 循环-->


        <fieldset class="layui-elem-field layui-field-title form-group-title" id="random-9666-fieldset">

            <legend th:text="${lang.translate('盘点范围')}">盘点范围</legend>
        </fieldset>

        <div class="layui-row form-row" id="random-9666-content">

            <!--开始：group 循环-->
            <div class="layui-col-xs4 form-column" >


                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('购置开始日期')}">购置开始日期</div></div>
                    <div class="layui-input-block ">
                        <input input-type="date" lay-filter="purchaseStartDate" id="purchaseStartDate" name="purchaseStartDate"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择') +''+ lang.translate('购置开始日期') }" type="text" class="layui-input"    lay-verify=""   />
                    </div>
                </div>


                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('购置结束日期')}">购置结束日期</div></div>
                    <div class="layui-input-block ">
                        <input input-type="date" lay-filter="purchaseEndDate" id="purchaseEndDate" name="purchaseEndDate"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择') +''+ lang.translate('购置结束日期') }" type="text" class="layui-input"    lay-verify=""   />
                    </div>
                </div>


                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('资产状态')}">资产状态</div></div>
                    <div class="layui-input-block ">
                        <div id="assetStatus" input-type="select" th:data="${'/service-eam/eam-asset-status/query-list'}" extraParam="{}"></div>
                    </div>
                </div>


                <!--结束：栏次内字段循环-->
            </div>
            <!--开始：group 循环-->
            <div class="layui-col-xs4 form-column" >

                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('所属公司')}">所属公司</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="ownCompanyId" id="ownCompanyId" name="ownCompanyId"  type="hidden" class="layui-input"   />
                        <button id="ownCompanyId-button" type="button" action-type="org-dialog" class="layui-btn   " style="width: 100%"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择公司')}" th:default-label="${lang.translate('请选择公司')}">按钮文本</span></button>
                    </div>
                </div>

                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('使用公司/部门')}">使用公司/部门</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="useOrganizationId" id="useOrganizationId" name="useOrganizationId"  type="hidden" class="layui-input"   />
                        <button id="useOrganizationId-button" type="button" action-type="org-dialog" class="layui-btn   " style="width: 100%"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择组织节点')}" th:default-label="${lang.translate('请选择组织节点')}">按钮文本</span></button>
                    </div>
                </div>

                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('保管人')}">保管人</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="inventoryManagerIds" id="inventoryManagerIds" name="inventoryManagerIds"  type="hidden" class="layui-input"   />
                        <button id="inventoryManagerIds-button" type="button" action-type="emp-dialog" class="layui-btn   " style="width: 100%"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择人员')}" th:default-label="${lang.translate('请选择人员')}">按钮文本</span></button>
                    </div>
                </div>

                <!--结束：栏次内字段循环-->
            </div>
            <!--开始：group 循环-->
            <div class="layui-col-xs4 form-column" >


                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('资产分类')}">资产分类</div></div>
                    <div class="layui-input-block ">
                        <div id="categoryIds" input-type="select" ></div>
                    </div>
                </div>



                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('位置')}">位置</div></div>
                    <div class="layui-input-block ">
                        <div id="positionIds" input-type="select" th:data="${'/service-eam/eam-position/query-paged-list'}" extraParam="{}"></div>
                    </div>
                </div>


                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('仓库')}">仓库</div></div>
                    <div class="layui-input-block ">
                        <div id="warehouseIds" input-type="select" th:data="${'/service-eam/eam-warehouse/query-paged-list'}" extraParam="{}"></div>
                    </div>
                </div>


                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 200px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('eam_inventory:create','eam_inventory:update','eam_inventory:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>

<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetHandleStatusEnum')}]];
    var SELECT_INVENTORYSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetInventoryActionStatusEnum')}]];
    var SELECT_DATASTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetInventoryDataStatusEnum')}]];
    var SELECT_ALLEMPLOYEE_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var SELECT_ASSETSTATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.AssetStatusEnum')}]];
    var VALIDATE_CONFIG={"allEmployee":{"labelInForm":"全员盘点","inputType":"select_box","required":true},"finishTime":{"date":true,"labelInForm":"盘点结束时间","inputType":"date_input"},"purchaseEndDate":{"date":true,"labelInForm":"购置结束日期","inputType":"date_input"},"name":{"labelInForm":"盘点名称","inputType":"text_input","required":true},"purchaseStartDate":{"date":true,"labelInForm":"购置开始日期","inputType":"date_input"},"startTime":{"date":true,"labelInForm":"盘点开始时间","inputType":"date_input"}};
    var AUTH_PREFIX="eam_inventory";

    var ASSET_CATEGORY_DATA =  [[${assetCategoryData}]];

    var PLAN_ID = [[${planId}]] ;
    // OWNER_CODE
    var OWNER_CODE = [[${ownerCode}]] ;

</script>



<script th:src="'/business/eam/inventory/inventory_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/inventory/inventory_form.js?'+${cacheKey}"></script>

</body>
</html>
