<!--
/**
 * 备件清单 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2023-08-08 12:13:41
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
	<title th:text="${lang.translate('备件清单')}">备件清单</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="opacity:0">

        <input name="id" id="id"  type="hidden"/>

         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-4469-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >

                <!-- text_input : 备件编号 ,  code -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('备件编号')}">备件编号</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="code" id="code" name="code" th:placeholder="${ lang.translate('请输入'+'备件编号') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- button : 物品档案 ,  goodId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('物品档案')}">物品档案</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="goodId" id="goodId" name="goodId"  type="hidden" class="layui-input"    lay-verify="|required"   />
                        <button id="goodId-button" type="button" action-type="" class="layui-btn " style="width: 100%" default-width="100%" auto-width="false"><span th:text="${lang.translate('物品选择')}" th:default-label="${lang.translate('物品选择')}">按钮文本</span></button>
                    </div>
                </div>
            
                <!-- text_input : 备件名称 ,  name -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('备件名称')}">备件名称</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="name" id="name" name="name" th:placeholder="${ lang.translate('请输入'+'备件名称') }" type="text" class="layui-input"    lay-verify="|required"  />
                    </div>
                </div>
            
                <!-- text_input : 规格型号 ,  model -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('规格型号')}">规格型号</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="model" id="model" name="model" th:placeholder="${ lang.translate('请输入'+'规格型号') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- text_input : 备件序列 ,  sn -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('备件序列')}">备件序列</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="sn" id="sn" name="sn" th:placeholder="${ lang.translate('请输入'+'备件序列') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- select_box : 使用场景 ,  usageRange  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('使用场景')}">使用场景</div></div>
                    <div class="layui-input-block ">
                        <div id="usageRange" input-type="select" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_sp_usage_range'}" extraParam="{}"></div>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >

                <!-- button : 保管人员 ,  managerUserId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('保管人员')}">保管人员</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="managerUserId" id="managerUserId" name="managerUserId"  type="hidden" class="layui-input"   />
                        <button id="managerUserId-button" type="button" action-type="emp-dialog" class="layui-btn   " style="width: 100%" default-width="100%" auto-width="false"> <i class='layui-icon layui-icon-search'></i> <span th:text="${lang.translate('请选择人员')}" th:default-label="${lang.translate('请选择人员')}">按钮文本</span></button>
                    </div>
                </div>
            
                <!-- select_box : 所在仓库 ,  warehouseId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('所在仓库')}">所在仓库</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="warehouseId" input-type="select" th:data="${'/service-eam/eam-warehouse/query-paged-list'}" extraParam="{}"></div>
                    </div>
                </div>
            
                <!-- select_box : 存放位置 ,  locId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('存放位置')}">存放位置</div></div>
                    <div class="layui-input-block ">
                        <div id="locId" input-type="select" th:data="${'/service-eam/eam-position/query-paged-list'}" extraParam="{}"></div>
                    </div>
                </div>
            
                <!-- text_input : 供应厂商 ,  supplier -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('供应厂商')}">供应厂商</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="supplier" id="supplier" name="supplier" th:placeholder="${ lang.translate('请输入'+'供应厂商') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- date_input : 入库时间 ,  insertTime  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('入库时间')}">入库时间</div></div>
                    <div class="layui-input-block ">
                        <input input-type="date" lay-filter="insertTime" id="insertTime" name="insertTime"  autocomplete="off"  readonly  th:placeholder="${ lang.translate('请选择'+'入库时间') }" type="text" class="layui-input"    lay-verify=""   />
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-8967-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column"  style="padding-top: 0px" >

                <!-- text_input : 来源描述 ,  sourceDesc -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('来源描述')}">来源描述</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="sourceDesc" id="sourceDesc" name="sourceDesc" th:placeholder="${ lang.translate('请输入'+'来源描述') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- text_input : 适配信息 ,  adaptingDevice -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('适配信息')}">适配信息</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="adaptingDevice" id="adaptingDevice" name="adaptingDevice" th:placeholder="${ lang.translate('请输入'+'适配信息') }" type="text" class="layui-input"  />
                    </div>
                </div>
            
                <!-- text_area : 备注 ,  notes  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('备注')}">备注</div></div>
                    <div class="layui-input-block ">
                        <textarea lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入'+'备注') }" class="layui-textarea" style="height: 150px" ></textarea>
                    </div>
                </div>
                            <!-- upload : 图片 ,  pictureId  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('图片')}">图片</div></div>
                    <div class="layui-upload layui-input-block ">
                        <input input-type="upload" id="pictureId"  name="pictureId" lay-filter="pictureId" style="display: none">
                        <button type="button" class="layui-btn" id="pictureId-button" th:text="${lang.translate('选择附件')}">选择附件</button>
                        <div class="layui-upload-list" id="pictureId-file-list"></div>
                    </div>
                </div>
            <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 100px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消','','form.button')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('eam_device_sp:create','eam_device_sp:update','eam_device_sp:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存','','form.button')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var RADIO_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.DeviceSpStatusEnum')}]];
    var VALIDATE_CONFIG={"insertTime":{"date":true,"labelInForm":"入库时间","inputType":"date_input"},"warehouseId":{"labelInForm":"所在仓库","inputType":"select_box","required":true},"name":{"labelInForm":"备件名称","inputType":"text_input","required":true},"goodId":{"labelInForm":"物品档案","inputType":"button","required":true}};
    var AUTH_PREFIX="eam_device_sp";


</script>



<script th:src="'/business/eam/device_sp/device_sp_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/device_sp/device_sp_form.js?'+${cacheKey}"></script>

</body>
</html>