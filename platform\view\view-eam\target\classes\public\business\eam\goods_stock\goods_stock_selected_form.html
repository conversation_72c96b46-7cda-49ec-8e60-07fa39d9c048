<!--
/**
 * 库存物品 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2022-04-22 05:33:06
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
	<title th:text="${lang.translate('库存物品')}">库存物品</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="display:none">

        <input name="id" id="id"  type="hidden"/>

         <!--开始：group 循环-->



        <div class="layui-row form-row" id="random-5497-content">

             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >


                    <div class="layui-form-item" >
                        <div class="layui-form-label "><div th:text="${lang.translate('物品数量')}">物品数量</div></div>
                        <div class="layui-input-block ">
                            <input lay-filter="stockInNumber" id="stockInNumber" name="stockInNumber" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('入库存数量') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="0" />
                        </div>
                    </div>


                <!--结束：栏次内字段循环-->
            </div>
             <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
<!--            <div class="layui-col-xs6 form-column"  id="sumAmount">-->
<!--                    <div class="layui-form-item" >-->
<!--                        <div class="layui-form-label "><div th:text="${lang.translate('总金额')}">总金额</div></div>-->
<!--                        <div class="layui-input-block ">-->
<!--                            <input lay-filter="amount" id="amount" name="amount" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('总金额') }" type="text" class="layui-input"   autocomplete="off" input-type="number_input" integer="false" decimal="true" allow-negative="true" step="1.0"   scale="0" />-->
<!--                        </div>-->
<!--                    </div>-->
<!--                &lt;!&ndash;结束：栏次内字段循环&ndash;&gt;-->
<!--            </div>-->
            <!--结束：栏次输入框循环-->
        </div>
         <!--开始：group 循环-->



        <div class="layui-row form-row" id="random-3065-content" >

<!--            <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">-->
<!--                <div class="layui-form-label "><div th:text="${lang.translate('仓库')}">仓库</div><div class="layui-required">*</div></div>-->
<!--                <div class="layui-input-block ">-->
<!--                    <div id="warehouseId" input-type="select" th:data="${'/service-eam/eam-warehouse/query-paged-list'}" extraParam="{}"></div>-->
<!--                </div>-->
<!--            </div>-->


            <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                <div class="layui-form-label "><div th:text="${lang.translate('物品库位')}">物品库位</div><div class="layui-required">*</div></div>
                <div class="layui-input-block ">
                    <div id="positionId" input-type="select" th:data="${'/service-eam/eam-warehouse-position/query-paged-list'}"  extraParam="{}"></div>
                </div>
            </div>


<!--            &lt;!&ndash; 只有当非第一个分组没有title时才使 padding-top 为 0 &ndash;&gt;-->
<!--            <div class="layui-col-xs12 form-column"  style="padding-top: 0px" id="snarea">-->
                <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('物品序列')}">物品序列</div></div>
                    <div class="layui-input-block ">
                        <textarea lay-filter="sn" id="sn" name="sn" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('序列') }" class="layui-textarea" style="height: 120px" ></textarea>
                    </div>
                </div>


<!--                &lt;!&ndash;开始：column 循环&ndash;&gt;-->
<!--            &lt;!&ndash; 只有当非第一个分组没有title时才使 padding-top 为 0 &ndash;&gt;-->

               <div class="layui-form-item" >
                    <div class="layui-form-label "><div th:text="${lang.translate('备注')}">备注</div></div>
                    <div class="layui-input-block ">
                        <textarea lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('备注') }" class="layui-textarea" style="height: 120px" ></textarea>
                    </div>
                </div>
<!--                &lt;!&ndash;结束：栏次内字段循环&ndash;&gt;-->
<!--            </div>-->

            <!--结束：栏次输入框循环-->
        </div>
        <!--结束：group循环-->

        <div style="height: 250px"></div>
        <div style="height: 20px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('eam_goods_stock:create','eam_goods_stock:update','eam_goods_stock:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>

<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
    var VALIDATE_CONFIG={"positionId":{"labelInForm":"库位","inputType":"select_box","required":true}}
    var AUTH_PREFIX="eam_goods_stock";

    var WAREHOUSE_ID = [[${warehouseId}]] ;
    // OWNER_CODE
    var OWNER_CODE = [[${ownerCode}]] ;
    // OWNER_TYPE
    var OWNER_TYPE = [[${ownerType}]] ;

    var OPER_TYPE = [[${operType}]] ;

    var IS_MULTIPLE = [[${isMultiple}]] ;


</script>



<script th:src="'/business/eam/goods_stock/goods_stock_selected_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/goods_stock/goods_stock_selected_form.js?'+${cacheKey}"></script>

</body>
</html>
