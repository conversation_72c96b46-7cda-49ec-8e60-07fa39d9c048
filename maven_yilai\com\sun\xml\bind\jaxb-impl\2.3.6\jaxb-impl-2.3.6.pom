<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 2013, 2022 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Distribution License v. 1.0, which is available at
    http://www.eclipse.org/org/documents/edl-v10.php.

    SPDX-License-Identifier: BSD-3-Clause

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sun.xml.bind.mvn</groupId>
        <artifactId>jaxb-bundles</artifactId>
        <version>2.3.6</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <groupId>com.sun.xml.bind</groupId>
    <artifactId>jaxb-impl</artifactId>

    <packaging>jar</packaging>
    <name>Old JAXB Runtime</name>
    <description>Old JAXB Runtime module. Contains sources required for runtime processing.</description>
    <url>https://eclipse-ee4j.github.io/jaxb-ri/</url>

    <dependencies>
        <dependency>
            <groupId>jakarta.xml.bind</groupId>
            <artifactId>jakarta.xml.bind-api</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.activation</groupId>
            <artifactId>jakarta.activation-api</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.sun.xml.fastinfoset</groupId>
            <artifactId>FastInfoset</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.jvnet.staxex</groupId>
            <artifactId>stax-ex</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.sun.activation</groupId>
            <artifactId>jakarta.activation</artifactId>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-runtime</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>txw2</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.sun.istack</groupId>
            <artifactId>istack-commons-runtime</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

    </dependencies>

    <properties>
        <dep.sources>${project.build.directory}/generated-sources/dependencies</dep.sources>
        <dep.sources.mr>${project.build.directory}/generated-sources/dependencies-mr</dep.sources.mr>
    </properties>

    <profiles>
        <profile>
            <id>default-profile</id>
            <activation>
                <property>
                    <name>!dev</name>
                </property>
            </activation>
            <dependencies>
                <dependency>
                    <groupId>org.osgi</groupId>
                    <artifactId>osgi.core</artifactId>
                    <version>${felix.osgi.core}</version>
                    <scope>test</scope>
                </dependency>
                <dependency>
                    <groupId>org.glassfish.jaxb</groupId>
                    <artifactId>osgi-test-runtime</artifactId>
                    <version>${project.version}</version>
                    <scope>test</scope>
                </dependency>
                <dependency>
                    <groupId>org.glassfish.jaxb</groupId>
                    <artifactId>osgi-test-parent</artifactId>
                    <version>${project.version}</version>
                    <scope>test</scope>
                </dependency>
            </dependencies>
        </profile>
    </profiles>

    <build>
        <plugins>
            <plugin> <!-- want to unpack sources from individual modules -->
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>unpack-sources</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>unpack-dependencies</goal>
                        </goals>
                        <configuration>
                            <includeGroupIds>org.glassfish.jaxb,com.sun.istack</includeGroupIds>
                            <includeScope>provided</includeScope>
                            <classifier>sources</classifier>
                            <excludeTransitive>true</excludeTransitive>
                            <excludes>module-info.*,META-INF/versions/**,META-INF/MANIFEST.MF</excludes>
                            <outputDirectory>${dep.sources}</outputDirectory>
                        </configuration>
                    </execution>
                    <execution>
                        <id>unpack-sources-mr</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>unpack-dependencies</goal>
                        </goals>
                        <configuration>
                            <includeGroupIds>org.glassfish.jaxb,com.sun.istack</includeGroupIds>
                            <includeScope>provided</includeScope>
                            <classifier>sources</classifier>
                            <excludeTransitive>true</excludeTransitive>
                            <includes>META-INF/versions/9/**</includes>
                            <outputDirectory>${dep.sources.mr}</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>add-resource</id>
                        <phase>generate-resources</phase>
                        <goals>
                            <goal>add-resource</goal>
                        </goals>
                        <configuration>
                            <resources>
                                <resource>
                                    <directory>${dep.sources}</directory>
                                    <excludes>
                                        <exclude>**/*.java</exclude>
                                    </excludes>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                    <execution>
                        <id>add-sources</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>${dep.sources}</source>
                            </sources>
                        </configuration>
                    </execution>
                    <execution>
                        <id>add-mr-resource</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>add-resource</goal>
                        </goals>
                        <configuration>
                            <resources>
                                <resource>
                                    <directory>${dep.sources.mr}</directory>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <executions>
                    <execution>
                        <id>default-compile-mr</id>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <compileSourceRoots>
                                <compileSourceRoot>${dep.sources.mr}/META-INF/versions/9</compileSourceRoot>
                            </compileSourceRoots>
                            <outputDirectory>${project.build.outputDirectory}/META-INF/versions/${upper.java.level}</outputDirectory>
                            <source>${upper.java.level}</source>
                            <target>${upper.java.level}</target>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <executions>
                    <execution>
                        <id>bundle-manifest</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>manifest</goal>
                        </goals>
                        <configuration>
                            <instructions>
                                <Export-Package>
                                    !META-INF.*,
                                    *
                                </Export-Package>
                                <Import-Package>
                                    javax.activation;version=!,
                                    sun.misc;resolution:=optional,
                                    jdk.internal.misc;resolution:=optional,
                                    *
                                </Import-Package>
                                <DynamicImport-Package>*</DynamicImport-Package>
                            </instructions>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                        <manifestEntries>
                            <Multi-Release>true</Multi-Release>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                    <includeDependencySources>false</includeDependencySources>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-junit4osgi-plugin</artifactId>
                <version>${felix.junit4osgi}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>test</goal>
                        </goals>
                        <phase>integration-test</phase>
                        <configuration>
                            <skip>${skipOsgiTests}</skip>
                            <m_deployProjectArtifact>true</m_deployProjectArtifact>
                            <configuration>
                                <org.osgi.framework.system.packages.extra>
                                    org.xml.sax,
                                    javax.xml.stream,
                                    javax.xml.transform.dom,
                                    javax.xml.namespace,
                                    javax.imageio.stream,
                                    javax.activation,
                                    org.xml.sax.helpers,
                                    javax.xml.validation,
                                    org.w3c.dom,
                                    javax.xml.transform.sax,
                                    javax.imageio,
                                    javax.xml.parsers,
                                    javax.xml.transform,
                                    javax.xml.transform.stream,
                                    javax.xml.datatype,
                                    javax.xml.stream.events,
                                    org.xml.sax.ext,
                                    javax.xml
                                </org.osgi.framework.system.packages.extra>
                            </configuration>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <executions>
                    <execution>
                        <id>default-test</id>
                        <phase>integration-test</phase>
                        <configuration>
                            <systemPropertyVariables>
                                <jaxb.spec.version>${jaxb-api.version}</jaxb.spec.version>
                                <osgi.dist>${project.build.directory}/${project.build.finalName}</osgi.dist>
                            </systemPropertyVariables>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
