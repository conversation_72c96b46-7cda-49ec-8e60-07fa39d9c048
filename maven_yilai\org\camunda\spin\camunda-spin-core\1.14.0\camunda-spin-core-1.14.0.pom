<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.camunda.spin</groupId>
    <artifactId>camunda-spin-root</artifactId>
    <version>1.14.0</version>
  </parent>

  <artifactId>camunda-spin-core</artifactId>
  <name>camunda Spin - core</name>

  <dependencies>

    <dependency>
      <groupId>org.camunda.commons</groupId>
      <artifactId>camunda-commons-logging</artifactId>
    </dependency>

    <dependency>
      <groupId>org.camunda.commons</groupId>
      <artifactId>camunda-commons-utils</artifactId>
    </dependency>

    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.codehaus.groovy</groupId>
      <artifactId>groovy-all</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.python</groupId>
      <artifactId>jython</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.jruby</groupId>
      <artifactId>jruby-complete</artifactId>
      <scope>test</scope>
    </dependency>
    
    <dependency>
      <groupId>org.graalvm.js</groupId>
      <artifactId>js</artifactId>
      <scope>test</scope>
    </dependency>
    
    <dependency>
      <groupId>org.graalvm.js</groupId>
      <artifactId>js-scriptengine</artifactId>
      <scope>test</scope>
    </dependency>
    

    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>net.javacrumbs.json-unit</groupId>
      <artifactId>json-unit-fluent</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.powermock</groupId>
      <artifactId>powermock-module-junit4</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.powermock</groupId>
      <artifactId>powermock-api-mockito2</artifactId>
      <scope>test</scope>
    </dependency>

  </dependencies>

  <profiles>
    <profile>
      <id>disable-javascript-tests</id>
      <activation>
        <jdk>(,1.8)</jdk>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <argLine>-XX:PermSize=128m</argLine>
              <excludes>
                <exclude>**/javascript/**</exclude>
              </excludes>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <!-- exclude the DataFormatProviderTest until PowerMock 2.0 is released: https://app.camunda.com/jira/browse/CAM-9309 -->
    <profile>
      <id>jdk-9-and-onwards</id>
      <activation>
        <jdk>[9,)</jdk>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <argLine>-XX:PermSize=128m</argLine>
              <excludes>
                <exclude>**/DataFormatLoadingTest.java</exclude>
              </excludes>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>jdk-15-and-onwards</id>
      <activation>
        <jdk>[15,)</jdk>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <excludes combine.children="append">
                <exclude>**/*NashornTest.java</exclude>
              </excludes>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>jdk-16-and-onwards</id>
      <activation>
        <jdk>[16,)</jdk>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <argLine>
                -XX:MetaspaceSize=128m
              </argLine>
              <excludes combine.children="append">
                <exclude>**/*NashornTest.java</exclude>
              </excludes>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>

    <!-- check for api differences between latest minor release -->
    <profile>
      <id>check-api-compatibility</id>
      <activation>
        <jdk>(,)</jdk>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>clirr-maven-plugin</artifactId>
            <version>2.8</version>
            <configuration>
              <comparisonVersion>${spin.version.old}</comparisonVersion>
              <logResults>true</logResults>
              <excludes>
                <exclude>org/camunda/spin/impl/**</exclude>
              </excludes>
            </configuration>
            <executions>
              <execution>
                <id>all</id>
                <phase>verify</phase>
                <goals>
                  <goal>check-no-fork</goal>
                </goals>
                <configuration>
                  <textOutputFile>${project.build.directory}/clirr-all.txt</textOutputFile>
                  <failOnWarning>false</failOnWarning>
                  <failOnError>false</failOnError>
                </configuration>
              </execution>
              <execution>
                <id>restrictive</id>
                <phase>verify</phase>
                <goals>
                  <goal>check-no-fork</goal>
                </goals>
                <configuration>
                  <textOutputFile>${project.build.directory}/clirr-restrictive.txt</textOutputFile>
                  <failOnWarning>true</failOnWarning>
                  <ignoredDifferencesFile>../.clirr-jenkins-ignore.xml</ignoredDifferencesFile>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <configuration>
         <instructions>
          <Import-Package>
            org.junit*;resolution:=optional,
            *
          </Import-Package>
         </instructions>
        </configuration>
        <executions>
          <execution>
            <id>bundle-manifest</id>
            <phase>process-classes</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>test-jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
