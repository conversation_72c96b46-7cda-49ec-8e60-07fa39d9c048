<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 2013, 2022 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Distribution License v. 1.0, which is available at
    http://www.eclipse.org/org/documents/edl-v10.php.

    SPDX-License-Identifier: BSD-3-Clause

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.eclipse.ee4j</groupId>
        <artifactId>project</artifactId>
        <version>1.0.7</version>
        <relativePath/>
    </parent>

    <groupId>org.glassfish.jaxb</groupId>
    <artifactId>jaxb-bom</artifactId>
    <version>2.3.6</version>

    <packaging>pom</packaging>
    <name>JAXB BOM</name>
    <description>JAXB Bill of Materials (BOM)</description>
    <url>https://eclipse-ee4j.github.io/jaxb-ri/</url>

    <properties>
        <jaxb-api.version>2.3.3</jaxb-api.version>
        <jaxb-api-osgi.version>2.3</jaxb-api-osgi.version>
        <istack.version>3.0.12</istack.version>
        <fastinfoset.version>1.2.18</fastinfoset.version>
        <stax-ex.version>1.8.3</stax-ex.version>

        <jakarta.activation.version>1.2.2</jakarta.activation.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- *** SOURCES *** -->
            <!-- new -->
            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>jaxb-runtime</artifactId>
                <version>${project.version}</version>
                <classifier>sources</classifier>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>jaxb-xjc</artifactId>
                <version>${project.version}</version>
                <classifier>sources</classifier>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>jaxb-jxc</artifactId>
                <version>${project.version}</version>
                <classifier>sources</classifier>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>codemodel</artifactId>
                <version>${project.version}</version>
                <classifier>sources</classifier>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>txw2</artifactId>
                <version>${project.version}</version>
                <classifier>sources</classifier>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>xsom</artifactId>
                <version>${project.version}</version>
                <classifier>sources</classifier>
            </dependency>

            <!--OLD-->
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-impl</artifactId>
                <version>${project.version}</version>
                <classifier>sources</classifier>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-xjc</artifactId>
                <version>${project.version}</version>
                <classifier>sources</classifier>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-jxc</artifactId>
                <version>${project.version}</version>
                <classifier>sources</classifier>
            </dependency>

            <dependency> <!--JAXB-API-->
                <groupId>jakarta.xml.bind</groupId>
                <artifactId>jakarta.xml.bind-api</artifactId>
                <version>${jaxb-api.version}</version>
                <classifier>sources</classifier>
            </dependency>
            <dependency>
                <groupId>org.jvnet.staxex</groupId>
                <artifactId>stax-ex</artifactId>
                <version>${stax-ex.version}</version>
                <classifier>sources</classifier>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.fastinfoset</groupId>
                <artifactId>FastInfoset</artifactId>
                <version>${fastinfoset.version}</version>
                <classifier>sources</classifier>
            </dependency>


            <!-- *** BINARIES *** -->
            <!-- new -->
            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>jaxb-runtime</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>jaxb-xjc</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>jaxb-jxc</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>codemodel</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>txw2</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>xsom</artifactId>
                <version>${project.version}</version>
            </dependency>


            <!--OLD-->
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-impl</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-xjc</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-jxc</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- OSGI -->
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-osgi</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- Dependencies -->

            <dependency> <!--JAXB-API-->
                <groupId>jakarta.xml.bind</groupId>
                <artifactId>jakarta.xml.bind-api</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>jakarta.activation</groupId>
                        <artifactId>jakarta.activation-api</artifactId>
                    </exclusion>
                </exclusions>
                <version>${jaxb-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.istack</groupId>
                <artifactId>istack-commons-runtime</artifactId>
                <version>${istack.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.fastinfoset</groupId>
                <artifactId>FastInfoset</artifactId>
                <version>${fastinfoset.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jvnet.staxex</groupId>
                <artifactId>stax-ex</artifactId>
                <version>${stax-ex.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.activation</groupId>
                <artifactId>jakarta.activation-api</artifactId>
                <version>${jakarta.activation.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sun.activation</groupId>
                <artifactId>jakarta.activation</artifactId>
                <version>${jakarta.activation.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.2.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>3.0.0-M3</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.2.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>3.0.0-M1</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
