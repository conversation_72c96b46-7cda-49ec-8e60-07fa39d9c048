<!--
/**
 * 检查项选择 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2023-07-06 17:08:45
 */
 -->
 <!DOCTYPE html>
<html>

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
    <meta name="referrer" content="no-referrer">
    <title th:text="${lang.translate('检查项选择')}">检查项选择</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}">
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden">

<div class="layui-card">

    <div class="layui-card-body" style="">

        <div class="search-bar" style="">

            <div class="search-input-rows" style="opacity: 0">
                <!-- 搜索输入区域 -->
                <div class="layui-form toolbar search-inputs">
                    <!-- 主键 , id ,typeName=text_input, isHideInSearch=true -->
                    <!-- 任务 , taskId ,typeName=select_box, isHideInSearch=true -->
                    <!-- 巡检点位 , taskPointId ,typeName=select_box, isHideInSearch=true -->
                    <!-- 巡检点位 , pointId ,typeName=select_box, isHideInSearch=true -->
                    <!-- 是否检查 , ifCheck ,typeName=radio_box, isHideInSearch=true -->
                    <!-- 组件类型 , type ,typeName=text_input, isHideInSearch=true -->
                    <!-- 检查项 , itemId ,typeName=select_box, isHideInSearch=true -->
                    <!-- 检查项编码 , itemCode ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('检查项编码')}" class="search-label itemCode-label">检查项编码</span><span class="search-colon">:</span></div>
                        <input id="itemCode" class="layui-input search-input" style="width: 140px" type="text" />
                    </div>
                    <!-- 检查结果 , result ,typeName=text_input, isHideInSearch=true -->
                    <!-- 内容元数据 , resultMetaData ,typeName=text_input, isHideInSearch=true -->
                    <!-- 配置项 , config ,typeName=text_area, isHideInSearch=true -->
                    <!-- 默认值 , configDefValue ,typeName=text_input, isHideInSearch=true -->
                    <!-- 排序 , sort ,typeName=number_input, isHideInSearch=true -->
                    <!-- 检查项 , itemName ,typeName=text_input, isHideInSearch=false -->
                    <div class="search-unit">
                        <div class="search-label-div" style=""><span th:text="${lang.translate('检查项')}" class="search-label itemName-label">检查项</span><span class="search-colon">:</span></div>
                        <input id="itemName" class="layui-input search-input" style="width: 140px" type="text" />
                    </div>


                </div>
            </div>


            <!-- 按钮区域 -->
            <div id="search-area" class="layui-form toolbar search-buttons" style="opacity: 0">
                <button id="search-button" class="layui-btn icon-btn"><i class="layui-icon">&#xe615;</i>&nbsp;&nbsp;<span th:text="${lang.translate('搜索','','cmp:table.search')}">搜索</span></button>
            </div>
        </div>

        <div id="table-area" style="margin-top: 42px ">
            <table class="layui-table" id="data-table" lay-filter="data-table"></table>
        </div>

    </div>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>
<!-- 表格工具栏 -->
<script type="text/html" id="toolbarTemplate">
    <div class="layui-btn-container">
<!--        <button th:if="${perm.checkAuth('eam_check_select:create')}" id="add-button" class="layui-btn icon-btn layui-btn-sm create-new-button " lay-event="create"><i class="layui-icon">&#xe654;</i><span th:text="${lang.translate('新建','','cmp:table.button')}">新建</span></button>-->
<!--        <button th:if="${perm.checkAuth('eam_check_select:delete-by-ids')}" id="delete-button" class="layui-btn icon-btn layui-btn-danger layui-btn-sm batch-delete-button " lay-event="batch-del"><i class="layui-icon">&#xe67e;</i><span th:text="${lang.translate('删除','','cmp:table.button')}">删除</span></button>-->
<!--    -->

    </div>
</script>

<!-- 表格操作列 -->
<script type="text/html" id="tableOperationTemplate">

<!--    <button th:if="${perm.checkAuth('eam_check_select:view-form')}" class="layui-btn layui-btn-primary layui-btn-xs ops-view-button " lay-event="view"  data-id="{{d.id}}"> <span th:text="${lang.translate('查看','','cmp:table.ops')}">查看</span></button>-->
<!--    <button th:if="${perm.checkAnyAuth('eam_check_select:update','eam_check_select:save')}" class="layui-btn layui-btn-primary layui-btn-xs ops-edit-button " lay-event="edit"data-id="{{d.id}}"><span th:text="${lang.translate('修改','','cmp:table.ops')}">修改</span></button>-->
<!--    <button th:if="${perm.checkAuth('eam_check_select:delete')}" class="layui-btn layui-btn-xs layui-btn-danger ops-delete-button " lay-event="del" data-id="{{d.id}}"><span th:text="${lang.translate('删除','','cmp:table.ops')}">删除</span></button>-->
</script>


<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${pageHelper.getTableColumnWidthConfig('data-table')}]];
    var RADIO_IFCHECK_DATA = [[${enum.toArray('com.dt.platform.constants.enums.eam.CheckIfCheckEnum')}]];
    var AUTH_PREFIX="eam_check_select_show";
    var TASK_POINT_ID = [[${taskPointId}]];
</script>

<script th:src="'/business/eam/check_select/check_select_show_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/check_select/check_select_show.js?'+${cacheKey}"></script>

</body>
</html>