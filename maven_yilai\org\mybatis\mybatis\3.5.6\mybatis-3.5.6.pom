<?xml version="1.0" encoding="UTF-8"?>
<!--

       Copyright 2009-2020 the original author or authors.

       Licensed under the Apache License, Version 2.0 (the "License");
       you may not use this file except in compliance with the License.
       You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

       Unless required by applicable law or agreed to in writing, software
       distributed under the License is distributed on an "AS IS" BASIS,
       WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
       See the License for the specific language governing permissions and
       limitations under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.mybatis</groupId>
    <artifactId>mybatis-parent</artifactId>
    <version>32</version>
    <relativePath />
  </parent>

  <artifactId>mybatis</artifactId>
  <version>3.5.6</version>
  <packaging>jar</packaging>

  <name>mybatis</name>
  <description>
    The MyBatis SQL mapper framework makes it easier to use a relational database with object-oriented
    applications. MyBatis couples objects with stored procedures or SQL statements using a XML descriptor or
    annotations. Simplicity is the biggest advantage of the MyBatis data mapper over object relational mapping
    tools.
  </description>
  <url>http://www.mybatis.org/mybatis-3</url>

  <inceptionYear>2009</inceptionYear>

  <contributors>
    <contributor>
      <name>Adam Gent</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Andrea Selva</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Antonio Sánchez</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Arkadi Shishlov</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Axel Doerfler</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Chris Dadej</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Denis Vygovskiy</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Franta Mejta</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Jurriaan Pruys</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Keith Wong</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Lasse Voss</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Luke Stevens</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Paul Krause</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Peter Leibiger</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Riccardo Cossu</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Tomáš Neuberg</name>
      <email><EMAIL></email>
    </contributor>
  </contributors>

  <scm>
    <url>http://github.com/mybatis/mybatis-3</url>
    <connection>scm:git:ssh://github.com/mybatis/mybatis-3.git</connection>
    <developerConnection>scm:git:ssh://**************/mybatis/mybatis-3.git</developerConnection>
    <tag>mybatis-3.5.6</tag>
  </scm>
  <issueManagement>
    <system>GitHub Issue Management</system>
    <url>https://github.com/mybatis/mybatis-3/issues</url>
  </issueManagement>
  <ciManagement>
    <system>Travis CI</system>
    <url>https://travis-ci.org/mybatis/mybatis-3/</url>
  </ciManagement>
  <distributionManagement>
    <site>
      <id>gh-pages</id>
      <name>Mybatis GitHub Pages</name>
      <url>git:ssh://**************/mybatis/mybatis-3.git?gh-pages#</url>
    </site>
  </distributionManagement>

  <properties>
    <clirr.comparisonVersion>3.4.6</clirr.comparisonVersion>
    <excludedGroups>TestcontainersTests</excludedGroups>
    <maven.compiler.testCompilerArgument>-parameters</maven.compiler.testCompilerArgument>
    <module.name>org.mybatis</module.name>
    <osgi.export>org.apache.ibatis.*;version=${project.version};-noimport:=true</osgi.export>
    <osgi.import>*;resolution:=optional</osgi.import>
    <osgi.dynamicImport>*</osgi.dynamicImport>
    <spotbugs.onlyAnalyze>org.apache.ibatis.*</spotbugs.onlyAnalyze>
  </properties>

  <dependencies>
    <dependency>
      <groupId>ognl</groupId>
      <artifactId>ognl</artifactId>
      <version>3.2.15</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.javassist</groupId>
      <artifactId>javassist</artifactId>
      <version>3.27.0-GA</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.30</version>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-log4j12</artifactId>
      <version>1.7.30</version>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>log4j</groupId>
      <artifactId>log4j</artifactId>
      <version>1.2.17</version>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-core</artifactId>
      <version>2.13.3</version>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>commons-logging</groupId>
      <artifactId>commons-logging</artifactId>
      <version>1.2</version>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>cglib</groupId>
      <artifactId>cglib</artifactId>
      <version>3.3.0</version>
      <optional>true</optional>
    </dependency>

    <!-- Test dependencies -->
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <version>5.7.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.hsqldb</groupId>
      <artifactId>hsqldb</artifactId>
      <version>2.5.1</version>
      <scope>test</scope>
    </dependency>
    <dependency> <!-- 10.15+ need Java 9+ -->
      <groupId>org.apache.derby</groupId>
      <artifactId>derby</artifactId>
      <version>10.14.2.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.h2database</groupId>
      <artifactId>h2</artifactId>
      <version>1.4.200</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>3.5.13</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-junit-jupiter</artifactId>
      <version>3.5.13</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.velocity</groupId>
      <artifactId>velocity-engine-core</artifactId>
      <version>2.2</version>
      <scope>test</scope>
    </dependency>
    <!-- postgresql driver is required to run the refcursor tests -->
    <dependency>
      <groupId>org.postgresql</groupId>
      <artifactId>postgresql</artifactId>
      <version>42.2.16</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
      <version>8.0.21</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <version>3.17.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>eu.codearte.catch-exception</groupId>
      <artifactId>catch-exception</artifactId>
      <version>2.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>junit-jupiter</artifactId>
      <version>1.14.3</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>postgresql</artifactId>
      <version>1.14.3</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>mysql</artifactId>
      <version>1.14.3</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <testCompilerArgument>${maven.compiler.testCompilerArgument}</testCompilerArgument>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <argLine>${argLine} -Xmx2048m</argLine>
          <systemProperties>
            <property>
              <name>derby.stream.error.file</name>
              <value>${project.build.directory}/derby.log</value>
            </property>
            <property>
              <name>derby.system.home</name>
              <value>${project.build.directory}</value>
            </property>
          </systemProperties>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-pdf-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-shade-plugin</artifactId>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <createDependencyReducedPom>false</createDependencyReducedPom>
              <artifactSet>
                <includes>
                  <include>org.mybatis:mybatis</include>
                  <include>ognl:ognl</include>
                  <include>org.javassist:javassist</include>
                </includes>
              </artifactSet>
              <relocations>
                <relocation>
                  <pattern>ognl</pattern>
                  <shadedPattern>org.apache.ibatis.ognl</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>javassist</pattern>
                  <shadedPattern>org.apache.ibatis.javassist</shadedPattern>
                </relocation>
              </relocations>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-site-plugin</artifactId>
        <configuration>
          <locales>en,es,ja,fr,zh_CN,ko</locales>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <configuration>
          <excludes>
            <exclude>org.apache.ibatis.ognl.*</exclude>
            <exclude>org.apache.ibatis.javassist.*</exclude>
          </excludes>
        </configuration>
      </plugin>
    </plugins>

    <resources>
      <resource>
        <directory>${project.basedir}</directory>
        <targetPath>META-INF</targetPath>
        <includes>
          <include>LICENSE</include>
          <include>NOTICE</include>
        </includes>
      </resource>
      <resource>
        <directory>${project.build.sourceDirectory}</directory>
        <excludes>
          <exclude>**/*.java</exclude>
        </excludes>
      </resource>
    </resources>
    <testResources>
      <testResource>
        <directory>${project.build.testSourceDirectory}</directory>
        <excludes>
          <exclude>**/*.java</exclude>
        </excludes>
      </testResource>
    </testResources>
  </build>

  <profiles>
    <profile>
      <!-- Run slow tests only on travis ci, to force run otherwise use -D"env.TRAVIS" -->
      <id>travis-ci</id>
      <activation>
        <property>
          <name>env.TRAVIS</name>
        </property>
      </activation>
      <properties>
        <excludedGroups />
      </properties>
    </profile>
  </profiles>

  <!-- Will remove after released mybatis-parent 32+ (See https://github.com/mybatis/mybatis-3/issues/1926) -->
  <repositories>
    <repository>
      <id>sonatype-oss-snapshots</id>
      <name>Sonatype OSS Snapshots Repository</name>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
    </repository>
  </repositories>

</project>
