<?xml version="1.0" encoding="UTF-8"?>

<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.maven.doxia</groupId>
    <artifactId>doxia-sitetools</artifactId>
    <version>1.9</version>
    <relativePath>../pom.xml</relativePath>
  </parent>

  <artifactId>doxia-decoration-model</artifactId>

  <name>Doxia Sitetools :: Decoration Model</name>
  <description>The Decoration Model handles the decoration descriptor for sites, also known as site.xml.</description>

  <dependencies>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-component-annotations</artifactId>
    </dependency>
    <dependency>
      <groupId>org.codehaus.plexus</groupId>
      <artifactId>plexus-utils</artifactId>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.modello</groupId>
        <artifactId>modello-maven-plugin</artifactId>
        <configuration>
          <models>
            <model>src/main/mdo/decoration.mdo</model>
          </models>
          <!-- TODO Do not forget to update the version in the decoration description. See DOXIASITETOOLS-98. -->
          <version>1.8.0</version>
          <firstVersion>1.0.0</firstVersion>
        </configuration>
        <executions>
          <execution>
            <id>descriptor</id>
            <phase>generate-sources</phase>
            <goals>
              <goal>xpp3-writer</goal>
              <goal>java</goal>
              <goal>xpp3-reader</goal>
              <goal>xsd</goal>
            </goals>
          </execution>
          <execution>
            <id>descriptor-xdoc</id>
            <phase>pre-site</phase>
            <goals>
              <goal>xdoc</goal>
            </goals>
          </execution>
          <execution>
            <id>descriptor-xsd</id>
            <phase>pre-site</phase>
            <goals>
              <goal>xsd</goal>
            </goals>
            <configuration>
              <outputDirectory>${project.build.directory}/generated-site/resources/xsd</outputDirectory>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
