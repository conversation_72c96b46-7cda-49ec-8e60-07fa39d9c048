<!--
/**
 * 盘点计划 列表页 JS 脚本
 * <AUTHOR> , <EMAIL>
 * @since 2022-01-03 10:30:51
 */
 -->
 <!DOCTYPE html>
<html style="background-color: #FFFFFF;">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta charset="utf-8"/>
	<title th:text="${lang.translate('盘点计划')}">盘点计划</title>
    <link th:if(theme.ico!="null") rel="shortcut icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <link th:if(theme.ico!="null") rel="icon" th:href="${theme.ico}" type="image/vnd.microsoft.icon">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/assets/libs/layui/css/layui.css" th:href="'/assets/libs/layui/css/layui.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/css/admin.css" th:href="'/assets/css/admin.css?'+${cacheKey}"/>
    <link rel="stylesheet" href="/assets/libs/toast/css/toast.css" type="text/css" th:href="'/assets/libs/toast/css/toast.css?'+${cacheKey}">
    <link rel="stylesheet" href="/assets/css/foxnic-web.css" th:href="'/assets/css/foxnic-web.css?'+${cacheKey}"/>
    <link href="/assets/libs/font-awesome-4.7.0/css/font-awesome.min.css" rel="stylesheet"/>
    <link href="/assets/libs/material-design-webfont/css/material-design-icons-min.css" rel="stylesheet">
    <script th:inline="javascript">var foxnic_cachekey=[[${cacheKey}]];</script>
    <style>
    </style>
    <link th:each="css:${theme.css}" rel="stylesheet" th:href="${css}+'?'+${cacheKey}">
</head>

<body style="overflow-y: hidden;">
<!--<div class="form-container" >-->

<!--    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="display:none">-->

<!--        <input name="id" id="id"  type="hidden"/>-->

<!--         &lt;!&ndash;开始：group 循环&ndash;&gt;-->
<!--        <div class="layui-row form-row" id="random-9293-content">-->

<!--             &lt;!&ndash;开始：group 循环&ndash;&gt;-->
<!--            <div class="layui-col-xs6 form-column"  style="padding-top: 15px" >-->

<!--                    <div class="layui-form-item" >-->
<!--                        <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('计划名称')}">计划名称</div></div>-->
<!--                        <div class="layui-input-block layui-input-block-c1">-->
<!--                            <input lay-filter="name" id="name" name="name" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('计划名称') }" type="text" class="layui-input"  />-->
<!--                        </div>-->
<!--                    </div>-->

<!--                                    <div class="layui-form-item" >-->
<!--                        <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('备注')}">备注</div></div>-->
<!--                        <div class="layui-input-block layui-input-block-c1">-->
<!--                            <input lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入') +''+ lang.translate('备注') }" type="text" class="layui-input"  />-->
<!--                        </div>-->
<!--                    </div>-->

<!--                &lt;!&ndash;结束：栏次内字段循环&ndash;&gt;-->
<!--            </div>-->
<!--             &lt;!&ndash;开始：group 循环&ndash;&gt;-->
<!--            <div class="layui-col-xs6 form-column"  style="padding-top: 15px" >-->


<!--                    <div class="layui-form-item" >-->
<!--                        <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('状态')}">状态</div><div class="layui-required">*</div></div>-->
<!--                        <div class="layui-input-block layui-input-block-c1">-->
<!--                            <div id="status" input-type="select" th:data="${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}" extraParam="{}"></div>-->
<!--                        </div>-->
<!--                    </div>-->



<!--                    <div class="layui-form-item" >-->
<!--                        <div class="layui-form-label layui-form-label-c1"><div th:text="${lang.translate('计划类型')}">计划类型</div><div class="layui-required">*</div></div>-->
<!--                        <div class="layui-input-block layui-input-block-c1">-->
<!--                            <div id="planType" input-type="select" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_inventory_plan_type'}" extraParam="{}"></div>-->
<!--                        </div>-->
<!--                    </div>-->


<!--                &lt;!&ndash;结束：栏次内字段循环&ndash;&gt;-->
<!--            </div>-->
<!--            &lt;!&ndash;结束：栏次输入框循环&ndash;&gt;-->
<!--        </div>-->
<!--        &lt;!&ndash;结束：group循环&ndash;&gt;-->

<!--        <div style="height: 8px"></div>-->
<!--        <div style="height: 250px"></div>-->


<!--    </form>-->

<!--</div>-->

<div class="form-container" >

    <form id="data-form" lay-filter="data-form" class="layui-form model-form" style="opacity:0">

        <input name="id" id="id"  type="hidden"/>

        <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-3490-content">

            <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >

                <!-- text_input : 计划名称 ,  name -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('计划名称')}">计划名称</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="name" id="name" name="name" th:placeholder="${ lang.translate('请输入'+'计划名称') }" type="text" class="layui-input"    lay-verify="|required"  />
                    </div>
                </div>

                <!-- radio_box : 状态 ,  status  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('状态')}">状态</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <input input-type="radio" type="radio" name="status" lay-filter="status" th:each="e,stat:${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}" th:value="${e.code}" th:title="${e.text}" th:checked="${(e.code=='' || stat.index==-1)}">
                    </div>
                </div>
                <!--结束：栏次内字段循环-->
            </div>
            <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs6 form-column" >

                <!-- select_box : 计划类型 ,  planType  -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('计划类型')}">计划类型</div><div class="layui-required">*</div></div>
                    <div class="layui-input-block ">
                        <div id="planType" input-type="select" th:data="${'/service-system/sys-dict-item/query-list?dictCode=eam_inventory_plan_type'}" extraParam="{}"></div>
                    </div>
                </div>
                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--开始：group 循环-->


        <div class="layui-row form-row" id="random-6285-content">

            <!--开始：column 循环-->
            <!-- 只有当非第一个分组没有title时才使 padding-top 为 0 -->
            <div class="layui-col-xs12 form-column"  style="padding-top: 0px" >

                <!-- text_input : 备注 ,  notes -->
                <div class="layui-form-item" inlines=""  inline-delta="0" input-width="">
                    <div class="layui-form-label "><div th:text="${lang.translate('备注')}">备注</div></div>
                    <div class="layui-input-block ">
                        <input lay-filter="notes" id="notes" name="notes" th:placeholder="${ lang.translate('请输入'+'备注') }" type="text" class="layui-input"  />
                    </div>
                </div>
                <!--结束：栏次内字段循环-->
            </div>
            <!--结束：栏次输入框循环-->
        </div>
        <!--结束：group循环-->

        <div style="height: 8px"></div>
        <div style="height: 250px"></div>


    </form>

</div>
<div class="model-form-footer">
    <button class="layui-btn layui-btn-primary" id="cancel-button" lay-filter="cancel-button" type="button" th:text="${lang.translate('取消')}" >取消</button>
    <button th:if="${perm.checkAnyAuth('eam_inventory_plan:create','eam_inventory_plan:update','eam_inventory_plan:save')}" class="layui-btn" style="margin-right: 15px;display: none;"  id="submit-button" lay-filter="submit-button" lay-submit th:text="${lang.translate('保存')}">保存</button>
</div>

<script type="text/javascript" src="/module/global.js" th:src="'/module/global.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/jquery-3.2.1.min.js" th:src="'/assets/libs/jquery-3.2.1.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/pandyle.min.js" th:src="'/assets/libs/pandyle.min.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js" th:src="'/assets/libs/layui/layui.js?'+${cacheKey}"></script>
<script type="text/javascript" src="/assets/libs/toast/js/toast.js" th:src="'/assets/libs/toast/js/toast.js?'+${cacheKey}"></script>

<script th:inline="javascript">
    var LAYUI_TABLE_WIDTH_CONFIG = [[${layuiTableWidthConfig}]];
    var SELECT_STATUS_DATA = [[${enum.toArray('com.dt.platform.constants.enums.common.StatusEnableEnum')}]];
  //  var VALIDATE_CONFIG={"planType":{"labelInForm":"计划类型","inputType":"select_box","required":true},"status":{"labelInForm":"状态","inputType":"select_box","required":true}};
    var VALIDATE_CONFIG={"planType":{"labelInForm":"计划类型","inputType":"select_box","required":true},"name":{"labelInForm":"计划名称","inputType":"text_input","required":true},"status":{"labelInForm":"状态","inputType":"radio_box","required":true}};

    var AUTH_PREFIX="eam_inventory_plan";

    // OWNER_CODE
    var OWNER_CODE = [[${ownerCode}]] ;


</script>



<script th:src="'/business/eam/inventory_plan/inventory_plan_ext.js?'+${cacheKey}"></script>
<script th:src="'/business/eam/inventory_plan/inventory_plan_form.js?'+${cacheKey}"></script>

</body>
</html>
